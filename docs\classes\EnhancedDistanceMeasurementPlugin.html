<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>EnhancedDistanceMeasurementPlugin | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="EnhancedDistanceMeasurementPlugin.html">EnhancedDistanceMeasurementPlugin</a></li></ul><h1>Class EnhancedDistanceMeasurementPlugin</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="../modules/math.html" class="tsd-signature-type" data-tsd-kind="Namespace">math</a><ul class="tsd-hierarchy"><li><span class="target">EnhancedDistanceMeasurementPlugin</span></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="EnhancedDistanceMeasurementPlugin.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#_tooltip" class="tsd-kind-icon">_tooltip</a></li></ul></section><section class="tsd-index-section "><h3>Accessors</h3><ul class="tsd-index-list"><li class="tsd-kind-accessor tsd-parent-kind-class"><a href="EnhancedDistanceMeasurementPlugin.html#active" class="tsd-kind-icon">active</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#attachEvents" class="tsd-kind-icon">attach<wbr/>Events</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#changeCursor" class="tsd-kind-icon">change<wbr/>Cursor</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#changeStyle" class="tsd-kind-icon">change<wbr/>Style</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#createTooltip" class="tsd-kind-icon">create<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="EnhancedDistanceMeasurementPlugin.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#destroyEvents" class="tsd-kind-icon">destroy<wbr/>Events</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#hideAllmeasurements" class="tsd-kind-icon">hide<wbr/>Allmeasurements</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#onActive" class="tsd-kind-icon">on<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#onDeactive" class="tsd-kind-icon">on<wbr/>Deactive</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#removeTooltip" class="tsd-kind-icon">remove<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#showAllmeasurements" class="tsd-kind-icon">show<wbr/>Allmeasurements</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Enhanced<wbr/>Distance<wbr/>Measurement<wbr/>Plugin<span class="tsd-signature-symbol">(</span>viewer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, cfg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">EnhancedDistanceMeasurementPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides DistanceMeasurementsPlugin.constructor</p><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:12</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>viewer: <span class="tsd-signature-type">any</span></h5></li><li><h5>cfg: <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> = {}</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">EnhancedDistanceMeasurementPlugin</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_active" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _active</h3><div class="tsd-signature tsd-kind-icon">_active<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:8</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_tooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _tooltip</h3><div class="tsd-signature tsd-kind-icon">_tooltip<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="Tooltip.html" class="tsd-signature-type" data-tsd-kind="Class">Tooltip</a></div><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:9</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Accessors</h2><section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class"><a id="active" class="tsd-anchor"></a><h3>active</h3><ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> active<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> active<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:18</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:22</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="attachEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> attach<wbr/>Events</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">attach<wbr/>Events<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:64</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="changeCursor" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> change<wbr/>Cursor</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">change<wbr/>Cursor<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:45</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="changeStyle" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> change<wbr/>Style</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">change<wbr/>Style<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:49</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Tooltip</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Tooltip<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:69</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:102</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="destroyEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> destroy<wbr/>Events</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">destroy<wbr/>Events<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:96</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="hideAllmeasurements" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> hide<wbr/>Allmeasurements</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">hide<wbr/>Allmeasurements<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:82</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="onActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> on<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">on<wbr/>Active<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:31</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="onDeactive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> on<wbr/>Deactive</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">on<wbr/>Deactive<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:38</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="removeTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> remove<wbr/>Tooltip</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">remove<wbr/>Tooltip<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:77</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="showAllmeasurements" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> show<wbr/>Allmeasurements</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">show<wbr/>Allmeasurements<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/EnhancedDistanceMeasurementPlugin.ts:89</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-kind-icon">Enhanced<wbr/>Distance<wbr/>Measurement<wbr/>Plugin</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="EnhancedDistanceMeasurementPlugin.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#_tooltip" class="tsd-kind-icon">_tooltip</a></li><li class="tsd-kind-accessor tsd-parent-kind-class"><a href="EnhancedDistanceMeasurementPlugin.html#active" class="tsd-kind-icon">active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#attachEvents" class="tsd-kind-icon">attach<wbr/>Events</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#changeCursor" class="tsd-kind-icon">change<wbr/>Cursor</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#changeStyle" class="tsd-kind-icon">change<wbr/>Style</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#createTooltip" class="tsd-kind-icon">create<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="EnhancedDistanceMeasurementPlugin.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#destroyEvents" class="tsd-kind-icon">destroy<wbr/>Events</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#hideAllmeasurements" class="tsd-kind-icon">hide<wbr/>Allmeasurements</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#onActive" class="tsd-kind-icon">on<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#onDeactive" class="tsd-kind-icon">on<wbr/>Deactive</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#removeTooltip" class="tsd-kind-icon">remove<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="EnhancedDistanceMeasurementPlugin.html#showAllmeasurements" class="tsd-kind-icon">show<wbr/>Allmeasurements</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>