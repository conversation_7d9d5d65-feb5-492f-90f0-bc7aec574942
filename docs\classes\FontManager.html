<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>FontManager | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="FontManager.html">FontManager</a></li></ul><h1>Class FontManager</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">FontManager</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#_typeFaceMap" class="tsd-kind-icon">_type<wbr/>Face<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private tsd-is-static"><a href="FontManager.html#_instance" class="tsd-kind-icon">_instance</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#createPath" class="tsd-kind-icon">create<wbr/>Path</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#createPaths" class="tsd-kind-icon">create<wbr/>Paths</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#destroyTypeFaceById" class="tsd-kind-icon">destroy<wbr/>Type<wbr/>Face<wbr/>By<wbr/>Id</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#generateShapes" class="tsd-kind-icon">generate<wbr/>Shapes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#generateTextGeometry" class="tsd-kind-icon">generate<wbr/>Text<wbr/>Geometry</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#havaFontById" class="tsd-kind-icon">hava<wbr/>Font<wbr/>By<wbr/>Id</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#isEmpty" class="tsd-kind-icon">is<wbr/>Empty</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#load" class="tsd-kind-icon">load</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="FontManager.html#instance" class="tsd-kind-icon">instance</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-private"><a id="constructor" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">new <wbr/>Font<wbr/>Manager<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="FontManager.html" class="tsd-signature-type" data-tsd-kind="Class">FontManager</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:52</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="FontManager.html" class="tsd-signature-type" data-tsd-kind="Class">FontManager</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_typeFaceMap" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _type<wbr/>Face<wbr/>Map</h3><div class="tsd-signature tsd-kind-icon">_type<wbr/>Face<wbr/>Map<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Map</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></div><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:49</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private tsd-is-static"><a id="_instance" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagStatic">Static</span> <span class="tsd-flag ts-flagPrivate">Private</span> _instance</h3><div class="tsd-signature tsd-kind-icon">_instance<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="FontManager.html" class="tsd-signature-type" data-tsd-kind="Class">FontManager</a></div><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:50</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createPath" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Path</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Path<span class="tsd-signature-symbol">(</span>char<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, scale<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, offsetX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, offsetY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, data<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">{ </span>offsetX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>path<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a><span class="tsd-signature-symbol"> }</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:196</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>char: <span class="tsd-signature-type">string</span></h5></li><li><h5>scale: <span class="tsd-signature-type">number</span></h5></li><li><h5>offsetX: <span class="tsd-signature-type">number</span></h5></li><li><h5>offsetY: <span class="tsd-signature-type">number</span></h5></li><li><h5>data: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">{ </span>offsetX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">; </span>path<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a><span class="tsd-signature-symbol"> }</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createPaths" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Paths</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Paths<span class="tsd-signature-symbol">(</span>text<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, size<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, fontId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:161</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>text: <span class="tsd-signature-type">string</span></h5></li><li><h5>size: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> fontId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a><span class="tsd-signature-symbol">[]</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:146</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroyTypeFaceById" class="tsd-anchor"></a><h3>destroy<wbr/>Type<wbr/>Face<wbr/>By<wbr/>Id</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<wbr/>Type<wbr/>Face<wbr/>By<wbr/>Id<span class="tsd-signature-symbol">(</span>fontId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:140</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>fontId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="generateShapes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> generate<wbr/>Shapes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">generate<wbr/>Shapes<span class="tsd-signature-symbol">(</span>text<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, size<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span>, fontId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Shape.html" class="tsd-signature-type" data-tsd-kind="Class">Shape</a><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:150</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>text: <span class="tsd-signature-type">string</span></h5></li><li><h5>size: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 100</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> fontId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Shape.html" class="tsd-signature-type" data-tsd-kind="Class">Shape</a><span class="tsd-signature-symbol">[]</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="generateTextGeometry" class="tsd-anchor"></a><h3>generate<wbr/>Text<wbr/>Geometry</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">generate<wbr/>Text<wbr/>Geometry<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><a href="../interfaces/TextGeometryParameter.html" class="tsd-signature-type" data-tsd-kind="Interface">TextGeometryParameter</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">{ </span>indices<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>positions<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:101</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>params: <a href="../interfaces/TextGeometryParameter.html" class="tsd-signature-type" data-tsd-kind="Interface">TextGeometryParameter</a></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">{ </span>indices<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>positions<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="havaFontById" class="tsd-anchor"></a><h3>hava<wbr/>Font<wbr/>By<wbr/>Id</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">hava<wbr/>Font<wbr/>By<wbr/>Id<span class="tsd-signature-symbol">(</span>fontId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:136</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>fontId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="isEmpty" class="tsd-anchor"></a><h3>is<wbr/>Empty</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">is<wbr/>Empty<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:132</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="load" class="tsd-anchor"></a><h3>load</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">load<span class="tsd-signature-symbol">(</span>cfg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">TypeFaceLoadConfig</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:63</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>cfg: <span class="tsd-signature-type">TypeFaceLoadConfig</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">&gt;</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-static"><a id="instance" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagStatic">Static</span> instance</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-static"><li class="tsd-signature tsd-kind-icon">instance<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="FontManager.html" class="tsd-signature-type" data-tsd-kind="Class">FontManager</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in services/FontManager.ts:56</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="FontManager.html" class="tsd-signature-type" data-tsd-kind="Class">FontManager</a></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="FontManager.html" class="tsd-kind-icon">Font<wbr/>Manager</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#_typeFaceMap" class="tsd-kind-icon">_type<wbr/>Face<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private tsd-is-static"><a href="FontManager.html#_instance" class="tsd-kind-icon">_instance</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#createPath" class="tsd-kind-icon">create<wbr/>Path</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#createPaths" class="tsd-kind-icon">create<wbr/>Paths</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#destroyTypeFaceById" class="tsd-kind-icon">destroy<wbr/>Type<wbr/>Face<wbr/>By<wbr/>Id</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="FontManager.html#generateShapes" class="tsd-kind-icon">generate<wbr/>Shapes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#generateTextGeometry" class="tsd-kind-icon">generate<wbr/>Text<wbr/>Geometry</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#havaFontById" class="tsd-kind-icon">hava<wbr/>Font<wbr/>By<wbr/>Id</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#isEmpty" class="tsd-kind-icon">is<wbr/>Empty</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="FontManager.html#load" class="tsd-kind-icon">load</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><a href="FontManager.html#instance" class="tsd-kind-icon">instance</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>