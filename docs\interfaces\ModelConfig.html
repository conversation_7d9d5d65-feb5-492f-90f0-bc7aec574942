<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>ModelConfig | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="ModelConfig.html">ModelConfig</a></li></ul><h1>Interface ModelConfig</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><div class="lead">
<p>Model config</p>
</div></div></section><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">ModelConfig</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#edges" class="tsd-kind-icon">edges</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#name" class="tsd-kind-icon">name</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#performance" class="tsd-kind-icon">performance</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#position" class="tsd-kind-icon">position</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#rotation" class="tsd-kind-icon">rotation</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#scale" class="tsd-kind-icon">scale</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#src" class="tsd-kind-icon">src</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#visible" class="tsd-kind-icon">visible</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="edges" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> edges</h3><div class="tsd-signature tsd-kind-icon">edges<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:23</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="name" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> name</h3><div class="tsd-signature tsd-kind-icon">name<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:18</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="performance" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> performance</h3><div class="tsd-signature tsd-kind-icon">performance<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:25</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="position" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> position</h3><div class="tsd-signature tsd-kind-icon">position<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:20</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="rotation" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> rotation</h3><div class="tsd-signature tsd-kind-icon">rotation<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:21</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="scale" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> scale</h3><div class="tsd-signature tsd-kind-icon">scale<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:22</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="src" class="tsd-anchor"></a><h3>src</h3><div class="tsd-signature tsd-kind-icon">src<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:19</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="visible" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> visible</h3><div class="tsd-signature tsd-kind-icon">visible<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:24</li></ul></aside></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-interface"><a href="ModelConfig.html" class="tsd-kind-icon">Model<wbr/>Config</a><ul><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#edges" class="tsd-kind-icon">edges</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#name" class="tsd-kind-icon">name</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#performance" class="tsd-kind-icon">performance</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#position" class="tsd-kind-icon">position</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#rotation" class="tsd-kind-icon">rotation</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#scale" class="tsd-kind-icon">scale</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#src" class="tsd-kind-icon">src</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="ModelConfig.html#visible" class="tsd-kind-icon">visible</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>