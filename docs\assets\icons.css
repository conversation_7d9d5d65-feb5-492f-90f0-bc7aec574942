.tsd-kind-icon {
    display: block;
    position: relative;
    padding-left: 20px;
    text-indent: -20px;
}
.tsd-kind-icon:before {
    content: "";
    display: inline-block;
    vertical-align: middle;
    width: 17px;
    height: 17px;
    margin: 0 3px 2px 0;
    background-image: url(./icons.png);
}
@media (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 144dpi) {
    .tsd-kind-icon:before {
        background-image: url(./<EMAIL>);
        background-size: 238px 204px;
    }
}

.tsd-signature.tsd-kind-icon:before {
    background-position: 0 -153px;
}

.tsd-kind-object-literal > .tsd-kind-icon:before {
    background-position: 0px -17px;
}
.tsd-kind-object-literal.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -17px;
}
.tsd-kind-object-literal.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -17px;
}

.tsd-kind-class > .tsd-kind-icon:before {
    background-position: 0px -34px;
}
.tsd-kind-class.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -34px;
}
.tsd-kind-class.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -34px;
}

.tsd-kind-class.tsd-has-type-parameter > .tsd-kind-icon:before {
    background-position: 0px -51px;
}
.tsd-kind-class.tsd-has-type-parameter.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -17px -51px;
}
.tsd-kind-class.tsd-has-type-parameter.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -51px;
}

.tsd-kind-interface > .tsd-kind-icon:before {
    background-position: 0px -68px;
}
.tsd-kind-interface.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -68px;
}
.tsd-kind-interface.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -68px;
}

.tsd-kind-interface.tsd-has-type-parameter > .tsd-kind-icon:before {
    background-position: 0px -85px;
}
.tsd-kind-interface.tsd-has-type-parameter.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -17px -85px;
}
.tsd-kind-interface.tsd-has-type-parameter.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -34px -85px;
}

.tsd-kind-namespace > .tsd-kind-icon:before {
    background-position: 0px -102px;
}
.tsd-kind-namespace.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -102px;
}
.tsd-kind-namespace.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -102px;
}

.tsd-kind-module > .tsd-kind-icon:before {
    background-position: 0px -102px;
}
.tsd-kind-module.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -102px;
}
.tsd-kind-module.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -102px;
}

.tsd-kind-enum > .tsd-kind-icon:before {
    background-position: 0px -119px;
}
.tsd-kind-enum.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -119px;
}
.tsd-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -119px;
}

.tsd-kind-enum-member > .tsd-kind-icon:before {
    background-position: 0px -136px;
}
.tsd-kind-enum-member.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -136px;
}
.tsd-kind-enum-member.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -136px;
}

.tsd-kind-signature > .tsd-kind-icon:before {
    background-position: 0px -153px;
}
.tsd-kind-signature.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -153px;
}
.tsd-kind-signature.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -153px;
}

.tsd-kind-type-alias > .tsd-kind-icon:before {
    background-position: 0px -170px;
}
.tsd-kind-type-alias.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -17px -170px;
}
.tsd-kind-type-alias.tsd-is-private > .tsd-kind-icon:before {
    background-position: -34px -170px;
}

.tsd-kind-type-alias.tsd-has-type-parameter > .tsd-kind-icon:before {
    background-position: 0px -187px;
}
.tsd-kind-type-alias.tsd-has-type-parameter.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -17px -187px;
}
.tsd-kind-type-alias.tsd-has-type-parameter.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -34px -187px;
}

.tsd-kind-variable > .tsd-kind-icon:before {
    background-position: -136px -0px;
}
.tsd-kind-variable.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -0px;
}
.tsd-kind-variable.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -0px;
}
.tsd-kind-variable.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -0px;
}
.tsd-kind-variable.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -0px;
}
.tsd-kind-variable.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -0px;
}
.tsd-kind-variable.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -0px;
}
.tsd-kind-variable.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -0px;
}
.tsd-kind-variable.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -0px;
}
.tsd-kind-variable.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -0px;
}
.tsd-kind-variable.tsd-parent-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -0px;
}
.tsd-kind-variable.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -0px;
}
.tsd-kind-variable.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -0px;
}

.tsd-kind-property > .tsd-kind-icon:before {
    background-position: -136px -0px;
}
.tsd-kind-property.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -0px;
}
.tsd-kind-property.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -0px;
}
.tsd-kind-property.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -0px;
}
.tsd-kind-property.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -0px;
}
.tsd-kind-property.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -0px;
}
.tsd-kind-property.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -0px;
}
.tsd-kind-property.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -0px;
}
.tsd-kind-property.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -0px;
}
.tsd-kind-property.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -0px;
}
.tsd-kind-property.tsd-parent-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -0px;
}
.tsd-kind-property.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -0px;
}
.tsd-kind-property.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -0px;
}

.tsd-kind-get-signature > .tsd-kind-icon:before {
    background-position: -136px -17px;
}
.tsd-kind-get-signature.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -17px;
}
.tsd-kind-get-signature.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -17px;
}
.tsd-kind-get-signature.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -17px;
}

.tsd-kind-set-signature > .tsd-kind-icon:before {
    background-position: -136px -34px;
}
.tsd-kind-set-signature.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -34px;
}
.tsd-kind-set-signature.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -34px;
}
.tsd-kind-set-signature.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -34px;
}

.tsd-kind-accessor > .tsd-kind-icon:before {
    background-position: -136px -51px;
}
.tsd-kind-accessor.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -51px;
}
.tsd-kind-accessor.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -51px;
}
.tsd-kind-accessor.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -51px;
}

.tsd-kind-function > .tsd-kind-icon:before {
    background-position: -136px -68px;
}
.tsd-kind-function.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -68px;
}
.tsd-kind-function.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-function.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -68px;
}
.tsd-kind-function.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -68px;
}
.tsd-kind-function.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -68px;
}
.tsd-kind-function.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -68px;
}
.tsd-kind-function.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-function.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -68px;
}
.tsd-kind-function.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -68px;
}
.tsd-kind-function.tsd-parent-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-function.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -68px;
}
.tsd-kind-function.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -68px;
}

.tsd-kind-method > .tsd-kind-icon:before {
    background-position: -136px -68px;
}
.tsd-kind-method.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -68px;
}
.tsd-kind-method.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-method.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -68px;
}
.tsd-kind-method.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -68px;
}
.tsd-kind-method.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -68px;
}
.tsd-kind-method.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -68px;
}
.tsd-kind-method.tsd-parent-kind-class.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-method.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -68px;
}
.tsd-kind-method.tsd-parent-kind-enum.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -187px -68px;
}
.tsd-kind-method.tsd-parent-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-method.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -68px;
}
.tsd-kind-method.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -68px;
}

.tsd-kind-call-signature > .tsd-kind-icon:before {
    background-position: -136px -68px;
}
.tsd-kind-call-signature.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -68px;
}
.tsd-kind-call-signature.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -68px;
}
.tsd-kind-call-signature.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -68px;
}

.tsd-kind-function.tsd-has-type-parameter > .tsd-kind-icon:before {
    background-position: -136px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -153px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-class
    > .tsd-kind-icon:before {
    background-position: -51px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-enum
    > .tsd-kind-icon:before {
    background-position: -170px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-interface
    > .tsd-kind-icon:before {
    background-position: -204px -85px;
}
.tsd-kind-function.tsd-has-type-parameter.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -85px;
}

.tsd-kind-method.tsd-has-type-parameter > .tsd-kind-icon:before {
    background-position: -136px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -153px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-class
    > .tsd-kind-icon:before {
    background-position: -51px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-enum
    > .tsd-kind-icon:before {
    background-position: -170px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-interface
    > .tsd-kind-icon:before {
    background-position: -204px -85px;
}
.tsd-kind-method.tsd-has-type-parameter.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -85px;
}

.tsd-kind-constructor > .tsd-kind-icon:before {
    background-position: -136px -102px;
}
.tsd-kind-constructor.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -102px;
}
.tsd-kind-constructor.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -102px;
}
.tsd-kind-constructor.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -102px;
}

.tsd-kind-constructor-signature > .tsd-kind-icon:before {
    background-position: -136px -102px;
}
.tsd-kind-constructor-signature.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -102px;
}
.tsd-kind-constructor-signature.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-interface
    > .tsd-kind-icon:before {
    background-position: -204px -102px;
}
.tsd-kind-constructor-signature.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -102px;
}

.tsd-kind-index-signature > .tsd-kind-icon:before {
    background-position: -136px -119px;
}
.tsd-kind-index-signature.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -119px;
}
.tsd-kind-index-signature.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -119px;
}
.tsd-kind-index-signature.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -119px;
}

.tsd-kind-event > .tsd-kind-icon:before {
    background-position: -136px -136px;
}
.tsd-kind-event.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -136px;
}
.tsd-kind-event.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -136px;
}
.tsd-kind-event.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -136px;
}
.tsd-kind-event.tsd-parent-kind-class.tsd-is-inherited > .tsd-kind-icon:before {
    background-position: -68px -136px;
}
.tsd-kind-event.tsd-parent-kind-class.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -85px -136px;
}
.tsd-kind-event.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -136px;
}
.tsd-kind-event.tsd-parent-kind-class.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -136px;
}
.tsd-kind-event.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -136px;
}
.tsd-kind-event.tsd-parent-kind-enum.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -187px -136px;
}
.tsd-kind-event.tsd-parent-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -136px;
}
.tsd-kind-event.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -136px;
}
.tsd-kind-event.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -136px;
}

.tsd-is-static > .tsd-kind-icon:before {
    background-position: -136px -153px;
}
.tsd-is-static.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -153px;
}
.tsd-is-static.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -153px;
}
.tsd-is-static.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -153px;
}
.tsd-is-static.tsd-parent-kind-class.tsd-is-inherited > .tsd-kind-icon:before {
    background-position: -68px -153px;
}
.tsd-is-static.tsd-parent-kind-class.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -85px -153px;
}
.tsd-is-static.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -153px;
}
.tsd-is-static.tsd-parent-kind-class.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -153px;
}
.tsd-is-static.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -153px;
}
.tsd-is-static.tsd-parent-kind-enum.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -187px -153px;
}
.tsd-is-static.tsd-parent-kind-enum.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -153px;
}
.tsd-is-static.tsd-parent-kind-interface > .tsd-kind-icon:before {
    background-position: -204px -153px;
}
.tsd-is-static.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -153px;
}

.tsd-is-static.tsd-kind-function > .tsd-kind-icon:before {
    background-position: -136px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-interface
    > .tsd-kind-icon:before {
    background-position: -204px -170px;
}
.tsd-is-static.tsd-kind-function.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -170px;
}

.tsd-is-static.tsd-kind-method > .tsd-kind-icon:before {
    background-position: -136px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-interface
    > .tsd-kind-icon:before {
    background-position: -204px -170px;
}
.tsd-is-static.tsd-kind-method.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -170px;
}

.tsd-is-static.tsd-kind-call-signature > .tsd-kind-icon:before {
    background-position: -136px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -153px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-class
    > .tsd-kind-icon:before {
    background-position: -51px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-enum
    > .tsd-kind-icon:before {
    background-position: -170px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-interface
    > .tsd-kind-icon:before {
    background-position: -204px -170px;
}
.tsd-is-static.tsd-kind-call-signature.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -170px;
}

.tsd-is-static.tsd-kind-event > .tsd-kind-icon:before {
    background-position: -136px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-is-protected > .tsd-kind-icon:before {
    background-position: -153px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-is-private > .tsd-kind-icon:before {
    background-position: -119px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-class > .tsd-kind-icon:before {
    background-position: -51px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-class.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -68px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-class.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -85px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-class.tsd-is-protected.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -102px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-class.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-enum > .tsd-kind-icon:before {
    background-position: -170px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-enum.tsd-is-protected
    > .tsd-kind-icon:before {
    background-position: -187px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-enum.tsd-is-private
    > .tsd-kind-icon:before {
    background-position: -119px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-interface
    > .tsd-kind-icon:before {
    background-position: -204px -187px;
}
.tsd-is-static.tsd-kind-event.tsd-parent-kind-interface.tsd-is-inherited
    > .tsd-kind-icon:before {
    background-position: -221px -187px;
}
