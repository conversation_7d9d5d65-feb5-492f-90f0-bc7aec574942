<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>SceneGraphTreeView | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="SceneGraphTreeView.html">SceneGraphTreeView</a></li></ul><h1>Class SceneGraphTreeView</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">SceneGraphTreeView</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="SceneGraphTreeView.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_autoExpandDepth" class="tsd-kind-icon">_auto<wbr/>Expand<wbr/>Depth</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_baseId" class="tsd-kind-icon">_base<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_checkboxChangeHandler" class="tsd-kind-icon">_checkbox<wbr/>Change<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_containerElement" class="tsd-kind-icon">_container<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_id" class="tsd-kind-icon">_id</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_isPerformance" class="tsd-kind-icon">_is<wbr/>Performance</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_muteSceneEvents" class="tsd-kind-icon">_mute<wbr/>Scene<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_muteTreeEvents" class="tsd-kind-icon">_mute<wbr/>Tree<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_objectNodes" class="tsd-kind-icon">_object<wbr/>Nodes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_onObjectVisibility" class="tsd-kind-icon">_on<wbr/>Object<wbr/>Visibility</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootElement" class="tsd-kind-icon">_root<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootModel" class="tsd-kind-icon">_root<wbr/>Model</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootName" class="tsd-kind-icon">_root<wbr/>Name</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootNodes" class="tsd-kind-icon">_root<wbr/>Nodes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_showListItemElementId" class="tsd-kind-icon">_show<wbr/>List<wbr/>Item<wbr/>Element<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_switchCollapseHandler" class="tsd-kind-icon">_switch<wbr/>Collapse<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_switchExpandHandler" class="tsd-kind-icon">_switch<wbr/>Expand<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_treeViewPlugin" class="tsd-kind-icon">_tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_viewer" class="tsd-kind-icon">_viewer</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_collapseSwitchElement" class="tsd-kind-icon">_collapse<wbr/>Switch<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createContainmentNodes" class="tsd-kind-icon">_create<wbr/>Containment<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createEnabledNodes" class="tsd-kind-icon">_create<wbr/>Enabled<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createNodeElement" class="tsd-kind-icon">_create<wbr/>Node<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createNodes" class="tsd-kind-icon">_create<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createTrees" class="tsd-kind-icon">_create<wbr/>Trees</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_expandSwitchElement" class="tsd-kind-icon">_expand<wbr/>Switch<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_nodeToObjectID" class="tsd-kind-icon">_node<wbr/>To<wbr/>ObjectID</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_objectToNodeID" class="tsd-kind-icon">_object<wbr/>To<wbr/>NodeID</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_performanceSynchNodesToEntities" class="tsd-kind-icon">_performance<wbr/>Synch<wbr/>Nodes<wbr/>To<wbr/>Entities</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_synchNodesToEntities" class="tsd-kind-icon">_synch<wbr/>Nodes<wbr/>To<wbr/>Entities</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_withNodeTree" class="tsd-kind-icon">_with<wbr/>Node<wbr/>Tree</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#expandToDepth" class="tsd-kind-icon">expand<wbr/>To<wbr/>Depth</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#showNode" class="tsd-kind-icon">show<wbr/>Node</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#unShowNode" class="tsd-kind-icon">un<wbr/>Show<wbr/>Node</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">new <wbr/>Scene<wbr/>Graph<wbr/>Tree<wbr/>View<span class="tsd-signature-symbol">(</span>viewer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, treeViewPlugin<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, cfg<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">SceneGraphTreeViewConfig</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SceneGraphTreeView.html" class="tsd-signature-type" data-tsd-kind="Class">SceneGraphTreeView</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:52</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>viewer: <span class="tsd-signature-type">any</span></h5></li><li><h5>treeViewPlugin: <span class="tsd-signature-type">any</span></h5></li><li><h5>model: <span class="tsd-signature-type">any</span></h5></li><li><h5>cfg: <span class="tsd-signature-type">SceneGraphTreeViewConfig</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="SceneGraphTreeView.html" class="tsd-signature-type" data-tsd-kind="Class">SceneGraphTreeView</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_autoExpandDepth" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _auto<wbr/>Expand<wbr/>Depth</h3><div class="tsd-signature tsd-kind-icon">_auto<wbr/>Expand<wbr/>Depth<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:47</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_baseId" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _base<wbr/>Id</h3><div class="tsd-signature tsd-kind-icon">_base<wbr/>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:37</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_checkboxChangeHandler" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _checkbox<wbr/>Change<wbr/>Handler</h3><div class="tsd-signature tsd-kind-icon">_checkbox<wbr/>Change<wbr/>Handler<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ElementCallbackReturnVoidType</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:33</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_containerElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _container<wbr/>Element</h3><div class="tsd-signature tsd-kind-icon">_container<wbr/>Element<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:40</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_id" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _id</h3><div class="tsd-signature tsd-kind-icon">_id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:36</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_isPerformance" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _is<wbr/>Performance</h3><div class="tsd-signature tsd-kind-icon">_is<wbr/>Performance<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:49</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_muteSceneEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _mute<wbr/>Scene<wbr/>Events</h3><div class="tsd-signature tsd-kind-icon">_mute<wbr/>Scene<wbr/>Events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:42</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_muteTreeEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _mute<wbr/>Tree<wbr/>Events</h3><div class="tsd-signature tsd-kind-icon">_mute<wbr/>Tree<wbr/>Events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:43</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_objectNodes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _object<wbr/>Nodes</h3><div class="tsd-signature tsd-kind-icon">_object<wbr/>Nodes<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:45</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_onObjectVisibility" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _on<wbr/>Object<wbr/>Visibility</h3><div class="tsd-signature tsd-kind-icon">_on<wbr/>Object<wbr/>Visibility<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:34</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_rootElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _root<wbr/>Element</h3><div class="tsd-signature tsd-kind-icon">_root<wbr/>Element<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:41</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_rootModel" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _root<wbr/>Model</h3><div class="tsd-signature tsd-kind-icon">_root<wbr/>Model<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:50</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_rootName" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _root<wbr/>Name</h3><div class="tsd-signature tsd-kind-icon">_root<wbr/>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:46</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_rootNodes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _root<wbr/>Nodes</h3><div class="tsd-signature tsd-kind-icon">_root<wbr/>Nodes<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">TreeViewNode</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:44</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_showListItemElementId" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _show<wbr/>List<wbr/>Item<wbr/>Element<wbr/>Id</h3><div class="tsd-signature tsd-kind-icon">_show<wbr/>List<wbr/>Item<wbr/>Element<wbr/>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:35</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_switchCollapseHandler" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _switch<wbr/>Collapse<wbr/>Handler</h3><div class="tsd-signature tsd-kind-icon">_switch<wbr/>Collapse<wbr/>Handler<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ElementCallbackReturnVoidType</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:30</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_switchExpandHandler" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _switch<wbr/>Expand<wbr/>Handler</h3><div class="tsd-signature tsd-kind-icon">_switch<wbr/>Expand<wbr/>Handler<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">ElementCallbackReturnVoidType</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:31</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_treeViewPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _tree<wbr/>View<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_tree<wbr/>View<wbr/>Plugin<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:39</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_viewer" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _viewer</h3><div class="tsd-signature tsd-kind-icon">_viewer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:38</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_collapseSwitchElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _collapse<wbr/>Switch<wbr/>Element</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_collapse<wbr/>Switch<wbr/>Element<span class="tsd-signature-symbol">(</span>switchElement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventTarget</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:258</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>switchElement: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventTarget</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_createContainmentNodes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _create<wbr/>Containment<wbr/>Nodes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_create<wbr/>Containment<wbr/>Nodes<span class="tsd-signature-symbol">(</span>metaObject<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, parent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">TreeViewNode</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:375</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>metaObject: <span class="tsd-signature-type">any</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> parent: <span class="tsd-signature-type">TreeViewNode</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_createEnabledNodes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _create<wbr/>Enabled<wbr/>Nodes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_create<wbr/>Enabled<wbr/>Nodes<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:359</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_createNodeElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _create<wbr/>Node<wbr/>Element</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_create<wbr/>Node<wbr/>Element<span class="tsd-signature-symbol">(</span>node<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">TreeViewNode</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:313</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>node: <span class="tsd-signature-type">TreeViewNode</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">HTMLElement</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_createNodes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _create<wbr/>Nodes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_create<wbr/>Nodes<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:279</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_createTrees" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _create<wbr/>Trees</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_create<wbr/>Trees<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:536</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_expandSwitchElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _expand<wbr/>Switch<wbr/>Element</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_expand<wbr/>Switch<wbr/>Element<span class="tsd-signature-symbol">(</span>switchElement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventTarget</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:229</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>switchElement: <span class="tsd-signature-type">null</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">EventTarget</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_nodeToObjectID" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _node<wbr/>To<wbr/>ObjectID</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_node<wbr/>To<wbr/>ObjectID<span class="tsd-signature-symbol">(</span>nodeId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:294</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>nodeId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_objectToNodeID" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _object<wbr/>To<wbr/>NodeID</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_object<wbr/>To<wbr/>NodeID<span class="tsd-signature-symbol">(</span>objectId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:298</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>objectId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_performanceSynchNodesToEntities" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _performance<wbr/>Synch<wbr/>Nodes<wbr/>To<wbr/>Entities</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_performance<wbr/>Synch<wbr/>Nodes<wbr/>To<wbr/>Entities<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:499</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_synchNodesToEntities" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _synch<wbr/>Nodes<wbr/>To<wbr/>Entities</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_synch<wbr/>Nodes<wbr/>To<wbr/>Entities<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:461</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_withNodeTree" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _with<wbr/>Node<wbr/>Tree</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_with<wbr/>Node<wbr/>Tree<span class="tsd-signature-symbol">(</span>node<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">TreeViewNode</span>, callbackFun<span class="tsd-signature-symbol">: </span><a href="../modules.html#CallbackRetureVoidType" class="tsd-signature-type" data-tsd-kind="Type alias">CallbackRetureVoidType</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">TreeViewNode</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:302</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>node: <span class="tsd-signature-type">TreeViewNode</span></h5></li><li><h5>callbackFun: <a href="../modules.html#CallbackRetureVoidType" class="tsd-signature-type" data-tsd-kind="Type alias">CallbackRetureVoidType</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">TreeViewNode</span><span class="tsd-signature-symbol">&gt;</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:605</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="expandToDepth" class="tsd-anchor"></a><h3>expand<wbr/>To<wbr/>Depth</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">expand<wbr/>To<wbr/>Depth<span class="tsd-signature-symbol">(</span>depth<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:206</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>depth: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="showNode" class="tsd-anchor"></a><h3>show<wbr/>Node</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">show<wbr/>Node<span class="tsd-signature-symbol">(</span>objectId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:551</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>objectId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="unShowNode" class="tsd-anchor"></a><h3>un<wbr/>Show<wbr/>Node</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">un<wbr/>Show<wbr/>Node<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/SceneGraphTreeView.ts:592</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="SceneGraphTreeView.html" class="tsd-kind-icon">Scene<wbr/>Graph<wbr/>Tree<wbr/>View</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="SceneGraphTreeView.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_autoExpandDepth" class="tsd-kind-icon">_auto<wbr/>Expand<wbr/>Depth</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_baseId" class="tsd-kind-icon">_base<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_checkboxChangeHandler" class="tsd-kind-icon">_checkbox<wbr/>Change<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_containerElement" class="tsd-kind-icon">_container<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_id" class="tsd-kind-icon">_id</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_isPerformance" class="tsd-kind-icon">_is<wbr/>Performance</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_muteSceneEvents" class="tsd-kind-icon">_mute<wbr/>Scene<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_muteTreeEvents" class="tsd-kind-icon">_mute<wbr/>Tree<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_objectNodes" class="tsd-kind-icon">_object<wbr/>Nodes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_onObjectVisibility" class="tsd-kind-icon">_on<wbr/>Object<wbr/>Visibility</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootElement" class="tsd-kind-icon">_root<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootModel" class="tsd-kind-icon">_root<wbr/>Model</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootName" class="tsd-kind-icon">_root<wbr/>Name</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_rootNodes" class="tsd-kind-icon">_root<wbr/>Nodes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_showListItemElementId" class="tsd-kind-icon">_show<wbr/>List<wbr/>Item<wbr/>Element<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_switchCollapseHandler" class="tsd-kind-icon">_switch<wbr/>Collapse<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_switchExpandHandler" class="tsd-kind-icon">_switch<wbr/>Expand<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_treeViewPlugin" class="tsd-kind-icon">_tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_viewer" class="tsd-kind-icon">_viewer</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_collapseSwitchElement" class="tsd-kind-icon">_collapse<wbr/>Switch<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createContainmentNodes" class="tsd-kind-icon">_create<wbr/>Containment<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createEnabledNodes" class="tsd-kind-icon">_create<wbr/>Enabled<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createNodeElement" class="tsd-kind-icon">_create<wbr/>Node<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createNodes" class="tsd-kind-icon">_create<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_createTrees" class="tsd-kind-icon">_create<wbr/>Trees</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_expandSwitchElement" class="tsd-kind-icon">_expand<wbr/>Switch<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_nodeToObjectID" class="tsd-kind-icon">_node<wbr/>To<wbr/>ObjectID</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_objectToNodeID" class="tsd-kind-icon">_object<wbr/>To<wbr/>NodeID</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_performanceSynchNodesToEntities" class="tsd-kind-icon">_performance<wbr/>Synch<wbr/>Nodes<wbr/>To<wbr/>Entities</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_synchNodesToEntities" class="tsd-kind-icon">_synch<wbr/>Nodes<wbr/>To<wbr/>Entities</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SceneGraphTreeView.html#_withNodeTree" class="tsd-kind-icon">_with<wbr/>Node<wbr/>Tree</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#expandToDepth" class="tsd-kind-icon">expand<wbr/>To<wbr/>Depth</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#showNode" class="tsd-kind-icon">show<wbr/>Node</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SceneGraphTreeView.html#unShowNode" class="tsd-kind-icon">un<wbr/>Show<wbr/>Node</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>