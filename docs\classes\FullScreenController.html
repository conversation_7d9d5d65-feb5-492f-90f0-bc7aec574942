<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>FullScreenController | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="FullScreenController.html">FullScreenController</a></li></ul><h1>Class FullScreenController</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><div class="lead">
<p>FullScreenController</p>
</div></div></section><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="ToolbarMenuBaseController.html" class="tsd-signature-type" data-tsd-kind="Class">ToolbarMenuBaseController</a><ul class="tsd-hierarchy"><li><span class="target">FullScreenController</span></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="FullScreenController.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-inherited"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_element" class="tsd-kind-icon">_element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_enabled" class="tsd-kind-icon">_enabled</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_eventCallDepth" class="tsd-kind-icon">_event<wbr/>Call<wbr/>Depth</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_eventSubs" class="tsd-kind-icon">_event<wbr/>Subs</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_events" class="tsd-kind-icon">_events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_subIdEvents" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_subIdMap" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#bimViewer" class="tsd-kind-icon">bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#children" class="tsd-kind-icon">children</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#destroyed" class="tsd-kind-icon">destroyed</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#parent" class="tsd-kind-icon">parent</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#server" class="tsd-kind-icon">server</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#viewer" class="tsd-kind-icon">viewer</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#error" class="tsd-kind-icon">error</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#fire" class="tsd-kind-icon">fire</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#getActive" class="tsd-kind-icon">get<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#getEnabled" class="tsd-kind-icon">get<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#log" class="tsd-kind-icon">log</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#mutexActivation" class="tsd-kind-icon">mutex<wbr/>Activation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#off" class="tsd-kind-icon">off</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#on" class="tsd-kind-icon">on</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#onActive" class="tsd-kind-icon">on<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite tsd-is-protected"><a href="FullScreenController.html#onClick" class="tsd-kind-icon">on<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#once" class="tsd-kind-icon">once</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#setActive" class="tsd-kind-icon">set<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#setEnabled" class="tsd-kind-icon">set<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#warn" class="tsd-kind-icon">warn</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Full<wbr/>Screen<wbr/>Controller<span class="tsd-signature-symbol">(</span>parent<span class="tsd-signature-symbol">: </span><a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a>, cfg<span class="tsd-signature-symbol">: </span><a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a>, node<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="FullScreenController.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenController</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#constructor">constructor</a></p><ul><li>Defined in widgets/toolbar/FullScreenController.ts:9</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>parent: <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a></h5></li><li><h5>cfg: <a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a></h5></li><li><h5>node: <span class="tsd-signature-type">HTMLElement</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="FullScreenController.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenController</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-inherited"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_active" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _active</h3><div class="tsd-signature tsd-kind-icon">_active<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_active">_active</a></p><ul><li>Defined in core/Controller.ts:20</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_element" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _element</h3><div class="tsd-signature tsd-kind-icon">_element<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_element">_element</a></p><ul><li>Defined in widgets/toolbar/ToolbarMenuBaseController.ts:13</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_enabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _enabled</h3><div class="tsd-signature tsd-kind-icon">_enabled<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_enabled">_enabled</a></p><ul><li>Defined in core/Controller.ts:19</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_eventCallDepth" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _event<wbr/>Call<wbr/>Depth</h3><div class="tsd-signature tsd-kind-icon">_event<wbr/>Call<wbr/>Depth<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 0</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_eventCallDepth">_eventCallDepth</a></p><ul><li>Defined in core/Controller.ts:18</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_eventSubs" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _event<wbr/>Subs</h3><div class="tsd-signature tsd-kind-icon">_event<wbr/>Subs<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_eventSubs">_eventSubs</a></p><ul><li>Defined in core/Controller.ts:16</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_events" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _events</h3><div class="tsd-signature tsd-kind-icon">_events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_events">_events</a></p><ul><li>Defined in core/Controller.ts:17</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_subIdEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _sub<wbr/>Id<wbr/>Events</h3><div class="tsd-signature tsd-kind-icon">_sub<wbr/>Id<wbr/>Events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_subIdEvents">_subIdEvents</a></p><ul><li>Defined in core/Controller.ts:15</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_subIdMap" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _sub<wbr/>Id<wbr/>Map</h3><div class="tsd-signature tsd-kind-icon">_sub<wbr/>Id<wbr/>Map<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="Map.html" class="tsd-signature-type" data-tsd-kind="Class">Map</a></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#_subIdMap">_subIdMap</a></p><ul><li>Defined in core/Controller.ts:14</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="bimViewer" class="tsd-anchor"></a><h3>bim<wbr/>Viewer</h3><div class="tsd-signature tsd-kind-icon">bim<wbr/>Viewer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#bimViewer">bimViewer</a></p><ul><li>Defined in core/Controller.ts:8</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="children" class="tsd-anchor"></a><h3>children</h3><div class="tsd-signature tsd-kind-icon">children<span class="tsd-signature-symbol">:</span> <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#children">children</a></p><ul><li>Defined in core/Controller.ts:13</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="destroyed" class="tsd-anchor"></a><h3>destroyed</h3><div class="tsd-signature tsd-kind-icon">destroyed<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#destroyed">destroyed</a></p><ul><li>Defined in core/Controller.ts:11</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="parent" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> parent</h3><div class="tsd-signature tsd-kind-icon">parent<span class="tsd-signature-symbol">?:</span> <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#parent">parent</a></p><ul><li>Defined in core/Controller.ts:12</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="server" class="tsd-anchor"></a><h3>server</h3><div class="tsd-signature tsd-kind-icon">server<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#server">server</a></p><ul><li>Defined in core/Controller.ts:9</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="viewer" class="tsd-anchor"></a><h3>viewer</h3><div class="tsd-signature tsd-kind-icon">viewer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#viewer">viewer</a></p><ul><li>Defined in core/Controller.ts:10</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="destroy" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#destroy">destroy</a></p><ul><li>Defined in core/Controller.ts:298</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Destroys this Controller.</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="error" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> error</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">error<span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#error">error</a></p><ul><li>Defined in core/Controller.ts:208</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Logs an error for this Controller to the JavaScript console.</p>
</div><div><p>The console message will have this format: <em><code>[ERROR] [&lt;component type&gt; =&lt;component id&gt;: &lt;message&gt;</code></em></p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>message: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The message to log</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="fire" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> fire</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">fire<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, forget<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#fire">fire</a></p><ul><li>Defined in core/Controller.ts:54</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Fires an event on this Controller.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The event type name</p>
</div></div></li><li><h5>value: <span class="tsd-signature-type">any</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The event parameters</p>
</div></div></li><li><h5>forget: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="getActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> get<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">get<wbr/>Active<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#getActive">getActive</a></p><ul><li>Defined in core/Controller.ts:288</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Gets whether or not this Controller is active.</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4><div></div></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="getEnabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> get<wbr/>Enabled</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">get<wbr/>Enabled<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#getEnabled">getEnabled</a></p><ul><li>Defined in core/Controller.ts:260</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Gets whether or not this Controller is enabled.</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4><div></div></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="log" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> log</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">log<span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#log">log</a></p><ul><li>Defined in core/Controller.ts:180</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Logs a console debugging message for this Controller.</p>
</div><div><p>The console message will have this format: <em><code>[LOG] [&lt;component type&gt; &lt;component id&gt;: &lt;message&gt;</code></em></p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>message: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The message to log</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="mutexActivation" class="tsd-anchor"></a><h3>mutex<wbr/>Activation</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">mutex<wbr/>Activation<span class="tsd-signature-symbol">(</span>controllers<span class="tsd-signature-symbol">: </span><a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#mutexActivation">mutexActivation</a></p><ul><li>Defined in core/Controller.ts:213</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>controllers: <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="off" class="tsd-anchor"></a><h3>off</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">off<span class="tsd-signature-symbol">(</span>subId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#off">off</a></p><ul><li>Defined in core/Controller.ts:130</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Cancels an event subscription that was previously made with {@link Controller#on} or {@link Controller#once}.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> subId: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Subscription ID</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="on" class="tsd-anchor"></a><h3>on</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">on<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, scope<span class="tsd-signature-symbol">?: </span><a href="FullScreenController.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenController</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#on">on</a></p><ul><li>Defined in core/Controller.ts:93</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Subscribes to an event on this Controller.</p>
</div><div><p>The callback is be called with this component as scope.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The event</p>
</div></div></li><li><h5>callback: <span class="tsd-signature-type">any</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Called fired on the event</p>
</div></div></li><li><h5>scope: <a href="FullScreenController.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenController</a><span class="tsd-signature-symbol"> = ...</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4><div><p>Handle to the subscription, which may be used to unsubscribe with {@link #off}.</p>
</div></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="onActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> on<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">on<wbr/>Active<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#onActive">onActive</a></p><ul><li>Defined in widgets/toolbar/ToolbarMenuBaseController.ts:33</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Default onActive behavior</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-overwrite tsd-is-protected"><a id="onClick" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> on<wbr/>Click</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-overwrite tsd-is-protected"><li class="tsd-signature tsd-kind-icon">on<wbr/>Click<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Event</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#onClick">onClick</a></p><ul><li>Defined in widgets/toolbar/FullScreenController.ts:17</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Default onClick behavior</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">Event</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="once" class="tsd-anchor"></a><h3>once</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, scope<span class="tsd-signature-symbol">?: </span><a href="FullScreenController.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenController</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#once">once</a></p><ul><li>Defined in core/Controller.ts:159</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Subscribes to the next occurrence of the given event, then un-subscribes as soon as the event is handled.</p>
</div><div><p>This is equivalent to calling {@link Controller#on}, and then calling {@link Controller#off} inside the callback function.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Data event to listen to</p>
</div></div></li><li><h5>callback: <span class="tsd-signature-type">any</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Called when fresh data is available at the event</p>
</div></div></li><li><h5>scope: <a href="FullScreenController.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenController</a><span class="tsd-signature-symbol"> = ...</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="setActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> set<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">set<wbr/>Active<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#setActive">setActive</a></p><ul><li>Defined in core/Controller.ts:273</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Activates or deactivates this Controller.</p>
</div><div><p>Fires an &quot;active&quot; event on update.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Whether or not to activate.</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="setEnabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> set<wbr/>Enabled</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">set<wbr/>Enabled<span class="tsd-signature-symbol">(</span>enabled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#setEnabled">setEnabled</a></p><ul><li>Defined in core/Controller.ts:245</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Enables or disables this Controller.</p>
</div><div><p>Fires an &quot;enabled&quot; event on update.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>enabled: <span class="tsd-signature-type">boolean</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Whether or not to enable.</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="warn" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> warn</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">warn<span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="ToolbarMenuBaseController.html">ToolbarMenuBaseController</a>.<a href="ToolbarMenuBaseController.html#warn">warn</a></p><ul><li>Defined in core/Controller.ts:194</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Logs a warning for this Controller to the JavaScript console.</p>
</div><div><p>The console message will have this format: <em><code>[WARN] [&lt;component type&gt; =&lt;component id&gt;: &lt;message&gt;</code></em></p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>message: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The message to log</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="FullScreenController.html" class="tsd-kind-icon">Full<wbr/>Screen<wbr/>Controller</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="FullScreenController.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_element" class="tsd-kind-icon">_element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_enabled" class="tsd-kind-icon">_enabled</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_eventCallDepth" class="tsd-kind-icon">_event<wbr/>Call<wbr/>Depth</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_eventSubs" class="tsd-kind-icon">_event<wbr/>Subs</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_events" class="tsd-kind-icon">_events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_subIdEvents" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#_subIdMap" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#bimViewer" class="tsd-kind-icon">bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#children" class="tsd-kind-icon">children</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#destroyed" class="tsd-kind-icon">destroyed</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#parent" class="tsd-kind-icon">parent</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#server" class="tsd-kind-icon">server</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#viewer" class="tsd-kind-icon">viewer</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#error" class="tsd-kind-icon">error</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#fire" class="tsd-kind-icon">fire</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#getActive" class="tsd-kind-icon">get<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#getEnabled" class="tsd-kind-icon">get<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#log" class="tsd-kind-icon">log</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#mutexActivation" class="tsd-kind-icon">mutex<wbr/>Activation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#off" class="tsd-kind-icon">off</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#on" class="tsd-kind-icon">on</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#onActive" class="tsd-kind-icon">on<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite tsd-is-protected"><a href="FullScreenController.html#onClick" class="tsd-kind-icon">on<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="FullScreenController.html#once" class="tsd-kind-icon">once</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#setActive" class="tsd-kind-icon">set<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#setEnabled" class="tsd-kind-icon">set<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="FullScreenController.html#warn" class="tsd-kind-icon">warn</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>