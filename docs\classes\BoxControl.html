<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>BoxControl | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="BoxControl.html">BoxControl</a></li></ul><h1>Class BoxControl</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">BoxControl</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="BoxControl.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_cameraControlSubIds" class="tsd-kind-icon">_camera<wbr/>Control<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_currentAABB" class="tsd-kind-icon">_currentAABB</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_hideableMeshes" class="tsd-kind-icon">_hideable<wbr/>Meshes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_inputSubIds" class="tsd-kind-icon">_input<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_meshes" class="tsd-kind-icon">_meshes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_originAABB" class="tsd-kind-icon">_originAABB</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_rootNode" class="tsd-kind-icon">_root<wbr/>Node</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_sceneSubIds" class="tsd-kind-icon">_scene<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_sectionPlaneMap" class="tsd-kind-icon">_section<wbr/>Plane<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_viewer" class="tsd-kind-icon">_viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_visible" class="tsd-kind-icon">_visible</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#bindEvents" class="tsd-kind-icon">bind<wbr/>Events</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#createMeshes" class="tsd-kind-icon">create<wbr/>Meshes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#createNodes" class="tsd-kind-icon">create<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#destroyNodes" class="tsd-kind-icon">destroy<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#getVisible" class="tsd-kind-icon">get<wbr/>Visible</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#initSectionPlanes" class="tsd-kind-icon">init<wbr/>Section<wbr/>Planes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#rebuildBoxMesh" class="tsd-kind-icon">rebuild<wbr/>Box<wbr/>Mesh</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#setBoxMeshPosition" class="tsd-kind-icon">set<wbr/>Box<wbr/>Mesh<wbr/>Position</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#setCulled" class="tsd-kind-icon">set<wbr/>Culled</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#setVisible" class="tsd-kind-icon">set<wbr/>Visible</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#unbindEvents" class="tsd-kind-icon">unbind<wbr/>Events</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">new <wbr/>Box<wbr/>Control<span class="tsd-signature-symbol">(</span>owner<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="BoxControl.html" class="tsd-signature-type" data-tsd-kind="Class">BoxControl</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:65</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>owner: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="BoxControl.html" class="tsd-signature-type" data-tsd-kind="Class">BoxControl</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_cameraControlSubIds" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _camera<wbr/>Control<wbr/>Sub<wbr/>Ids</h3><div class="tsd-signature tsd-kind-icon">_camera<wbr/>Control<wbr/>Sub<wbr/>Ids<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:62</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_currentAABB" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _currentAABB</h3><div class="tsd-signature tsd-kind-icon">_currentAABB<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:58</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_hideableMeshes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _hideable<wbr/>Meshes</h3><div class="tsd-signature tsd-kind-icon">_hideable<wbr/>Meshes<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Map</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:56</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_inputSubIds" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _input<wbr/>Sub<wbr/>Ids</h3><div class="tsd-signature tsd-kind-icon">_input<wbr/>Sub<wbr/>Ids<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:63</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_meshes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _meshes</h3><div class="tsd-signature tsd-kind-icon">_meshes<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Map</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:55</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_originAABB" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _originAABB</h3><div class="tsd-signature tsd-kind-icon">_originAABB<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:57</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_rootNode" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _root<wbr/>Node</h3><div class="tsd-signature tsd-kind-icon">_root<wbr/>Node<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:54</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_sceneSubIds" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _scene<wbr/>Sub<wbr/>Ids</h3><div class="tsd-signature tsd-kind-icon">_scene<wbr/>Sub<wbr/>Ids<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:61</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_sectionPlaneMap" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _section<wbr/>Plane<wbr/>Map</h3><div class="tsd-signature tsd-kind-icon">_section<wbr/>Plane<wbr/>Map<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">Map</span><span class="tsd-signature-symbol">&lt;</span><a href="../enums/BoxSectionPlaneType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">BoxSectionPlaneType</a><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:59</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_viewer" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _viewer</h3><div class="tsd-signature tsd-kind-icon">_viewer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:52</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_visible" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _visible</h3><div class="tsd-signature tsd-kind-icon">_visible<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:53</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="bindEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> bind<wbr/>Events</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">bind<wbr/>Events<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:406</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createMeshes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Meshes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Meshes<span class="tsd-signature-symbol">(</span>shapes<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span>, materials<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:176</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>shapes: <span class="tsd-signature-symbol">{}</span></h5><ul class="tsd-parameters"><li class="tsd-parameter-index-signature"><h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">any</span></h5></li></ul></li><li><h5>materials: <span class="tsd-signature-symbol">{}</span></h5><ul class="tsd-parameters"><li class="tsd-parameter-index-signature"><h5><span class="tsd-signature-symbol">[</span>key: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-type">any</span></h5></li></ul></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createNodes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Nodes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Nodes<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:328</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Builds the Entities that represent this control.</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:933</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="destroyNodes" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> destroy<wbr/>Nodes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">destroy<wbr/>Nodes<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:951</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="getVisible" class="tsd-anchor"></a><h3>get<wbr/>Visible</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">get<wbr/>Visible<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:164</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="initSectionPlanes" class="tsd-anchor"></a><h3>init<wbr/>Section<wbr/>Planes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">init<wbr/>Section<wbr/>Planes<span class="tsd-signature-symbol">(</span>sectionPlaneMap<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Map</span><span class="tsd-signature-symbol">&lt;</span><a href="../enums/BoxSectionPlaneType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">BoxSectionPlaneType</a><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span>, aabb<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:72</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>sectionPlaneMap: <span class="tsd-signature-type">Map</span><span class="tsd-signature-symbol">&lt;</span><a href="../enums/BoxSectionPlaneType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">BoxSectionPlaneType</a><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">&gt;</span></h5></li><li><h5>aabb: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="rebuildBoxMesh" class="tsd-anchor"></a><h3>rebuild<wbr/>Box<wbr/>Mesh</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">rebuild<wbr/>Box<wbr/>Mesh<span class="tsd-signature-symbol">(</span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:82</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>value: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="setBoxMeshPosition" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> set<wbr/>Box<wbr/>Mesh<wbr/>Position</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">set<wbr/>Box<wbr/>Mesh<wbr/>Position<span class="tsd-signature-symbol">(</span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span>, changePlaneMeshDirection<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:95</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>value: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5></li><li><h5>changePlaneMeshDirection: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setCulled" class="tsd-anchor"></a><h3>set<wbr/>Culled</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Culled<span class="tsd-signature-symbol">(</span>culled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:172</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Sets if this Control is culled. This is called by SectionPlanesPlugin to
temporarily hide the Control while a snapshot is being taken by Viewer#getSnapshot().</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>culled: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setVisible" class="tsd-anchor"></a><h3>set<wbr/>Visible</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Visible<span class="tsd-signature-symbol">(</span>visible<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:155</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>visible: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="unbindEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> unbind<wbr/>Events</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">unbind<wbr/>Events<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/section/BoxControl.ts:938</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="BoxControl.html" class="tsd-kind-icon">Box<wbr/>Control</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="BoxControl.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_cameraControlSubIds" class="tsd-kind-icon">_camera<wbr/>Control<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_currentAABB" class="tsd-kind-icon">_currentAABB</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_hideableMeshes" class="tsd-kind-icon">_hideable<wbr/>Meshes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_inputSubIds" class="tsd-kind-icon">_input<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_meshes" class="tsd-kind-icon">_meshes</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_originAABB" class="tsd-kind-icon">_originAABB</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_rootNode" class="tsd-kind-icon">_root<wbr/>Node</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_sceneSubIds" class="tsd-kind-icon">_scene<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_sectionPlaneMap" class="tsd-kind-icon">_section<wbr/>Plane<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_viewer" class="tsd-kind-icon">_viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#_visible" class="tsd-kind-icon">_visible</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#bindEvents" class="tsd-kind-icon">bind<wbr/>Events</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#createMeshes" class="tsd-kind-icon">create<wbr/>Meshes</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#createNodes" class="tsd-kind-icon">create<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#destroyNodes" class="tsd-kind-icon">destroy<wbr/>Nodes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#getVisible" class="tsd-kind-icon">get<wbr/>Visible</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#initSectionPlanes" class="tsd-kind-icon">init<wbr/>Section<wbr/>Planes</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#rebuildBoxMesh" class="tsd-kind-icon">rebuild<wbr/>Box<wbr/>Mesh</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#setBoxMeshPosition" class="tsd-kind-icon">set<wbr/>Box<wbr/>Mesh<wbr/>Position</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#setCulled" class="tsd-kind-icon">set<wbr/>Culled</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BoxControl.html#setVisible" class="tsd-kind-icon">set<wbr/>Visible</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BoxControl.html#unbindEvents" class="tsd-kind-icon">unbind<wbr/>Events</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>