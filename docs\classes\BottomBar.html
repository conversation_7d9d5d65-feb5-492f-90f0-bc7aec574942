<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>BottomBar | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="BottomBar.html">BottomBar</a></li></ul><h1>Class BottomBar</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">BottomBar</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="BottomBar.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_bimViewer" class="tsd-kind-icon">_bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_cameraInfo" class="tsd-kind-icon">_camera<wbr/>Info</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_cameraInfoTooltip" class="tsd-kind-icon">_camera<wbr/>Info<wbr/>Tooltip</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_fps" class="tsd-kind-icon">_fps</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_location" class="tsd-kind-icon">_location</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_statistics" class="tsd-kind-icon">_statistics</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_statisticsTooltip" class="tsd-kind-icon">_statistics<wbr/>Tooltip</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_subIds" class="tsd-kind-icon">_sub<wbr/>Ids</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#changeIconStyle" class="tsd-kind-icon">change<wbr/>Icon<wbr/>Style</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#createBottomBar" class="tsd-kind-icon">create<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#createBottomBarItem" class="tsd-kind-icon">create<wbr/>Bottom<wbr/>Bar<wbr/>Item</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BottomBar.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#removeBottomBar" class="tsd-kind-icon">remove<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#removeTooltip" class="tsd-kind-icon">remove<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#toggleActiveCameraInfo" class="tsd-kind-icon">toggle<wbr/>Active<wbr/>Camera<wbr/>Info</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#toggleActivePickLocation" class="tsd-kind-icon">toggle<wbr/>Active<wbr/>Pick<wbr/>Location</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#toggleActiveStatistics" class="tsd-kind-icon">toggle<wbr/>Active<wbr/>Statistics</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateCameraInfo" class="tsd-kind-icon">update<wbr/>Camera<wbr/>Info</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateFps" class="tsd-kind-icon">update<wbr/>Fps</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateMouseLocation" class="tsd-kind-icon">update<wbr/>Mouse<wbr/>Location</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateStatistics" class="tsd-kind-icon">update<wbr/>Statistics</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">new <wbr/>Bottom<wbr/>Bar<span class="tsd-signature-symbol">(</span>bimViewer<span class="tsd-signature-symbol">: </span><a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="BottomBar.html" class="tsd-signature-type" data-tsd-kind="Class">BottomBar</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:27</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>bimViewer: <a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="BottomBar.html" class="tsd-signature-type" data-tsd-kind="Class">BottomBar</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_bimViewer" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _bim<wbr/>Viewer</h3><div class="tsd-signature tsd-kind-icon">_bim<wbr/>Viewer<span class="tsd-signature-symbol">?:</span> <a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:18</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_cameraInfo" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _camera<wbr/>Info</h3><div class="tsd-signature tsd-kind-icon">_camera<wbr/>Info<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">BottomBarItem</span></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:22</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_cameraInfoTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _camera<wbr/>Info<wbr/>Tooltip</h3><div class="tsd-signature tsd-kind-icon">_camera<wbr/>Info<wbr/>Tooltip<span class="tsd-signature-symbol">:</span> <a href="Tooltip.html" class="tsd-signature-type" data-tsd-kind="Class">Tooltip</a></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:24</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_fps" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _fps</h3><div class="tsd-signature tsd-kind-icon">_fps<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">BottomBarItem</span></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:20</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_location" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _location</h3><div class="tsd-signature tsd-kind-icon">_location<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">BottomBarItem</span></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:23</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_statistics" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _statistics</h3><div class="tsd-signature tsd-kind-icon">_statistics<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">BottomBarItem</span></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:21</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_statisticsTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _statistics<wbr/>Tooltip</h3><div class="tsd-signature tsd-kind-icon">_statistics<wbr/>Tooltip<span class="tsd-signature-symbol">:</span> <a href="Tooltip.html" class="tsd-signature-type" data-tsd-kind="Class">Tooltip</a></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:25</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_subIds" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _sub<wbr/>Ids</h3><div class="tsd-signature tsd-kind-icon">_sub<wbr/>Ids<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>camera<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>input<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:19</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5>camera<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5></li><li class="tsd-parameter"><h5>input<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5></li><li class="tsd-parameter"><h5>scene<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h5></li></ul></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="changeIconStyle" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> change<wbr/>Icon<wbr/>Style</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">change<wbr/>Icon<wbr/>Style<span class="tsd-signature-symbol">(</span>item<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">BottomBarItem</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:71</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>item: <span class="tsd-signature-type">BottomBarItem</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createBottomBar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Bottom<wbr/>Bar</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Bottom<wbr/>Bar<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:75</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createBottomBarItem" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Bottom<wbr/>Bar<wbr/>Item</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Bottom<wbr/>Bar<wbr/>Item<span class="tsd-signature-symbol">(</span>iconClass<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span>, clickHandler<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLSpanElement</span><span class="tsd-signature-symbol">; </span>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span><span class="tsd-signature-symbol"> }</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:58</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> iconClass: <span class="tsd-signature-type">string</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> clickHandler: <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5><ul class="tsd-parameters"><li class="tsd-parameter-signature"><ul class="tsd-signatures tsd-kind-type-literal"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></li></ul></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLSpanElement</span><span class="tsd-signature-symbol">; </span>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span><span class="tsd-signature-symbol"> }</span></h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></h5></li><li class="tsd-parameter"><h5>content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLSpanElement</span></h5></li><li class="tsd-parameter"><h5>icon<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span></h5></li></ul></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:207</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="removeBottomBar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> remove<wbr/>Bottom<wbr/>Bar</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">remove<wbr/>Bottom<wbr/>Bar<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:220</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="removeTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> remove<wbr/>Tooltip</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">remove<wbr/>Tooltip<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:224</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="toggleActiveCameraInfo" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> toggle<wbr/>Active<wbr/>Camera<wbr/>Info</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">toggle<wbr/>Active<wbr/>Camera<wbr/>Info<span class="tsd-signature-symbol">(</span>enable<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:139</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> enable: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="toggleActivePickLocation" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> toggle<wbr/>Active<wbr/>Pick<wbr/>Location</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">toggle<wbr/>Active<wbr/>Pick<wbr/>Location<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:192</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="toggleActiveStatistics" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> toggle<wbr/>Active<wbr/>Statistics</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">toggle<wbr/>Active<wbr/>Statistics<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:92</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="updateCameraInfo" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> update<wbr/>Camera<wbr/>Info</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">update<wbr/>Camera<wbr/>Info<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:146</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="updateFps" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> update<wbr/>Fps</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">update<wbr/>Fps<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>tickEvent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:164</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">(</span>tickEvent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h4><ul class="tsd-parameters"><li class="tsd-parameter-signature"><ul class="tsd-signatures tsd-kind-type-literal"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>tickEvent<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>tickEvent: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></li></ul></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="updateMouseLocation" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> update<wbr/>Mouse<wbr/>Location</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">update<wbr/>Mouse<wbr/>Location<span class="tsd-signature-symbol">(</span>canvasPos<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:198</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>canvasPos: <span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="updateStatistics" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> update<wbr/>Statistics</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">update<wbr/>Statistics<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/BottomBar.ts:105</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="BottomBar.html" class="tsd-kind-icon">Bottom<wbr/>Bar</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="BottomBar.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_bimViewer" class="tsd-kind-icon">_bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_cameraInfo" class="tsd-kind-icon">_camera<wbr/>Info</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_cameraInfoTooltip" class="tsd-kind-icon">_camera<wbr/>Info<wbr/>Tooltip</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_fps" class="tsd-kind-icon">_fps</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_location" class="tsd-kind-icon">_location</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_statistics" class="tsd-kind-icon">_statistics</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_statisticsTooltip" class="tsd-kind-icon">_statistics<wbr/>Tooltip</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#_subIds" class="tsd-kind-icon">_sub<wbr/>Ids</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#changeIconStyle" class="tsd-kind-icon">change<wbr/>Icon<wbr/>Style</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#createBottomBar" class="tsd-kind-icon">create<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#createBottomBarItem" class="tsd-kind-icon">create<wbr/>Bottom<wbr/>Bar<wbr/>Item</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BottomBar.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#removeBottomBar" class="tsd-kind-icon">remove<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#removeTooltip" class="tsd-kind-icon">remove<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#toggleActiveCameraInfo" class="tsd-kind-icon">toggle<wbr/>Active<wbr/>Camera<wbr/>Info</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#toggleActivePickLocation" class="tsd-kind-icon">toggle<wbr/>Active<wbr/>Pick<wbr/>Location</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#toggleActiveStatistics" class="tsd-kind-icon">toggle<wbr/>Active<wbr/>Statistics</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateCameraInfo" class="tsd-kind-icon">update<wbr/>Camera<wbr/>Info</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateFps" class="tsd-kind-icon">update<wbr/>Fps</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateMouseLocation" class="tsd-kind-icon">update<wbr/>Mouse<wbr/>Location</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BottomBar.html#updateStatistics" class="tsd-kind-icon">update<wbr/>Statistics</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>