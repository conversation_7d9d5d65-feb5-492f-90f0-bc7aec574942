.xeokit-context-menu {
  font-family: "Roboto", sans-serif;
  font-size: 15px;
  display: none;
  z-index: 2;
  background: rgba(255, 255, 255, 0.46);
  border: 5px solid black;
  border-radius: 6px;
  padding: 0;
  width: 220px;
  ul {
      list-style: none;
      margin-left: 0;
      margin-top: 0;
      margin-bottom: 0;
      padding: 0;
  }
  ul li {
      list-style-type: none;
      padding-left: 10px;
      padding-right: 20px;
      padding-top: 6px;
      padding-bottom: 6px;
      color: black;
      border-bottom: 1px solid gray;
      background: rgba(255, 255, 255, 0.46);
      cursor: pointer;
      width: calc(100% - 1px);
  }
  ul li:hover {
      background: black;
      color: white;
      font-weight: bold;
  }
  ul li span {
      display: inline-block;
  }
  .disabled {
      display: inline-block;
      color: gray;
      cursor: default;
      font-weight: normal;
  }
  .disabled:hover {
      color: gray;
      cursor: default;
      background: #eeeeee;
      font-weight: normal;
  }
  .xeokit-context-submenu {
      font-family: "Roboto", sans-serif;
      font-size: 15px;
      display: none;
      z-index: 2;
      background: rgba(255, 255, 255, 0.46);
      border: 1px solid black;
      border-radius: 0 6px 6px 6px !important;
      padding: 0;
      width: 200px;
  }
}
