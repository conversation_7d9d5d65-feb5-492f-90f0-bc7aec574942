<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>SectionPlanePopPanel | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="SectionPlanePopPanel.html">SectionPlanePopPanel</a></li></ul><h1>Class SectionPlanePopPanel</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="PopPanel.html" class="tsd-signature-type" data-tsd-kind="Class">PopPanel</a><ul class="tsd-hierarchy"><li><span class="target">SectionPlanePopPanel</span></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="SectionPlanePopPanel.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section "><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_activeItem" class="tsd-kind-icon">_active<wbr/>Item</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_activeSelectConfig" class="tsd-kind-icon">_active<wbr/>Select<wbr/>Config</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_activeSelectNode" class="tsd-kind-icon">_active<wbr/>Select<wbr/>Node</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#_body" class="tsd-kind-icon">_body</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_groupSelectConfig" class="tsd-kind-icon">_group<wbr/>Select<wbr/>Config</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_groupSelectNode" class="tsd-kind-icon">_group<wbr/>Select<wbr/>Node</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#_header" class="tsd-kind-icon">_header</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_isDisable" class="tsd-kind-icon">_is<wbr/>Disable</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#addActiveSelectItems" class="tsd-kind-icon">add<wbr/>Active<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#addGroupSelectItems" class="tsd-kind-icon">add<wbr/>Group<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#creatGroupSelectLayout" class="tsd-kind-icon">creat<wbr/>Group<wbr/>Select<wbr/>Layout</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#createActiveSelectLayout" class="tsd-kind-icon">create<wbr/>Active<wbr/>Select<wbr/>Layout</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="SectionPlanePopPanel.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#disableActiveSelectItems" class="tsd-kind-icon">disable<wbr/>Active<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#enableActiveSelectItems" class="tsd-kind-icon">enable<wbr/>Active<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#follow" class="tsd-kind-icon">follow</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#onActiveSelectClick" class="tsd-kind-icon">on<wbr/>Active<wbr/>Select<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#onGroupSelectClick" class="tsd-kind-icon">on<wbr/>Group<wbr/>Select<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#setActiveSelectItem" class="tsd-kind-icon">set<wbr/>Active<wbr/>Select<wbr/>Item</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#start" class="tsd-kind-icon">start</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#stop" class="tsd-kind-icon">stop</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Section<wbr/>Plane<wbr/>Pop<wbr/>Panel<span class="tsd-signature-symbol">(</span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, content<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">HTMLElement</span>, config<span class="tsd-signature-symbol">: </span><a href="../interfaces/SectionPlanePopPanelConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SectionPlanePopPanel.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePopPanel</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="PopPanel.html">PopPanel</a>.<a href="PopPanel.html#constructor">constructor</a></p><ul><li>Defined in components/SectionPlanePopPanel.ts:55</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>id: <span class="tsd-signature-type">string</span></h5></li><li><h5>content: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">HTMLElement</span></h5></li><li><h5>config: <a href="../interfaces/SectionPlanePopPanelConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelConfig</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="SectionPlanePopPanel.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePopPanel</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group "><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_activeItem" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _active<wbr/>Item</h3><div class="tsd-signature tsd-kind-icon">_active<wbr/>Item<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:48</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_activeSelectConfig" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _active<wbr/>Select<wbr/>Config</h3><div class="tsd-signature tsd-kind-icon">_active<wbr/>Select<wbr/>Config<span class="tsd-signature-symbol">:</span> <a href="../interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelItemConfig</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:50</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_activeSelectNode" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _active<wbr/>Select<wbr/>Node</h3><div class="tsd-signature tsd-kind-icon">_active<wbr/>Select<wbr/>Node<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:49</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="_body" class="tsd-anchor"></a><h3>_body</h3><div class="tsd-signature tsd-kind-icon">_body<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><p>Inherited from <a href="PopPanel.html">PopPanel</a>.<a href="PopPanel.html#_body">_body</a></p><ul><li>Defined in components/PopPanel.ts:6</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_groupSelectConfig" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _group<wbr/>Select<wbr/>Config</h3><div class="tsd-signature tsd-kind-icon">_group<wbr/>Select<wbr/>Config<span class="tsd-signature-symbol">:</span> <a href="../interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelItemConfig</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:52</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_groupSelectNode" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _group<wbr/>Select<wbr/>Node</h3><div class="tsd-signature tsd-kind-icon">_group<wbr/>Select<wbr/>Node<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:51</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="_header" class="tsd-anchor"></a><h3>_header</h3><div class="tsd-signature tsd-kind-icon">_header<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><p>Inherited from <a href="PopPanel.html">PopPanel</a>.<a href="PopPanel.html#_header">_header</a></p><ul><li>Defined in components/PopPanel.ts:5</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_isDisable" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _is<wbr/>Disable</h3><div class="tsd-signature tsd-kind-icon">_is<wbr/>Disable<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:53</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="addActiveSelectItems" class="tsd-anchor"></a><h3>add<wbr/>Active<wbr/>Select<wbr/>Items</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">add<wbr/>Active<wbr/>Select<wbr/>Items<span class="tsd-signature-symbol">(</span>activeConfig<span class="tsd-signature-symbol">: </span><a href="../interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelItemConfig</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:212</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>activeConfig: <a href="../interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelItemConfig</a><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="addGroupSelectItems" class="tsd-anchor"></a><h3>add<wbr/>Group<wbr/>Select<wbr/>Items</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">add<wbr/>Group<wbr/>Select<wbr/>Items<span class="tsd-signature-symbol">(</span>groupConfig<span class="tsd-signature-symbol">: </span><a href="../interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelItemConfig</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:131</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>groupConfig: <a href="../interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">SectionPlanePopPanelItemConfig</a><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="creatGroupSelectLayout" class="tsd-anchor"></a><h3>creat<wbr/>Group<wbr/>Select<wbr/>Layout</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">creat<wbr/>Group<wbr/>Select<wbr/>Layout<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:73</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="createActiveSelectLayout" class="tsd-anchor"></a><h3>create<wbr/>Active<wbr/>Select<wbr/>Layout</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">create<wbr/>Active<wbr/>Select<wbr/>Layout<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:153</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="PopPanel.html">PopPanel</a>.<a href="PopPanel.html#destroy">destroy</a></p><ul><li>Defined in components/SectionPlanePopPanel.ts:266</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="disableActiveSelectItems" class="tsd-anchor"></a><h3>disable<wbr/>Active<wbr/>Select<wbr/>Items</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">disable<wbr/>Active<wbr/>Select<wbr/>Items<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:252</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="enableActiveSelectItems" class="tsd-anchor"></a><h3>enable<wbr/>Active<wbr/>Select<wbr/>Items</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">enable<wbr/>Active<wbr/>Select<wbr/>Items<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:238</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="follow" class="tsd-anchor"></a><h3>follow</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">follow<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">MouseEvent</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="PopPanel.html">PopPanel</a>.<a href="PopPanel.html#follow">follow</a></p><ul><li>Defined in components/PopPanel.ts:43</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">MouseEvent</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="onActiveSelectClick" class="tsd-anchor"></a><h3>on<wbr/>Active<wbr/>Select<wbr/>Click</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">on<wbr/>Active<wbr/>Select<wbr/>Click<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:160</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="onGroupSelectClick" class="tsd-anchor"></a><h3>on<wbr/>Group<wbr/>Select<wbr/>Click</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">on<wbr/>Group<wbr/>Select<wbr/>Click<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:80</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setActiveSelectItem" class="tsd-anchor"></a><h3>set<wbr/>Active<wbr/>Select<wbr/>Item</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Active<wbr/>Select<wbr/>Item<span class="tsd-signature-symbol">(</span>index<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:221</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>index: <span class="tsd-signature-type">number</span></h5></li><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="start" class="tsd-anchor"></a><h3>start</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">start<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">MouseEvent</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="PopPanel.html">PopPanel</a>.<a href="PopPanel.html#start">start</a></p><ul><li>Defined in components/PopPanel.ts:33</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">MouseEvent</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="stop" class="tsd-anchor"></a><h3>stop</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">stop<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="PopPanel.html">PopPanel</a>.<a href="PopPanel.html#stop">stop</a></p><ul><li>Defined in components/PopPanel.ts:39</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="SectionPlanePopPanel.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="SectionPlanePopPanel.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_activeItem" class="tsd-kind-icon">_active<wbr/>Item</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_activeSelectConfig" class="tsd-kind-icon">_active<wbr/>Select<wbr/>Config</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_activeSelectNode" class="tsd-kind-icon">_active<wbr/>Select<wbr/>Node</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#_body" class="tsd-kind-icon">_body</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_groupSelectConfig" class="tsd-kind-icon">_group<wbr/>Select<wbr/>Config</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_groupSelectNode" class="tsd-kind-icon">_group<wbr/>Select<wbr/>Node</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#_header" class="tsd-kind-icon">_header</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePopPanel.html#_isDisable" class="tsd-kind-icon">_is<wbr/>Disable</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#addActiveSelectItems" class="tsd-kind-icon">add<wbr/>Active<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#addGroupSelectItems" class="tsd-kind-icon">add<wbr/>Group<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#creatGroupSelectLayout" class="tsd-kind-icon">creat<wbr/>Group<wbr/>Select<wbr/>Layout</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#createActiveSelectLayout" class="tsd-kind-icon">create<wbr/>Active<wbr/>Select<wbr/>Layout</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="SectionPlanePopPanel.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#disableActiveSelectItems" class="tsd-kind-icon">disable<wbr/>Active<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#enableActiveSelectItems" class="tsd-kind-icon">enable<wbr/>Active<wbr/>Select<wbr/>Items</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#follow" class="tsd-kind-icon">follow</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#onActiveSelectClick" class="tsd-kind-icon">on<wbr/>Active<wbr/>Select<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#onGroupSelectClick" class="tsd-kind-icon">on<wbr/>Group<wbr/>Select<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePopPanel.html#setActiveSelectItem" class="tsd-kind-icon">set<wbr/>Active<wbr/>Select<wbr/>Item</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#start" class="tsd-kind-icon">start</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="SectionPlanePopPanel.html#stop" class="tsd-kind-icon">stop</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>