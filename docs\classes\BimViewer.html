<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Bim<PERSON>iewer | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="BimViewer.html">BimViewer</a></li></ul><h1>Class BimViewer</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><div class="lead">
<p>BimViewer class</p>
</div></div></section><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a><ul class="tsd-hierarchy"><li><span class="target">BimViewer</span></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="BimViewer.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section "><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_annotationsPlugin" class="tsd-kind-icon">_annotations<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_axisGizmoPlugin" class="tsd-kind-icon">_axis<wbr/>Gizmo<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_axisSectionPlanePlugin" class="tsd-kind-icon">_axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_backgroundColorPlugin" class="tsd-kind-icon">_background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_bcfViewpointsPlugin" class="tsd-kind-icon">_bcf<wbr/>Viewpoints<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_bimViewerCfg" class="tsd-kind-icon">_bim<wbr/>Viewer<wbr/>Cfg</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_bottomBar" class="tsd-kind-icon">_bottom<wbr/>Bar</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_componentPropertyPlugin" class="tsd-kind-icon">_component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_contextMenu" class="tsd-kind-icon">_context<wbr/>Menu</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_distanceMeasurementsPlugin" class="tsd-kind-icon">_distance<wbr/>Measurements<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_enabled" class="tsd-kind-icon">_enabled</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_eventCallDepth" class="tsd-kind-icon">_event<wbr/>Call<wbr/>Depth</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_eventSubs" class="tsd-kind-icon">_event<wbr/>Subs</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_events" class="tsd-kind-icon">_events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_fastNavPlugin" class="tsd-kind-icon">_fast<wbr/>Nav<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_fullScreenPlugin" class="tsd-kind-icon">_full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_girdPlugin" class="tsd-kind-icon">_gird<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_homeView" class="tsd-kind-icon">_home<wbr/>View</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_loaders" class="tsd-kind-icon">_loaders</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_localeService" class="tsd-kind-icon">_locale<wbr/>Service</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_navControlCfg" class="tsd-kind-icon">_nav<wbr/>Control<wbr/>Cfg</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_navCubePlugin" class="tsd-kind-icon">_nav<wbr/>Cube<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_orthoModePlugin" class="tsd-kind-icon">_ortho<wbr/>Mode<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_planViewPlugin" class="tsd-kind-icon">_plan<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_rootHtmlElement" class="tsd-kind-icon">_root<wbr/>Html<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_rootStyleElement" class="tsd-kind-icon">_root<wbr/>Style<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_sectionBoxPlugin" class="tsd-kind-icon">_section<wbr/>Box<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_sectionPlanePlugin" class="tsd-kind-icon">_section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_selectionSuppressCount" class="tsd-kind-icon">_selection<wbr/>Suppress<wbr/>Count</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_singleSelectionPlugin" class="tsd-kind-icon">_single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_skybox" class="tsd-kind-icon">_skybox</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_subIdEvents" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_subIdMap" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_threeDModelCount" class="tsd-kind-icon">_threeDModel<wbr/>Count</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_toolbar" class="tsd-kind-icon">_toolbar</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_treeViewPlugin" class="tsd-kind-icon">_tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_twoDModelCount" class="tsd-kind-icon">_twoDModel<wbr/>Count</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#bimViewer" class="tsd-kind-icon">bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#children" class="tsd-kind-icon">children</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#destroyed" class="tsd-kind-icon">destroyed</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#parent" class="tsd-kind-icon">parent</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#server" class="tsd-kind-icon">server</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#viewer" class="tsd-kind-icon">viewer</a></li></ul></section><section class="tsd-index-section "><h3>Accessors</h3><ul class="tsd-index-list"><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#annotationsPlugin" class="tsd-kind-icon">annotations<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#axisSectionPlanePlugin" class="tsd-kind-icon">axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#backgroundColorPlugin" class="tsd-kind-icon">background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#bcfViewpointsPlugin" class="tsd-kind-icon">bcf<wbr/>Viewpoints<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#componentPropertyPlugin" class="tsd-kind-icon">component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#distanceMeasurementsPlugin" class="tsd-kind-icon">distance<wbr/>Measurements<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#fastNavPlugin" class="tsd-kind-icon">fast<wbr/>Nav<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#fullScreenPlugin" class="tsd-kind-icon">full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#girdPlugin" class="tsd-kind-icon">gird<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#has2dModel" class="tsd-kind-icon">has2d<wbr/>Model</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#has3dModel" class="tsd-kind-icon">has3d<wbr/>Model</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#navCubePlugin" class="tsd-kind-icon">nav<wbr/>Cube<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#orthoModePlugin" class="tsd-kind-icon">ortho<wbr/>Mode<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#planViewPlugin" class="tsd-kind-icon">plan<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#rootHtmlElement" class="tsd-kind-icon">root<wbr/>Html<wbr/>Element</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#rootStyleElement" class="tsd-kind-icon">root<wbr/>Style<wbr/>Element</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#sectionBoxPlugin" class="tsd-kind-icon">section<wbr/>Box<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#sectionPlanePlugin" class="tsd-kind-icon">section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#singleSelectionPlugin" class="tsd-kind-icon">single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#skybox" class="tsd-kind-icon">skybox</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#toolbar" class="tsd-kind-icon">toolbar</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#treeViewPlugin" class="tsd-kind-icon">tree<wbr/>View<wbr/>Plugin</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#active2dMode" class="tsd-kind-icon">active2d<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeAnnotation" class="tsd-kind-icon">active<wbr/>Annotation</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeAxisSectionPlane" class="tsd-kind-icon">active<wbr/>Axis<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeBimTree" class="tsd-kind-icon">active<wbr/>Bim<wbr/>Tree</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeDistanceMeasurement" class="tsd-kind-icon">active<wbr/>Distance<wbr/>Measurement</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeFullScreen" class="tsd-kind-icon">active<wbr/>Full<wbr/>Screen</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeOrthoMode" class="tsd-kind-icon">active<wbr/>Ortho<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeProperty" class="tsd-kind-icon">active<wbr/>Property</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSectionBox" class="tsd-kind-icon">active<wbr/>Section<wbr/>Box</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSectionPlane" class="tsd-kind-icon">active<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSetting" class="tsd-kind-icon">active<wbr/>Setting</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSingleSelection" class="tsd-kind-icon">active<wbr/>Single<wbr/>Selection</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeViewpoint" class="tsd-kind-icon">active<wbr/>Viewpoint</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#appendHtmlElement" class="tsd-kind-icon">append<wbr/>Html<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#clearAnnotations" class="tsd-kind-icon">clear<wbr/>Annotations</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#createAnnotation" class="tsd-kind-icon">create<wbr/>Annotation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="BimViewer.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#destroyAnnotation" class="tsd-kind-icon">destroy<wbr/>Annotation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#error" class="tsd-kind-icon">error</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#fire" class="tsd-kind-icon">fire</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#getActive" class="tsd-kind-icon">get<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#getCanvasImageDataUrl" class="tsd-kind-icon">get<wbr/>Canvas<wbr/>Image<wbr/>Data<wbr/>Url</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#getEnabled" class="tsd-kind-icon">get<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#getLoader" class="tsd-kind-icon">get<wbr/>Loader</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#getNavValueByName" class="tsd-kind-icon">get<wbr/>Nav<wbr/>Value<wbr/>By<wbr/>Name</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#getUniqulModelId" class="tsd-kind-icon">get<wbr/>Uniqul<wbr/>Model<wbr/>Id</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#goToHomeView" class="tsd-kind-icon">go<wbr/>To<wbr/>Home<wbr/>View</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initAxisGizmoPlugin" class="tsd-kind-icon">init<wbr/>Axis<wbr/>Gizmo<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initAxisSectionPlanePlugin" class="tsd-kind-icon">init<wbr/>Axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initBackgroundColorPlugin" class="tsd-kind-icon">init<wbr/>Background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initBottomBar" class="tsd-kind-icon">init<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initComponentPropertyPlugin" class="tsd-kind-icon">init<wbr/>Component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initContextMenu" class="tsd-kind-icon">init<wbr/>Context<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initDistanceMeasurementsPlugin" class="tsd-kind-icon">init<wbr/>Distance<wbr/>Measurements<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initFastNavPlugin" class="tsd-kind-icon">init<wbr/>Fast<wbr/>Nav<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initFullScreenPlugin" class="tsd-kind-icon">init<wbr/>Full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initLights" class="tsd-kind-icon">init<wbr/>Lights</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initLocaleService" class="tsd-kind-icon">init<wbr/>Locale<wbr/>Service</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initNavConfig" class="tsd-kind-icon">init<wbr/>Nav<wbr/>Config</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initNavCubePlugin" class="tsd-kind-icon">init<wbr/>Nav<wbr/>Cube<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initSectionPlanePlugin" class="tsd-kind-icon">init<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initSingleSelectionPlugin" class="tsd-kind-icon">init<wbr/>Single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initSkybox" class="tsd-kind-icon">init<wbr/>Skybox</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initToolbar" class="tsd-kind-icon">init<wbr/>Toolbar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initTreeViewPlugin" class="tsd-kind-icon">init<wbr/>Tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initViewer" class="tsd-kind-icon">init<wbr/>Viewer</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#is2d" class="tsd-kind-icon">is2d</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#loadFont" class="tsd-kind-icon">load<wbr/>Font</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#loadModel" class="tsd-kind-icon">load<wbr/>Model</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#log" class="tsd-kind-icon">log</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#mutexActivation" class="tsd-kind-icon">mutex<wbr/>Activation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#off" class="tsd-kind-icon">off</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#on" class="tsd-kind-icon">on</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#once" class="tsd-kind-icon">once</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#reset" class="tsd-kind-icon">reset</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#setActive" class="tsd-kind-icon">set<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#setEnabled" class="tsd-kind-icon">set<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#setModelVisibility" class="tsd-kind-icon">set<wbr/>Model<wbr/>Visibility</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#setNavConfig" class="tsd-kind-icon">set<wbr/>Nav<wbr/>Config</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#setObjectsVisibility" class="tsd-kind-icon">set<wbr/>Objects<wbr/>Visibility</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#suppressSingleSelection" class="tsd-kind-icon">suppress<wbr/>Single<wbr/>Selection</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#translate" class="tsd-kind-icon">translate</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#viewFitAll" class="tsd-kind-icon">view<wbr/>Fit<wbr/>All</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#warn" class="tsd-kind-icon">warn</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Bim<wbr/>Viewer<span class="tsd-signature-symbol">(</span>bimViewerCfg<span class="tsd-signature-symbol">: </span><a href="../interfaces/BimViewerConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">BimViewerConfig</a>, cameraCfg<span class="tsd-signature-symbol">?: </span><a href="../interfaces/CameraConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">CameraConfig</a>, navControlCfg<span class="tsd-signature-symbol">?: </span><a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="Controller.html">Controller</a>.<a href="Controller.html#constructor">constructor</a></p><ul><li>Defined in core/BimViewer.ts:94</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>BimViewer constructor</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>bimViewerCfg: <a href="../interfaces/BimViewerConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">BimViewerConfig</a></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> cameraCfg: <a href="../interfaces/CameraConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">CameraConfig</a></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> navControlCfg: <a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group "><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_active" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _active</h3><div class="tsd-signature tsd-kind-icon">_active<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#_active">_active</a></p><ul><li>Defined in core/Controller.ts:20</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_annotationsPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _annotations<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_annotations<wbr/>Plugin<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:59</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_axisGizmoPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _axis<wbr/>Gizmo<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_axis<wbr/>Gizmo<wbr/>Plugin<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:61</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_axisSectionPlanePlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_axis<wbr/>Section<wbr/>Plane<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="AxisSectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">AxisSectionPlanePlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:60</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_backgroundColorPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _background<wbr/>Color<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_background<wbr/>Color<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="BackgroundColorPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">BackgroundColorPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:62</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_bcfViewpointsPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _bcf<wbr/>Viewpoints<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_bcf<wbr/>Viewpoints<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:63</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_bimViewerCfg" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _bim<wbr/>Viewer<wbr/>Cfg</h3><div class="tsd-signature tsd-kind-icon">_bim<wbr/>Viewer<wbr/>Cfg<span class="tsd-signature-symbol">:</span> <a href="../interfaces/BimViewerConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">BimViewerConfig</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:80</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_bottomBar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _bottom<wbr/>Bar</h3><div class="tsd-signature tsd-kind-icon">_bottom<wbr/>Bar<span class="tsd-signature-symbol">?:</span> <a href="BottomBar.html" class="tsd-signature-type" data-tsd-kind="Class">BottomBar</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:88</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_componentPropertyPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _component<wbr/>Property<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_component<wbr/>Property<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="ComponentPropertyPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">ComponentPropertyPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:64</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_contextMenu" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _context<wbr/>Menu</h3><div class="tsd-signature tsd-kind-icon">_context<wbr/>Menu<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:79</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_distanceMeasurementsPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _distance<wbr/>Measurements<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_distance<wbr/>Measurements<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">EnhancedDistanceMeasurementPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:65</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_enabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _enabled</h3><div class="tsd-signature tsd-kind-icon">_enabled<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#_enabled">_enabled</a></p><ul><li>Defined in core/Controller.ts:19</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_eventCallDepth" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _event<wbr/>Call<wbr/>Depth</h3><div class="tsd-signature tsd-kind-icon">_event<wbr/>Call<wbr/>Depth<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 0</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#_eventCallDepth">_eventCallDepth</a></p><ul><li>Defined in core/Controller.ts:18</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_eventSubs" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _event<wbr/>Subs</h3><div class="tsd-signature tsd-kind-icon">_event<wbr/>Subs<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#_eventSubs">_eventSubs</a></p><ul><li>Defined in core/Controller.ts:16</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_events" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _events</h3><div class="tsd-signature tsd-kind-icon">_events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#_events">_events</a></p><ul><li>Defined in core/Controller.ts:17</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_fastNavPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _fast<wbr/>Nav<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_fast<wbr/>Nav<wbr/>Plugin<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:66</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_fullScreenPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _full<wbr/>Screen<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_full<wbr/>Screen<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="FullScreenPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:67</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_girdPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _gird<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_gird<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="GridPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">GridPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:68</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_homeView" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _home<wbr/>View</h3><div class="tsd-signature tsd-kind-icon">_home<wbr/>View<span class="tsd-signature-symbol">?:</span> <a href="../interfaces/CameraConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">CameraConfig</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:85</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_loaders" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _loaders</h3><div class="tsd-signature tsd-kind-icon">_loaders<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:51</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter-index-signature"><h5><span class="tsd-signature-symbol">[</span>format: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><span class="tsd-signature-symbol">{ </span>constructorFunc<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">; </span>is2d<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>loader<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>constructor<wbr/>Func<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></h5></li><li class="tsd-parameter"><h5><span class="tsd-flag ts-flagOptional">Optional</span> is2d<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5></li><li class="tsd-parameter"><h5><span class="tsd-flag ts-flagOptional">Optional</span> loader<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span></h5></li></ul></li></ul></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_localeService" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _locale<wbr/>Service</h3><div class="tsd-signature tsd-kind-icon">_locale<wbr/>Service<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:78</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_navControlCfg" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _nav<wbr/>Control<wbr/>Cfg</h3><div class="tsd-signature tsd-kind-icon">_nav<wbr/>Control<wbr/>Cfg<span class="tsd-signature-symbol">:</span> <a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a><span class="tsd-signature-symbol"> = {}</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:81</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_navCubePlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _nav<wbr/>Cube<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_nav<wbr/>Cube<wbr/>Plugin<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:69</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_orthoModePlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _ortho<wbr/>Mode<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_ortho<wbr/>Mode<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="OrthoModePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">OrthoModePlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:70</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_planViewPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _plan<wbr/>View<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_plan<wbr/>View<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="PlanViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">PlanViewPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:71</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_rootHtmlElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _root<wbr/>Html<wbr/>Element</h3><div class="tsd-signature tsd-kind-icon">_root<wbr/>Html<wbr/>Element<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:86</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_rootStyleElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _root<wbr/>Style<wbr/>Element</h3><div class="tsd-signature tsd-kind-icon">_root<wbr/>Style<wbr/>Element<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">HTMLElement</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:87</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_sectionBoxPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _section<wbr/>Box<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_section<wbr/>Box<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="SectionBoxPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionBoxPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:74</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_sectionPlanePlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _section<wbr/>Plane<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_section<wbr/>Plane<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="SectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:75</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_selectionSuppressCount" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _selection<wbr/>Suppress<wbr/>Count</h3><div class="tsd-signature tsd-kind-icon">_selection<wbr/>Suppress<wbr/>Count<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 0</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:84</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_singleSelectionPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _single<wbr/>Selection<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_single<wbr/>Selection<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="SingleSelectionPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SingleSelectionPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:73</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_skybox" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _skybox</h3><div class="tsd-signature tsd-kind-icon">_skybox<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:72</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_subIdEvents" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _sub<wbr/>Id<wbr/>Events</h3><div class="tsd-signature tsd-kind-icon">_sub<wbr/>Id<wbr/>Events<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#_subIdEvents">_subIdEvents</a></p><ul><li>Defined in core/Controller.ts:15</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_subIdMap" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _sub<wbr/>Id<wbr/>Map</h3><div class="tsd-signature tsd-kind-icon">_sub<wbr/>Id<wbr/>Map<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="Map.html" class="tsd-signature-type" data-tsd-kind="Class">Map</a></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#_subIdMap">_subIdMap</a></p><ul><li>Defined in core/Controller.ts:14</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_threeDModelCount" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _threeDModel<wbr/>Count</h3><div class="tsd-signature tsd-kind-icon">_threeDModel<wbr/>Count<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 0</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:83</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_toolbar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _toolbar</h3><div class="tsd-signature tsd-kind-icon">_toolbar<span class="tsd-signature-symbol">?:</span> <a href="Toolbar.html" class="tsd-signature-type" data-tsd-kind="Class">Toolbar</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:89</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_treeViewPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> <span class="tsd-flag ts-flagOptional">Optional</span> _tree<wbr/>View<wbr/>Plugin</h3><div class="tsd-signature tsd-kind-icon">_tree<wbr/>View<wbr/>Plugin<span class="tsd-signature-symbol">?:</span> <a href="SceneGraphTreeViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SceneGraphTreeViewPlugin</a></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:76</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_twoDModelCount" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _twoDModel<wbr/>Count</h3><div class="tsd-signature tsd-kind-icon">_twoDModel<wbr/>Count<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 0</span></div><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:82</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="bimViewer" class="tsd-anchor"></a><h3>bim<wbr/>Viewer</h3><div class="tsd-signature tsd-kind-icon">bim<wbr/>Viewer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#bimViewer">bimViewer</a></p><ul><li>Defined in core/Controller.ts:8</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="children" class="tsd-anchor"></a><h3>children</h3><div class="tsd-signature tsd-kind-icon">children<span class="tsd-signature-symbol">:</span> <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#children">children</a></p><ul><li>Defined in core/Controller.ts:13</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="destroyed" class="tsd-anchor"></a><h3>destroyed</h3><div class="tsd-signature tsd-kind-icon">destroyed<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#destroyed">destroyed</a></p><ul><li>Defined in core/Controller.ts:11</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="parent" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> parent</h3><div class="tsd-signature tsd-kind-icon">parent<span class="tsd-signature-symbol">?:</span> <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#parent">parent</a></p><ul><li>Defined in core/Controller.ts:12</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="server" class="tsd-anchor"></a><h3>server</h3><div class="tsd-signature tsd-kind-icon">server<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#server">server</a></p><ul><li>Defined in core/Controller.ts:9</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a id="viewer" class="tsd-anchor"></a><h3>viewer</h3><div class="tsd-signature tsd-kind-icon">viewer<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#viewer">viewer</a></p><ul><li>Defined in core/Controller.ts:10</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Accessors</h2><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="annotationsPlugin" class="tsd-anchor"></a><h3>annotations<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> annotationsPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:531</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="axisSectionPlanePlugin" class="tsd-anchor"></a><h3>axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> axisSectionPlanePlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="AxisSectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">AxisSectionPlanePlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:544</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="AxisSectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">AxisSectionPlanePlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="backgroundColorPlugin" class="tsd-anchor"></a><h3>background<wbr/>Color<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> backgroundColorPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="BackgroundColorPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">BackgroundColorPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:476</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="BackgroundColorPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">BackgroundColorPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="bcfViewpointsPlugin" class="tsd-anchor"></a><h3>bcf<wbr/>Viewpoints<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> bcfViewpointsPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:524</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="componentPropertyPlugin" class="tsd-anchor"></a><h3>component<wbr/>Property<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> componentPropertyPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ComponentPropertyPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">ComponentPropertyPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:484</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="ComponentPropertyPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">ComponentPropertyPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="distanceMeasurementsPlugin" class="tsd-anchor"></a><h3>distance<wbr/>Measurements<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> distanceMeasurementsPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">EnhancedDistanceMeasurementPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:550</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">EnhancedDistanceMeasurementPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="fastNavPlugin" class="tsd-anchor"></a><h3>fast<wbr/>Nav<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> fastNavPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:469</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="fullScreenPlugin" class="tsd-anchor"></a><h3>full<wbr/>Screen<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> fullScreenPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="FullScreenPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:498</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="FullScreenPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="girdPlugin" class="tsd-anchor"></a><h3>gird<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> girdPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="GridPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">GridPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:517</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="GridPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">GridPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="has2dModel" class="tsd-anchor"></a><h3>has2d<wbr/>Model</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> has2dModel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:581</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>If there is any 2d model loaded</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="has3dModel" class="tsd-anchor"></a><h3>has3d<wbr/>Model</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> has3dModel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:588</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>If there is any 3d model loaded</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="navCubePlugin" class="tsd-anchor"></a><h3>nav<wbr/>Cube<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> navCubePlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:462</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="orthoModePlugin" class="tsd-anchor"></a><h3>ortho<wbr/>Mode<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> orthoModePlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="OrthoModePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">OrthoModePlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:503</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="OrthoModePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">OrthoModePlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="planViewPlugin" class="tsd-anchor"></a><h3>plan<wbr/>View<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> planViewPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="PlanViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">PlanViewPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:510</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="PlanViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">PlanViewPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="rootHtmlElement" class="tsd-anchor"></a><h3>root<wbr/>Html<wbr/>Element</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> rootHtmlElement<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:595</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Gets root HTMLElement that contains dynamically created ui</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">HTMLElement</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="rootStyleElement" class="tsd-anchor"></a><h3>root<wbr/>Style<wbr/>Element</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> rootStyleElement<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:607</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Gets root HTMLElement that contains dynamically created css</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">HTMLElement</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="sectionBoxPlugin" class="tsd-anchor"></a><h3>section<wbr/>Box<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> sectionBoxPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SectionBoxPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionBoxPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:557</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="SectionBoxPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionBoxPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="sectionPlanePlugin" class="tsd-anchor"></a><h3>section<wbr/>Plane<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> sectionPlanePlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:538</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="SectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="singleSelectionPlugin" class="tsd-anchor"></a><h3>single<wbr/>Selection<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> singleSelectionPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SingleSelectionPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SingleSelectionPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:491</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="SingleSelectionPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SingleSelectionPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="skybox" class="tsd-anchor"></a><h3>skybox</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> skybox<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:564</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="toolbar" class="tsd-anchor"></a><h3>toolbar</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> toolbar<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Toolbar.html" class="tsd-signature-type" data-tsd-kind="Class">Toolbar</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:617</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="Toolbar.html" class="tsd-signature-type" data-tsd-kind="Class">Toolbar</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="treeViewPlugin" class="tsd-anchor"></a><h3>tree<wbr/>View<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> treeViewPlugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="SceneGraphTreeViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SceneGraphTreeViewPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:571</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="SceneGraphTreeViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SceneGraphTreeViewPlugin</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="active2dMode" class="tsd-anchor"></a><h3>active2d<wbr/>Mode</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active2d<wbr/>Mode<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:814</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives 2d mode</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeAnnotation" class="tsd-anchor"></a><h3>active<wbr/>Annotation</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Annotation<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:786</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives annotation panel</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeAxisSectionPlane" class="tsd-anchor"></a><h3>active<wbr/>Axis<wbr/>Section<wbr/>Plane</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Axis<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:757</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Active axis section plane</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeBimTree" class="tsd-anchor"></a><h3>active<wbr/>Bim<wbr/>Tree</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Bim<wbr/>Tree<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:772</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives BIM Tree</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeDistanceMeasurement" class="tsd-anchor"></a><h3>active<wbr/>Distance<wbr/>Measurement</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Distance<wbr/>Measurement<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:765</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives distance measurement</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeFullScreen" class="tsd-anchor"></a><h3>active<wbr/>Full<wbr/>Screen</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Full<wbr/>Screen<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:807</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives full screen</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeOrthoMode" class="tsd-anchor"></a><h3>active<wbr/>Ortho<wbr/>Mode</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Ortho<wbr/>Mode<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:734</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives ortho mode</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeProperty" class="tsd-anchor"></a><h3>active<wbr/>Property</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Property<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:793</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives property panel</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeSectionBox" class="tsd-anchor"></a><h3>active<wbr/>Section<wbr/>Box</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Section<wbr/>Box<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:741</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives section box</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeSectionPlane" class="tsd-anchor"></a><h3>active<wbr/>Section<wbr/>Plane</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:749</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives section plane</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeSetting" class="tsd-anchor"></a><h3>active<wbr/>Setting</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Setting<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:800</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives setting panel</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeSingleSelection" class="tsd-anchor"></a><h3>active<wbr/>Single<wbr/>Selection</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Single<wbr/>Selection<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:727</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives single selection</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="activeViewpoint" class="tsd-anchor"></a><h3>active<wbr/>Viewpoint</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">active<wbr/>Viewpoint<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:779</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Actives viewpoint panel</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="appendHtmlElement" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> append<wbr/>Html<wbr/>Element</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">append<wbr/>Html<wbr/>Element<span class="tsd-signature-symbol">(</span>html<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLElement</span>, css<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:1059</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Appends ui and css to current page</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>html: <span class="tsd-signature-type">HTMLElement</span></h5></li><li><h5>css: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="clearAnnotations" class="tsd-anchor"></a><h3>clear<wbr/>Annotations</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">clear<wbr/>Annotations<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:958</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Clears all annotations.</p>
</dd><dt>memberof</dt><dd><p>BimViewer</p>
</dd></dl></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="createAnnotation" class="tsd-anchor"></a><h3>create<wbr/>Annotation</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">create<wbr/>Annotation<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:950</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Creates an annotation on the canvas.</p>
</dd><dt>memberof</dt><dd><p>BimViewer</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>params: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="Controller.html">Controller</a>.<a href="Controller.html#destroy">destroy</a></p><ul><li>Defined in core/BimViewer.ts:978</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Destroies everything</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroyAnnotation" class="tsd-anchor"></a><h3>destroy<wbr/>Annotation</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<wbr/>Annotation<span class="tsd-signature-symbol">(</span>id<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:967</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Destroys an annotation on the canvas.</p>
</dd><dt>memberof</dt><dd><p>BimViewer</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>id: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="error" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> error</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">error<span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#error">error</a></p><ul><li>Defined in core/Controller.ts:208</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Logs an error for this Controller to the JavaScript console.</p>
</div><div><p>The console message will have this format: <em><code>[ERROR] [&lt;component type&gt; =&lt;component id&gt;: &lt;message&gt;</code></em></p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>message: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The message to log</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="fire" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> fire</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">fire<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, forget<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#fire">fire</a></p><ul><li>Defined in core/Controller.ts:54</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Fires an event on this Controller.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The event type name</p>
</div></div></li><li><h5>value: <span class="tsd-signature-type">any</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The event parameters</p>
</div></div></li><li><h5>forget: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="getActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> get<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">get<wbr/>Active<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#getActive">getActive</a></p><ul><li>Defined in core/Controller.ts:288</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Gets whether or not this Controller is active.</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4><div></div></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="getCanvasImageDataUrl" class="tsd-anchor"></a><h3>get<wbr/>Canvas<wbr/>Image<wbr/>Data<wbr/>Url</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">get<wbr/>Canvas<wbr/>Image<wbr/>Data<wbr/>Url<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:911</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Gets the screenshot of the model</p>
</dd><dt>memberof</dt><dd><p>BimViewer</p>
</dd></dl></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4><div><p>dataUrls</p>
</div></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="getEnabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> get<wbr/>Enabled</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">get<wbr/>Enabled<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#getEnabled">getEnabled</a></p><ul><li>Defined in core/Controller.ts:260</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Gets whether or not this Controller is enabled.</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4><div></div></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="getLoader" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr/>Loader</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">get<wbr/>Loader<span class="tsd-signature-symbol">(</span>format<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:1032</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Gets a loader by given file format</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>format: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="getNavValueByName" class="tsd-anchor"></a><h3>get<wbr/>Nav<wbr/>Value<wbr/>By<wbr/>Name</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">get<wbr/>Nav<wbr/>Value<wbr/>By<wbr/>Name<span class="tsd-signature-symbol">(</span>paraName<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">keyof </span><a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:939</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>paraName: <span class="tsd-signature-symbol">keyof </span><a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="getUniqulModelId" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> get<wbr/>Uniqul<wbr/>Model<wbr/>Id</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">get<wbr/>Uniqul<wbr/>Model<wbr/>Id<span class="tsd-signature-symbol">(</span>prefix<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:1044</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> prefix: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="goToHomeView" class="tsd-anchor"></a><h3>go<wbr/>To<wbr/>Home<wbr/>View</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">go<wbr/>To<wbr/>Home<wbr/>View<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:639</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Goes to home view</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initAxisGizmoPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Axis<wbr/>Gizmo<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Axis<wbr/>Gizmo<wbr/>Plugin<span class="tsd-signature-symbol">(</span>canvasId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:314</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> canvasId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initAxisSectionPlanePlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Axis<wbr/>Section<wbr/>Plane<wbr/>Plugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="AxisSectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">AxisSectionPlanePlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:383</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="AxisSectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">AxisSectionPlanePlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initBackgroundColorPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Background<wbr/>Color<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Background<wbr/>Color<wbr/>Plugin<span class="tsd-signature-symbol">(</span>backgroundColor<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span>, transparent<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="BackgroundColorPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">BackgroundColorPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:338</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>backgroundColor: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = DEFAULT_BACKGROUND_COLOR</span></h5></li><li><h5>transparent: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="BackgroundColorPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">BackgroundColorPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initBottomBar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Bottom<wbr/>Bar</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Bottom<wbr/>Bar<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="BottomBar.html" class="tsd-signature-type" data-tsd-kind="Class">BottomBar</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:455</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Init BottomBar</p>
</div></div><h4 class="tsd-returns-title">Returns <a href="BottomBar.html" class="tsd-signature-type" data-tsd-kind="Class">BottomBar</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initComponentPropertyPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Component<wbr/>Property<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Component<wbr/>Property<wbr/>Plugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ComponentPropertyPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">ComponentPropertyPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:358</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="ComponentPropertyPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">ComponentPropertyPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initContextMenu" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Context<wbr/>Menu</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Context<wbr/>Menu<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:349</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initDistanceMeasurementsPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Distance<wbr/>Measurements<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Distance<wbr/>Measurements<wbr/>Plugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">EnhancedDistanceMeasurementPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:392</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="EnhancedDistanceMeasurementPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">EnhancedDistanceMeasurementPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initFastNavPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Fast<wbr/>Nav<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Fast<wbr/>Nav<wbr/>Plugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:305</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initFullScreenPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Full<wbr/>Screen<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Full<wbr/>Screen<wbr/>Plugin<span class="tsd-signature-symbol">(</span>canvasId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="FullScreenPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:366</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>canvasId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="FullScreenPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">FullScreenPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initLights" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Lights</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Lights<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:221</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Replaces xeokit&#39;s default lights with our own, so we can change the parameters.
Reference to <a href="https://github.com/xeokit/xeokit-sdk/blob/master/src/viewer/scene/lights/DirLight.js">https://github.com/xeokit/xeokit-sdk/blob/master/src/viewer/scene/lights/DirLight.js</a></p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initLocaleService" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Locale<wbr/>Service</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Locale<wbr/>Service<span class="tsd-signature-symbol">(</span>locale<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:132</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>locale: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;cn&quot;</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initNavConfig" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Nav<wbr/>Config</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Nav<wbr/>Config<span class="tsd-signature-symbol">(</span>navControlCfg<span class="tsd-signature-symbol">?: </span><a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:245</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> navControlCfg: <a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initNavCubePlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Nav<wbr/>Cube<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Nav<wbr/>Cube<wbr/>Plugin<span class="tsd-signature-symbol">(</span>canvasId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:273</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> canvasId: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initSectionPlanePlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Section<wbr/>Plane<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Section<wbr/>Plane<wbr/>Plugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:378</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="SectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initSingleSelectionPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Single<wbr/>Selection<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Single<wbr/>Selection<wbr/>Plugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SingleSelectionPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SingleSelectionPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:343</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="SingleSelectionPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SingleSelectionPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initSkybox" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Skybox</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Skybox<span class="tsd-signature-symbol">(</span>src<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:209</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>src: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initToolbar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Toolbar</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Toolbar<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:444</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Init toolbar whether from user defined or build in element</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initTreeViewPlugin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Tree<wbr/>View<wbr/>Plugin</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Tree<wbr/>View<wbr/>Plugin<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="SceneGraphTreeViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SceneGraphTreeViewPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:400</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="SceneGraphTreeViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SceneGraphTreeViewPlugin</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initViewer" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Viewer</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Viewer<span class="tsd-signature-symbol">(</span>cfg<span class="tsd-signature-symbol">: </span><a href="../interfaces/BimViewerConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">BimViewerConfig</a>, cameraCfg<span class="tsd-signature-symbol">?: </span><a href="../interfaces/CameraConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">CameraConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:144</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>cfg: <a href="../interfaces/BimViewerConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">BimViewerConfig</a></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> cameraCfg: <a href="../interfaces/CameraConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">CameraConfig</a></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="is2d" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> is2d</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">is2d<span class="tsd-signature-symbol">(</span>format<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:1023</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Checks if a format is 2d</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>format: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="loadFont" class="tsd-anchor"></a><h3>load<wbr/>Font</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">load<wbr/>Font<span class="tsd-signature-symbol">(</span>url<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:920</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Loads fonts needed for text rendering</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>url: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">Promise</span><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">&gt;</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="loadModel" class="tsd-anchor"></a><h3>load<wbr/>Model</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">load<wbr/>Model<span class="tsd-signature-symbol">(</span>modleCfg<span class="tsd-signature-symbol">: </span><a href="../interfaces/ModelConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ModelConfig</a>, okFunc<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">(</span>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:843</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Loads a model</p>
</dd><dt>memberof</dt><dd><p>BimViewer</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>modleCfg: <a href="../interfaces/ModelConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ModelConfig</a></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> okFunc: <span class="tsd-signature-symbol">(</span>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></h5><ul class="tsd-parameters"><li class="tsd-parameter-signature"><ul class="tsd-signatures tsd-kind-type-literal"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>model: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></li></ul></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="log" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> log</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">log<span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#log">log</a></p><ul><li>Defined in core/Controller.ts:180</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Logs a console debugging message for this Controller.</p>
</div><div><p>The console message will have this format: <em><code>[LOG] [&lt;component type&gt; &lt;component id&gt;: &lt;message&gt;</code></em></p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>message: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The message to log</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="mutexActivation" class="tsd-anchor"></a><h3>mutex<wbr/>Activation</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">mutex<wbr/>Activation<span class="tsd-signature-symbol">(</span>controllers<span class="tsd-signature-symbol">: </span><a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#mutexActivation">mutexActivation</a></p><ul><li>Defined in core/Controller.ts:213</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>controllers: <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="off" class="tsd-anchor"></a><h3>off</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">off<span class="tsd-signature-symbol">(</span>subId<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#off">off</a></p><ul><li>Defined in core/Controller.ts:130</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Cancels an event subscription that was previously made with {@link Controller#on} or {@link Controller#once}.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> subId: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Subscription ID</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="on" class="tsd-anchor"></a><h3>on</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">on<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, scope<span class="tsd-signature-symbol">?: </span><a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#on">on</a></p><ul><li>Defined in core/Controller.ts:93</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Subscribes to an event on this Controller.</p>
</div><div><p>The callback is be called with this component as scope.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The event</p>
</div></div></li><li><h5>callback: <span class="tsd-signature-type">any</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Called fired on the event</p>
</div></div></li><li><h5>scope: <a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a><span class="tsd-signature-symbol"> = ...</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4><div><p>Handle to the subscription, which may be used to unsubscribe with {@link #off}.</p>
</div></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="once" class="tsd-anchor"></a><h3>once</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">once<span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, callback<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, scope<span class="tsd-signature-symbol">?: </span><a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#once">once</a></p><ul><li>Defined in core/Controller.ts:159</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Subscribes to the next occurrence of the given event, then un-subscribes as soon as the event is handled.</p>
</div><div><p>This is equivalent to calling {@link Controller#on}, and then calling {@link Controller#off} inside the callback function.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Data event to listen to</p>
</div></div></li><li><h5>callback: <span class="tsd-signature-type">any</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Called when fresh data is available at the event</p>
</div></div></li><li><h5>scope: <a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a><span class="tsd-signature-symbol"> = ...</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="reset" class="tsd-anchor"></a><h3>reset</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">reset<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:674</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Resets BimViewer by clearing invisible, selected, x-rayed, highlighted objects.</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="setActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> set<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">set<wbr/>Active<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#setActive">setActive</a></p><ul><li>Defined in core/Controller.ts:273</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Activates or deactivates this Controller.</p>
</div><div><p>Fires an &quot;active&quot; event on update.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Whether or not to activate.</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="setEnabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> set<wbr/>Enabled</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">set<wbr/>Enabled<span class="tsd-signature-symbol">(</span>enabled<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#setEnabled">setEnabled</a></p><ul><li>Defined in core/Controller.ts:245</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Enables or disables this Controller.</p>
</div><div><p>Fires an &quot;enabled&quot; event on update.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>enabled: <span class="tsd-signature-type">boolean</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>Whether or not to enable.</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setModelVisibility" class="tsd-anchor"></a><h3>set<wbr/>Model<wbr/>Visibility</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Model<wbr/>Visibility<span class="tsd-signature-symbol">(</span>model<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:888</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Sets the visibility for the model</p>
</dd><dt>memberof</dt><dd><p>BimViewer</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>model: <span class="tsd-signature-type">any</span></h5></li><li><h5>visible: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setNavConfig" class="tsd-anchor"></a><h3>set<wbr/>Nav<wbr/>Config</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Nav<wbr/>Config<span class="tsd-signature-symbol">(</span>newCfg<span class="tsd-signature-symbol">?: </span><a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:928</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Sets navigation control parameters</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>newCfg: <a href="NavControlConfig.html" class="tsd-signature-type" data-tsd-kind="Class">NavControlConfig</a><span class="tsd-signature-symbol"> = {}</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setObjectsVisibility" class="tsd-anchor"></a><h3>set<wbr/>Objects<wbr/>Visibility</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Objects<wbr/>Visibility<span class="tsd-signature-symbol">(</span>objectIds<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span>, visible<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:902</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Sets the visibily for Objects</p>
</dd><dt>memberof</dt><dd><p>BimViewer</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>objectIds: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span></h5></li><li><h5>visible: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="suppressSingleSelection" class="tsd-anchor"></a><h3>suppress<wbr/>Single<wbr/>Selection</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">suppress<wbr/>Single<wbr/>Selection<span class="tsd-signature-symbol">(</span>suppress<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:824</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Suppresses or unsupporess SingleSelectionPlugin
When other tools(measure, section, etc.) are actived, we need to suppress from selecting any object.
While, we need to unsuppress it when a tool is inactived.
Here we assume SingleSelectionPlugin is active once created, if not, we need to adjust the logic a bit.</p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>suppress: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="translate" class="tsd-anchor"></a><h3>translate</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">translate<span class="tsd-signature-symbol">(</span>key<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:971</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>key: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="viewFitAll" class="tsd-anchor"></a><h3>view<wbr/>Fit<wbr/>All</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">view<wbr/>Fit<wbr/>All<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/BimViewer.ts:630</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Makes viewport fit to all models</p>
</div></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="warn" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> warn</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><li class="tsd-signature tsd-kind-icon">warn<span class="tsd-signature-symbol">(</span>message<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="Controller.html">Controller</a>.<a href="Controller.html#warn">warn</a></p><ul><li>Defined in core/Controller.ts:194</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Logs a warning for this Controller to the JavaScript console.</p>
</div><div><p>The console message will have this format: <em><code>[WARN] [&lt;component type&gt; =&lt;component id&gt;: &lt;message&gt;</code></em></p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>message: <span class="tsd-signature-type">string</span></h5><div class="tsd-comment tsd-typography"><div class="lead">
<p>The message to log</p>
</div></div></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="BimViewer.html" class="tsd-kind-icon">Bim<wbr/>Viewer</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="BimViewer.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_annotationsPlugin" class="tsd-kind-icon">_annotations<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_axisGizmoPlugin" class="tsd-kind-icon">_axis<wbr/>Gizmo<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_axisSectionPlanePlugin" class="tsd-kind-icon">_axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_backgroundColorPlugin" class="tsd-kind-icon">_background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_bcfViewpointsPlugin" class="tsd-kind-icon">_bcf<wbr/>Viewpoints<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_bimViewerCfg" class="tsd-kind-icon">_bim<wbr/>Viewer<wbr/>Cfg</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_bottomBar" class="tsd-kind-icon">_bottom<wbr/>Bar</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_componentPropertyPlugin" class="tsd-kind-icon">_component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_contextMenu" class="tsd-kind-icon">_context<wbr/>Menu</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_distanceMeasurementsPlugin" class="tsd-kind-icon">_distance<wbr/>Measurements<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_enabled" class="tsd-kind-icon">_enabled</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_eventCallDepth" class="tsd-kind-icon">_event<wbr/>Call<wbr/>Depth</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_eventSubs" class="tsd-kind-icon">_event<wbr/>Subs</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_events" class="tsd-kind-icon">_events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_fastNavPlugin" class="tsd-kind-icon">_fast<wbr/>Nav<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_fullScreenPlugin" class="tsd-kind-icon">_full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_girdPlugin" class="tsd-kind-icon">_gird<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_homeView" class="tsd-kind-icon">_home<wbr/>View</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_loaders" class="tsd-kind-icon">_loaders</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_localeService" class="tsd-kind-icon">_locale<wbr/>Service</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_navControlCfg" class="tsd-kind-icon">_nav<wbr/>Control<wbr/>Cfg</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_navCubePlugin" class="tsd-kind-icon">_nav<wbr/>Cube<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_orthoModePlugin" class="tsd-kind-icon">_ortho<wbr/>Mode<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_planViewPlugin" class="tsd-kind-icon">_plan<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_rootHtmlElement" class="tsd-kind-icon">_root<wbr/>Html<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_rootStyleElement" class="tsd-kind-icon">_root<wbr/>Style<wbr/>Element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_sectionBoxPlugin" class="tsd-kind-icon">_section<wbr/>Box<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_sectionPlanePlugin" class="tsd-kind-icon">_section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_selectionSuppressCount" class="tsd-kind-icon">_selection<wbr/>Suppress<wbr/>Count</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_singleSelectionPlugin" class="tsd-kind-icon">_single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_skybox" class="tsd-kind-icon">_skybox</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_subIdEvents" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Events</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#_subIdMap" class="tsd-kind-icon">_sub<wbr/>Id<wbr/>Map</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_threeDModelCount" class="tsd-kind-icon">_threeDModel<wbr/>Count</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_toolbar" class="tsd-kind-icon">_toolbar</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_treeViewPlugin" class="tsd-kind-icon">_tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#_twoDModelCount" class="tsd-kind-icon">_twoDModel<wbr/>Count</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#bimViewer" class="tsd-kind-icon">bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#children" class="tsd-kind-icon">children</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#destroyed" class="tsd-kind-icon">destroyed</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#parent" class="tsd-kind-icon">parent</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#server" class="tsd-kind-icon">server</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#viewer" class="tsd-kind-icon">viewer</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#annotationsPlugin" class="tsd-kind-icon">annotations<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#axisSectionPlanePlugin" class="tsd-kind-icon">axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#backgroundColorPlugin" class="tsd-kind-icon">background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#bcfViewpointsPlugin" class="tsd-kind-icon">bcf<wbr/>Viewpoints<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#componentPropertyPlugin" class="tsd-kind-icon">component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#distanceMeasurementsPlugin" class="tsd-kind-icon">distance<wbr/>Measurements<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#fastNavPlugin" class="tsd-kind-icon">fast<wbr/>Nav<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#fullScreenPlugin" class="tsd-kind-icon">full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#girdPlugin" class="tsd-kind-icon">gird<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#has2dModel" class="tsd-kind-icon">has2d<wbr/>Model</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#has3dModel" class="tsd-kind-icon">has3d<wbr/>Model</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#navCubePlugin" class="tsd-kind-icon">nav<wbr/>Cube<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#orthoModePlugin" class="tsd-kind-icon">ortho<wbr/>Mode<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#planViewPlugin" class="tsd-kind-icon">plan<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#rootHtmlElement" class="tsd-kind-icon">root<wbr/>Html<wbr/>Element</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#rootStyleElement" class="tsd-kind-icon">root<wbr/>Style<wbr/>Element</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#sectionBoxPlugin" class="tsd-kind-icon">section<wbr/>Box<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#sectionPlanePlugin" class="tsd-kind-icon">section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#singleSelectionPlugin" class="tsd-kind-icon">single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#skybox" class="tsd-kind-icon">skybox</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#toolbar" class="tsd-kind-icon">toolbar</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="BimViewer.html#treeViewPlugin" class="tsd-kind-icon">tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#active2dMode" class="tsd-kind-icon">active2d<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeAnnotation" class="tsd-kind-icon">active<wbr/>Annotation</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeAxisSectionPlane" class="tsd-kind-icon">active<wbr/>Axis<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeBimTree" class="tsd-kind-icon">active<wbr/>Bim<wbr/>Tree</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeDistanceMeasurement" class="tsd-kind-icon">active<wbr/>Distance<wbr/>Measurement</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeFullScreen" class="tsd-kind-icon">active<wbr/>Full<wbr/>Screen</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeOrthoMode" class="tsd-kind-icon">active<wbr/>Ortho<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeProperty" class="tsd-kind-icon">active<wbr/>Property</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSectionBox" class="tsd-kind-icon">active<wbr/>Section<wbr/>Box</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSectionPlane" class="tsd-kind-icon">active<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSetting" class="tsd-kind-icon">active<wbr/>Setting</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeSingleSelection" class="tsd-kind-icon">active<wbr/>Single<wbr/>Selection</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#activeViewpoint" class="tsd-kind-icon">active<wbr/>Viewpoint</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#appendHtmlElement" class="tsd-kind-icon">append<wbr/>Html<wbr/>Element</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#clearAnnotations" class="tsd-kind-icon">clear<wbr/>Annotations</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#createAnnotation" class="tsd-kind-icon">create<wbr/>Annotation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="BimViewer.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#destroyAnnotation" class="tsd-kind-icon">destroy<wbr/>Annotation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#error" class="tsd-kind-icon">error</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#fire" class="tsd-kind-icon">fire</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#getActive" class="tsd-kind-icon">get<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#getCanvasImageDataUrl" class="tsd-kind-icon">get<wbr/>Canvas<wbr/>Image<wbr/>Data<wbr/>Url</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#getEnabled" class="tsd-kind-icon">get<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#getLoader" class="tsd-kind-icon">get<wbr/>Loader</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#getNavValueByName" class="tsd-kind-icon">get<wbr/>Nav<wbr/>Value<wbr/>By<wbr/>Name</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#getUniqulModelId" class="tsd-kind-icon">get<wbr/>Uniqul<wbr/>Model<wbr/>Id</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#goToHomeView" class="tsd-kind-icon">go<wbr/>To<wbr/>Home<wbr/>View</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initAxisGizmoPlugin" class="tsd-kind-icon">init<wbr/>Axis<wbr/>Gizmo<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initAxisSectionPlanePlugin" class="tsd-kind-icon">init<wbr/>Axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initBackgroundColorPlugin" class="tsd-kind-icon">init<wbr/>Background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initBottomBar" class="tsd-kind-icon">init<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initComponentPropertyPlugin" class="tsd-kind-icon">init<wbr/>Component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initContextMenu" class="tsd-kind-icon">init<wbr/>Context<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initDistanceMeasurementsPlugin" class="tsd-kind-icon">init<wbr/>Distance<wbr/>Measurements<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initFastNavPlugin" class="tsd-kind-icon">init<wbr/>Fast<wbr/>Nav<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initFullScreenPlugin" class="tsd-kind-icon">init<wbr/>Full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initLights" class="tsd-kind-icon">init<wbr/>Lights</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initLocaleService" class="tsd-kind-icon">init<wbr/>Locale<wbr/>Service</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initNavConfig" class="tsd-kind-icon">init<wbr/>Nav<wbr/>Config</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initNavCubePlugin" class="tsd-kind-icon">init<wbr/>Nav<wbr/>Cube<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initSectionPlanePlugin" class="tsd-kind-icon">init<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initSingleSelectionPlugin" class="tsd-kind-icon">init<wbr/>Single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initSkybox" class="tsd-kind-icon">init<wbr/>Skybox</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initToolbar" class="tsd-kind-icon">init<wbr/>Toolbar</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initTreeViewPlugin" class="tsd-kind-icon">init<wbr/>Tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#initViewer" class="tsd-kind-icon">init<wbr/>Viewer</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="BimViewer.html#is2d" class="tsd-kind-icon">is2d</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#loadFont" class="tsd-kind-icon">load<wbr/>Font</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#loadModel" class="tsd-kind-icon">load<wbr/>Model</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#log" class="tsd-kind-icon">log</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#mutexActivation" class="tsd-kind-icon">mutex<wbr/>Activation</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#off" class="tsd-kind-icon">off</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#on" class="tsd-kind-icon">on</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="BimViewer.html#once" class="tsd-kind-icon">once</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#reset" class="tsd-kind-icon">reset</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#setActive" class="tsd-kind-icon">set<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#setEnabled" class="tsd-kind-icon">set<wbr/>Enabled</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#setModelVisibility" class="tsd-kind-icon">set<wbr/>Model<wbr/>Visibility</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#setNavConfig" class="tsd-kind-icon">set<wbr/>Nav<wbr/>Config</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#setObjectsVisibility" class="tsd-kind-icon">set<wbr/>Objects<wbr/>Visibility</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#suppressSingleSelection" class="tsd-kind-icon">suppress<wbr/>Single<wbr/>Selection</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#translate" class="tsd-kind-icon">translate</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="BimViewer.html#viewFitAll" class="tsd-kind-icon">view<wbr/>Fit<wbr/>All</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="BimViewer.html#warn" class="tsd-kind-icon">warn</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>