@import url("./icons.css");

:root {
    /* Light */
    --light-color-background: #fcfcfc;
    --light-color-secondary-background: #fff;
    --light-color-text: #222;
    --light-color-text-aside: #707070;
    --light-color-link: #4da6ff;
    --light-color-menu-divider: #eee;
    --light-color-menu-divider-focus: #000;
    --light-color-menu-label: #707070;
    --light-color-panel: var(--light-color-secondary-background);
    --light-color-panel-divider: #eee;
    --light-color-comment-tag: #707070;
    --light-color-comment-tag-text: #fff;
    --light-color-ts: #9600ff;
    --light-color-ts-interface: #647f1b;
    --light-color-ts-enum: #937210;
    --light-color-ts-class: #0672de;
    --light-color-ts-private: #707070;
    --light-color-toolbar: #fff;
    --light-color-toolbar-text: #333;
    --light-icon-filter: invert(0);
    --light-external-icon: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' width='10' height='10'><path fill-opacity='0' stroke='%23000' stroke-width='10' d='m43,35H5v60h60V57M45,5v10l10,10-30,30 20,20 30-30 10,10h10V5z'/></svg>");

    /* Dark */
    --dark-color-background: #36393f;
    --dark-color-secondary-background: #2f3136;
    --dark-color-text: #ffffff;
    --dark-color-text-aside: #e6e4e4;
    --dark-color-link: #00aff4;
    --dark-color-menu-divider: #eee;
    --dark-color-menu-divider-focus: #000;
    --dark-color-menu-label: #707070;
    --dark-color-panel: var(--dark-color-secondary-background);
    --dark-color-panel-divider: #818181;
    --dark-color-comment-tag: #dcddde;
    --dark-color-comment-tag-text: #2f3136;
    --dark-color-ts: #c97dff;
    --dark-color-ts-interface: #9cbe3c;
    --dark-color-ts-enum: #d6ab29;
    --dark-color-ts-class: #3695f3;
    --dark-color-ts-private: #e2e2e2;
    --dark-color-toolbar: #34373c;
    --dark-color-toolbar-text: #ffffff;
    --dark-icon-filter: invert(1);
    --dark-external-icon: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' width='10' height='10'><path fill-opacity='0' stroke='%23fff' stroke-width='10' d='m43,35H5v60h60V57M45,5v10l10,10-30,30 20,20 30-30 10,10h10V5z'/></svg>");
}

@media (prefers-color-scheme: light) {
    :root {
        --color-background: var(--light-color-background);
        --color-secondary-background: var(--light-color-secondary-background);
        --color-text: var(--light-color-text);
        --color-text-aside: var(--light-color-text-aside);
        --color-link: var(--light-color-link);
        --color-menu-divider: var(--light-color-menu-divider);
        --color-menu-divider-focus: var(--light-color-menu-divider-focus);
        --color-menu-label: var(--light-color-menu-label);
        --color-panel: var(--light-color-panel);
        --color-panel-divider: var(--light-color-panel-divider);
        --color-comment-tag: var(--light-color-comment-tag);
        --color-comment-tag-text: var(--light-color-comment-tag-text);
        --color-ts: var(--light-color-ts);
        --color-ts-interface: var(--light-color-ts-interface);
        --color-ts-enum: var(--light-color-ts-enum);
        --color-ts-class: var(--light-color-ts-class);
        --color-ts-private: var(--light-color-ts-private);
        --color-toolbar: var(--light-color-toolbar);
        --color-toolbar-text: var(--light-color-toolbar-text);
        --icon-filter: var(--light-icon-filter);
        --external-icon: var(--light-external-icon);
    }
}

@media (prefers-color-scheme: dark) {
    :root {
        --color-background: var(--dark-color-background);
        --color-secondary-background: var(--dark-color-secondary-background);
        --color-text: var(--dark-color-text);
        --color-text-aside: var(--dark-color-text-aside);
        --color-link: var(--dark-color-link);
        --color-menu-divider: var(--dark-color-menu-divider);
        --color-menu-divider-focus: var(--dark-color-menu-divider-focus);
        --color-menu-label: var(--dark-color-menu-label);
        --color-panel: var(--dark-color-panel);
        --color-panel-divider: var(--dark-color-panel-divider);
        --color-comment-tag: var(--dark-color-comment-tag);
        --color-comment-tag-text: var(--dark-color-comment-tag-text);
        --color-ts: var(--dark-color-ts);
        --color-ts-interface: var(--dark-color-ts-interface);
        --color-ts-enum: var(--dark-color-ts-enum);
        --color-ts-class: var(--dark-color-ts-class);
        --color-ts-private: var(--dark-color-ts-private);
        --color-toolbar: var(--dark-color-toolbar);
        --color-toolbar-text: var(--dark-color-toolbar-text);
        --icon-filter: var(--dark-icon-filter);
        --external-icon: var(--dark-external-icon);
    }
}

body {
    margin: 0;
}

body.light {
    --color-background: var(--light-color-background);
    --color-secondary-background: var(--light-color-secondary-background);
    --color-text: var(--light-color-text);
    --color-text-aside: var(--light-color-text-aside);
    --color-link: var(--light-color-link);
    --color-menu-divider: var(--light-color-menu-divider);
    --color-menu-divider-focus: var(--light-color-menu-divider-focus);
    --color-menu-label: var(--light-color-menu-label);
    --color-panel: var(--light-color-panel);
    --color-panel-divider: var(--light-color-panel-divider);
    --color-comment-tag: var(--light-color-comment-tag);
    --color-comment-tag-text: var(--light-color-comment-tag-text);
    --color-ts: var(--light-color-ts);
    --color-ts-interface: var(--light-color-ts-interface);
    --color-ts-enum: var(--light-color-ts-enum);
    --color-ts-class: var(--light-color-ts-class);
    --color-ts-private: var(--light-color-ts-private);
    --color-toolbar: var(--light-color-toolbar);
    --color-toolbar-text: var(--light-color-toolbar-text);
    --icon-filter: var(--light-icon-filter);
    --external-icon: var(--light-external-icon);
}

body.dark {
    --color-background: var(--dark-color-background);
    --color-secondary-background: var(--dark-color-secondary-background);
    --color-text: var(--dark-color-text);
    --color-text-aside: var(--dark-color-text-aside);
    --color-link: var(--dark-color-link);
    --color-menu-divider: var(--dark-color-menu-divider);
    --color-menu-divider-focus: var(--dark-color-menu-divider-focus);
    --color-menu-label: var(--dark-color-menu-label);
    --color-panel: var(--dark-color-panel);
    --color-panel-divider: var(--dark-color-panel-divider);
    --color-comment-tag: var(--dark-color-comment-tag);
    --color-comment-tag-text: var(--dark-color-comment-tag-text);
    --color-ts: var(--dark-color-ts);
    --color-ts-interface: var(--dark-color-ts-interface);
    --color-ts-enum: var(--dark-color-ts-enum);
    --color-ts-class: var(--dark-color-ts-class);
    --color-ts-private: var(--dark-color-ts-private);
    --color-toolbar: var(--dark-color-toolbar);
    --color-toolbar-text: var(--dark-color-toolbar-text);
    --icon-filter: var(--dark-icon-filter);
    --external-icon: var(--dark-external-icon);
}

h1 {
    font-size: 2em;
    margin: 0.67em 0;
}

h2 {
    font-size: 1.5em;
    margin: 0.83em 0;
}

h3 {
    font-size: 1.17em;
    margin: 1em 0;
}

h4,
.tsd-index-panel h3 {
    font-size: 1em;
    margin: 1.33em 0;
}

h5 {
    font-size: 0.83em;
    margin: 1.67em 0;
}

h6 {
    font-size: 0.67em;
    margin: 2.33em 0;
}

pre {
    white-space: pre;
    white-space: pre-wrap;
    word-wrap: break-word;
}

dl,
menu,
ol,
ul {
    margin: 1em 0;
}

dd {
    margin: 0 0 0 40px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}
@media (max-width: 640px) {
    .container {
        padding: 0 20px;
    }
}

.container-main {
    padding-bottom: 200px;
}

.row {
    display: flex;
    position: relative;
    margin: 0 -10px;
}
.row:after {
    visibility: hidden;
    display: block;
    content: "";
    clear: both;
    height: 0;
}

.col-4,
.col-8 {
    box-sizing: border-box;
    float: left;
    padding: 0 10px;
}

.col-4 {
    width: 33.3333333333%;
}
.col-8 {
    width: 66.6666666667%;
}

ul.tsd-descriptions > li > :first-child,
.tsd-panel > :first-child,
.col-8 > :first-child,
.col-4 > :first-child,
ul.tsd-descriptions > li > :first-child > :first-child,
.tsd-panel > :first-child > :first-child,
.col-8 > :first-child > :first-child,
.col-4 > :first-child > :first-child,
ul.tsd-descriptions > li > :first-child > :first-child > :first-child,
.tsd-panel > :first-child > :first-child > :first-child,
.col-8 > :first-child > :first-child > :first-child,
.col-4 > :first-child > :first-child > :first-child {
    margin-top: 0;
}
ul.tsd-descriptions > li > :last-child,
.tsd-panel > :last-child,
.col-8 > :last-child,
.col-4 > :last-child,
ul.tsd-descriptions > li > :last-child > :last-child,
.tsd-panel > :last-child > :last-child,
.col-8 > :last-child > :last-child,
.col-4 > :last-child > :last-child,
ul.tsd-descriptions > li > :last-child > :last-child > :last-child,
.tsd-panel > :last-child > :last-child > :last-child,
.col-8 > :last-child > :last-child > :last-child,
.col-4 > :last-child > :last-child > :last-child {
    margin-bottom: 0;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
@keyframes fade-out {
    from {
        opacity: 1;
        visibility: visible;
    }
    to {
        opacity: 0;
    }
}
@keyframes fade-in-delayed {
    0% {
        opacity: 0;
    }
    33% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes fade-out-delayed {
    0% {
        opacity: 1;
        visibility: visible;
    }
    66% {
        opacity: 0;
    }
    100% {
        opacity: 0;
    }
}
@keyframes shift-to-left {
    from {
        transform: translate(0, 0);
    }
    to {
        transform: translate(-25%, 0);
    }
}
@keyframes unshift-to-left {
    from {
        transform: translate(-25%, 0);
    }
    to {
        transform: translate(0, 0);
    }
}
@keyframes pop-in-from-right {
    from {
        transform: translate(100%, 0);
    }
    to {
        transform: translate(0, 0);
    }
}
@keyframes pop-out-to-right {
    from {
        transform: translate(0, 0);
        visibility: visible;
    }
    to {
        transform: translate(100%, 0);
    }
}
body {
    background: var(--color-background);
    font-family: "Segoe UI", sans-serif;
    font-size: 16px;
    color: var(--color-text);
}

a {
    color: var(--color-link);
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
a.external[target="_blank"] {
    background-image: var(--external-icon);
    background-position: top 3px right;
    background-repeat: no-repeat;
    padding-right: 13px;
}

code,
pre {
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
    padding: 0.2em;
    margin: 0;
    font-size: 14px;
}

pre {
    padding: 10px;
}
pre code {
    padding: 0;
    font-size: 100%;
}

blockquote {
    margin: 1em 0;
    padding-left: 1em;
    border-left: 4px solid gray;
}

.tsd-typography {
    line-height: 1.333em;
}
.tsd-typography ul {
    list-style: square;
    padding: 0 0 0 20px;
    margin: 0;
}
.tsd-typography h4,
.tsd-typography .tsd-index-panel h3,
.tsd-index-panel .tsd-typography h3,
.tsd-typography h5,
.tsd-typography h6 {
    font-size: 1em;
    margin: 0;
}
.tsd-typography h5,
.tsd-typography h6 {
    font-weight: normal;
}
.tsd-typography p,
.tsd-typography ul,
.tsd-typography ol {
    margin: 1em 0;
}

@media (min-width: 901px) and (max-width: 1024px) {
    html .col-content {
        width: 72%;
    }
    html .col-menu {
        width: 28%;
    }
    html .tsd-navigation {
        padding-left: 10px;
    }
}
@media (max-width: 900px) {
    html .col-content {
        float: none;
        width: 100%;
    }
    html .col-menu {
        position: fixed !important;
        overflow: auto;
        -webkit-overflow-scrolling: touch;
        z-index: 1024;
        top: 0 !important;
        bottom: 0 !important;
        left: auto !important;
        right: 0 !important;
        width: 100%;
        padding: 20px 20px 0 0;
        max-width: 450px;
        visibility: hidden;
        background-color: var(--color-panel);
        transform: translate(100%, 0);
    }
    html .col-menu > *:last-child {
        padding-bottom: 20px;
    }
    html .overlay {
        content: "";
        display: block;
        position: fixed;
        z-index: 1023;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.75);
        visibility: hidden;
    }

    .to-has-menu .overlay {
        animation: fade-in 0.4s;
    }

    .to-has-menu :is(header, footer, .col-content) {
        animation: shift-to-left 0.4s;
    }

    .to-has-menu .col-menu {
        animation: pop-in-from-right 0.4s;
    }

    .from-has-menu .overlay {
        animation: fade-out 0.4s;
    }

    .from-has-menu :is(header, footer, .col-content) {
        animation: unshift-to-left 0.4s;
    }

    .from-has-menu .col-menu {
        animation: pop-out-to-right 0.4s;
    }

    .has-menu body {
        overflow: hidden;
    }
    .has-menu .overlay {
        visibility: visible;
    }
    .has-menu :is(header, footer, .col-content) {
        transform: translate(-25%, 0);
    }
    .has-menu .col-menu {
        visibility: visible;
        transform: translate(0, 0);
        display: grid;
        grid-template-rows: auto 1fr;
        max-height: 100vh;
    }
    .has-menu .tsd-navigation {
        max-height: 100%;
    }
}

.tsd-page-title {
    padding: 70px 0 20px 0;
    margin: 0 0 40px 0;
    background: var(--color-panel);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.35);
}
.tsd-page-title h1 {
    margin: 0;
}

.tsd-breadcrumb {
    margin: 0;
    padding: 0;
    color: var(--color-text-aside);
}
.tsd-breadcrumb a {
    color: var(--color-text-aside);
    text-decoration: none;
}
.tsd-breadcrumb a:hover {
    text-decoration: underline;
}
.tsd-breadcrumb li {
    display: inline;
}
.tsd-breadcrumb li:after {
    content: " / ";
}

dl.tsd-comment-tags {
    overflow: hidden;
}
dl.tsd-comment-tags dt {
    float: left;
    padding: 1px 5px;
    margin: 0 10px 0 0;
    border-radius: 4px;
    border: 1px solid var(--color-comment-tag);
    color: var(--color-comment-tag);
    font-size: 0.8em;
    font-weight: normal;
}
dl.tsd-comment-tags dd {
    margin: 0 0 10px 0;
}
dl.tsd-comment-tags dd:before,
dl.tsd-comment-tags dd:after {
    display: table;
    content: " ";
}
dl.tsd-comment-tags dd pre,
dl.tsd-comment-tags dd:after {
    clear: both;
}
dl.tsd-comment-tags p {
    margin: 0;
}

.tsd-panel.tsd-comment .lead {
    font-size: 1.1em;
    line-height: 1.333em;
    margin-bottom: 2em;
}
.tsd-panel.tsd-comment .lead:last-child {
    margin-bottom: 0;
}

.toggle-protected .tsd-is-private {
    display: none;
}

.toggle-public .tsd-is-private,
.toggle-public .tsd-is-protected,
.toggle-public .tsd-is-private-protected {
    display: none;
}

.toggle-inherited .tsd-is-inherited {
    display: none;
}

.toggle-externals .tsd-is-external {
    display: none;
}

#tsd-filter {
    position: relative;
    display: inline-block;
    height: 40px;
    vertical-align: bottom;
}
.no-filter #tsd-filter {
    display: none;
}
#tsd-filter .tsd-filter-group {
    display: inline-block;
    height: 40px;
    vertical-align: bottom;
    white-space: nowrap;
}
#tsd-filter input {
    display: none;
}
@media (max-width: 900px) {
    #tsd-filter .tsd-filter-group {
        display: block;
        position: absolute;
        top: 40px;
        right: 20px;
        height: auto;
        background-color: var(--color-panel);
        visibility: hidden;
        transform: translate(50%, 0);
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
    }
    .has-options #tsd-filter .tsd-filter-group {
        visibility: visible;
    }
    .to-has-options #tsd-filter .tsd-filter-group {
        animation: fade-in 0.2s;
    }
    .from-has-options #tsd-filter .tsd-filter-group {
        animation: fade-out 0.2s;
    }
    #tsd-filter label,
    #tsd-filter .tsd-select {
        display: block;
        padding-right: 20px;
    }
}

footer {
    border-top: 1px solid var(--color-panel-divider);
    background-color: var(--color-panel);
}
footer:after {
    content: "";
    display: table;
}
footer.with-border-bottom {
    border-bottom: 1px solid var(--color-panel-divider);
}
footer .tsd-legend-group {
    font-size: 0;
}
footer .tsd-legend {
    display: inline-block;
    width: 25%;
    padding: 0;
    font-size: 16px;
    list-style: none;
    line-height: 1.333em;
    vertical-align: top;
}
@media (max-width: 900px) {
    footer .tsd-legend {
        width: 50%;
    }
}

.tsd-hierarchy {
    list-style: square;
    padding: 0 0 0 20px;
    margin: 0;
}
.tsd-hierarchy .target {
    font-weight: bold;
}

.tsd-index-panel .tsd-index-content {
    margin-bottom: -30px !important;
}
.tsd-index-panel .tsd-index-section {
    margin-bottom: 30px !important;
}
.tsd-index-panel h3 {
    margin: 0 -20px 10px -20px;
    padding: 0 20px 10px 20px;
    border-bottom: 1px solid var(--color-panel-divider);
}
.tsd-index-panel ul.tsd-index-list {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    -ms-column-count: 3;
    -o-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 20px;
    -moz-column-gap: 20px;
    -ms-column-gap: 20px;
    -o-column-gap: 20px;
    column-gap: 20px;
    padding: 0;
    list-style: none;
    line-height: 1.333em;
}
@media (max-width: 900px) {
    .tsd-index-panel ul.tsd-index-list {
        -webkit-column-count: 1;
        -moz-column-count: 1;
        -ms-column-count: 1;
        -o-column-count: 1;
        column-count: 1;
    }
}
@media (min-width: 901px) and (max-width: 1024px) {
    .tsd-index-panel ul.tsd-index-list {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        -ms-column-count: 2;
        -o-column-count: 2;
        column-count: 2;
    }
}
.tsd-index-panel ul.tsd-index-list li {
    -webkit-page-break-inside: avoid;
    -moz-page-break-inside: avoid;
    -ms-page-break-inside: avoid;
    -o-page-break-inside: avoid;
    page-break-inside: avoid;
}
.tsd-index-panel a,
.tsd-index-panel .tsd-parent-kind-module a {
    color: var(--color-ts);
}
.tsd-index-panel .tsd-parent-kind-interface a {
    color: var(--color-ts-interface);
}
.tsd-index-panel .tsd-parent-kind-enum a {
    color: var(--color-ts-enum);
}
.tsd-index-panel .tsd-parent-kind-class a {
    color: var(--color-ts-class);
}
.tsd-index-panel .tsd-kind-module a {
    color: var(--color-ts);
}
.tsd-index-panel .tsd-kind-interface a {
    color: var(--color-ts-interface);
}
.tsd-index-panel .tsd-kind-enum a {
    color: var(--color-ts-enum);
}
.tsd-index-panel .tsd-kind-class a {
    color: var(--color-ts-class);
}
.tsd-index-panel .tsd-is-private a {
    color: var(--color-ts-private);
}

.tsd-flag {
    display: inline-block;
    padding: 1px 5px;
    border-radius: 4px;
    color: var(--color-comment-tag-text);
    background-color: var(--color-comment-tag);
    text-indent: 0;
    font-size: 14px;
    font-weight: normal;
}

.tsd-anchor {
    position: absolute;
    top: -100px;
}

.tsd-member {
    position: relative;
}
.tsd-member .tsd-anchor + h3 {
    margin-top: 0;
    margin-bottom: 0;
    border-bottom: none;
}
.tsd-member [data-tsd-kind] {
    color: var(--color-ts);
}
.tsd-member [data-tsd-kind="Interface"] {
    color: var(--color-ts-interface);
}
.tsd-member [data-tsd-kind="Enum"] {
    color: var(--color-ts-enum);
}
.tsd-member [data-tsd-kind="Class"] {
    color: var(--color-ts-class);
}
.tsd-member [data-tsd-kind="Private"] {
    color: var(--color-ts-private);
}

.tsd-navigation {
    margin: 0 0 0 40px;
}
.tsd-navigation a {
    display: block;
    padding-top: 2px;
    padding-bottom: 2px;
    border-left: 2px solid transparent;
    color: var(--color-text);
    text-decoration: none;
    transition: border-left-color 0.1s;
}
.tsd-navigation a:hover {
    text-decoration: underline;
}
.tsd-navigation ul {
    margin: 0;
    padding: 0;
    list-style: none;
}
.tsd-navigation li {
    padding: 0;
}

.tsd-navigation.primary {
    padding-bottom: 40px;
}
.tsd-navigation.primary a {
    display: block;
    padding-top: 6px;
    padding-bottom: 6px;
}
.tsd-navigation.primary ul li a {
    padding-left: 5px;
}
.tsd-navigation.primary ul li li a {
    padding-left: 25px;
}
.tsd-navigation.primary ul li li li a {
    padding-left: 45px;
}
.tsd-navigation.primary ul li li li li a {
    padding-left: 65px;
}
.tsd-navigation.primary ul li li li li li a {
    padding-left: 85px;
}
.tsd-navigation.primary ul li li li li li li a {
    padding-left: 105px;
}
.tsd-navigation.primary > ul {
    border-bottom: 1px solid var(--color-panel-divider);
}
.tsd-navigation.primary li {
    border-top: 1px solid var(--color-panel-divider);
}
.tsd-navigation.primary li.current > a {
    font-weight: bold;
}
.tsd-navigation.primary li.label span {
    display: block;
    padding: 20px 0 6px 5px;
    color: var(--color-menu-label);
}
.tsd-navigation.primary li.globals + li > span,
.tsd-navigation.primary li.globals + li > a {
    padding-top: 20px;
}

.tsd-navigation.secondary {
    max-height: calc(100vh - 1rem - 40px);
    overflow: auto;
    position: sticky;
    top: calc(0.5rem + 40px);
    transition: 0.3s;
}
.tsd-navigation.secondary.tsd-navigation--toolbar-hide {
    max-height: calc(100vh - 1rem);
    top: 0.5rem;
}
.tsd-navigation.secondary ul {
    transition: opacity 0.2s;
}
.tsd-navigation.secondary ul li a {
    padding-left: 25px;
}
.tsd-navigation.secondary ul li li a {
    padding-left: 45px;
}
.tsd-navigation.secondary ul li li li a {
    padding-left: 65px;
}
.tsd-navigation.secondary ul li li li li a {
    padding-left: 85px;
}
.tsd-navigation.secondary ul li li li li li a {
    padding-left: 105px;
}
.tsd-navigation.secondary ul li li li li li li a {
    padding-left: 125px;
}
.tsd-navigation.secondary ul.current a {
    border-left-color: var(--color-panel-divider);
}
.tsd-navigation.secondary li.focus > a,
.tsd-navigation.secondary ul.current li.focus > a {
    border-left-color: var(--color-menu-divider-focus);
}
.tsd-navigation.secondary li.current {
    margin-top: 20px;
    margin-bottom: 20px;
    border-left-color: var(--color-panel-divider);
}
.tsd-navigation.secondary li.current > a {
    font-weight: bold;
}

@media (min-width: 901px) {
    .menu-sticky-wrap {
        position: static;
    }
}

.tsd-panel {
    margin: 20px 0;
    padding: 20px;
    background-color: var(--color-panel);
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
}
.tsd-panel:empty {
    display: none;
}
.tsd-panel > h1,
.tsd-panel > h2,
.tsd-panel > h3 {
    margin: 1.5em -20px 10px -20px;
    padding: 0 20px 10px 20px;
    border-bottom: 1px solid var(--color-panel-divider);
}
.tsd-panel > h1.tsd-before-signature,
.tsd-panel > h2.tsd-before-signature,
.tsd-panel > h3.tsd-before-signature {
    margin-bottom: 0;
    border-bottom: 0;
}
.tsd-panel table {
    display: block;
    width: 100%;
    overflow: auto;
    margin-top: 10px;
    word-break: normal;
    word-break: keep-all;
    border-collapse: collapse;
}
.tsd-panel table th {
    font-weight: bold;
}
.tsd-panel table th,
.tsd-panel table td {
    padding: 6px 13px;
    border: 1px solid var(--color-panel-divider);
}
.tsd-panel table tr {
    background: var(--color-background);
}
.tsd-panel table tr:nth-child(even) {
    background: var(--color-secondary-background);
}

.tsd-panel-group {
    margin: 60px 0;
}
.tsd-panel-group > h1,
.tsd-panel-group > h2,
.tsd-panel-group > h3 {
    padding-left: 20px;
    padding-right: 20px;
}

#tsd-search {
    transition: background-color 0.2s;
}
#tsd-search .title {
    position: relative;
    z-index: 2;
}
#tsd-search .field {
    position: absolute;
    left: 0;
    top: 0;
    right: 40px;
    height: 40px;
}
#tsd-search .field input {
    box-sizing: border-box;
    position: relative;
    top: -50px;
    z-index: 1;
    width: 100%;
    padding: 0 10px;
    opacity: 0;
    outline: 0;
    border: 0;
    background: transparent;
    color: var(--color-text);
}
#tsd-search .field label {
    position: absolute;
    overflow: hidden;
    right: -40px;
}
#tsd-search .field input,
#tsd-search .title {
    transition: opacity 0.2s;
}
#tsd-search .results {
    position: absolute;
    visibility: hidden;
    top: 40px;
    width: 100%;
    margin: 0;
    padding: 0;
    list-style: none;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
}
#tsd-search .results li {
    padding: 0 10px;
    background-color: var(--color-background);
}
#tsd-search .results li:nth-child(even) {
    background-color: var(--color-panel);
}
#tsd-search .results li.state {
    display: none;
}
#tsd-search .results li.current,
#tsd-search .results li:hover {
    background-color: var(--color-panel-divider);
}
#tsd-search .results a {
    display: block;
}
#tsd-search .results a:before {
    top: 10px;
}
#tsd-search .results span.parent {
    color: var(--color-text-aside);
    font-weight: normal;
}
#tsd-search.has-focus {
    background-color: var(--color-panel-divider);
}
#tsd-search.has-focus .field input {
    top: 0;
    opacity: 1;
}
#tsd-search.has-focus .title {
    z-index: 0;
    opacity: 0;
}
#tsd-search.has-focus .results {
    visibility: visible;
}
#tsd-search.loading .results li.state.loading {
    display: block;
}
#tsd-search.failure .results li.state.failure {
    display: block;
}

.tsd-signature {
    margin: 0 0 1em 0;
    padding: 10px;
    border: 1px solid var(--color-panel-divider);
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
    font-size: 14px;
    overflow-x: auto;
}
.tsd-signature.tsd-kind-icon {
    padding-left: 30px;
}
.tsd-signature.tsd-kind-icon:before {
    top: 10px;
    left: 10px;
}
.tsd-panel > .tsd-signature {
    margin-left: -20px;
    margin-right: -20px;
    border-width: 1px 0;
}
.tsd-panel > .tsd-signature.tsd-kind-icon {
    padding-left: 40px;
}
.tsd-panel > .tsd-signature.tsd-kind-icon:before {
    left: 20px;
}

.tsd-signature-symbol {
    color: var(--color-text-aside);
    font-weight: normal;
}

.tsd-signature-type {
    font-style: italic;
    font-weight: normal;
}

.tsd-signatures {
    padding: 0;
    margin: 0 0 1em 0;
    border: 1px solid var(--color-panel-divider);
}
.tsd-signatures .tsd-signature {
    margin: 0;
    border-width: 1px 0 0 0;
    transition: background-color 0.1s;
}
.tsd-signatures .tsd-signature:first-child {
    border-top-width: 0;
}
.tsd-signatures .tsd-signature.current {
    background-color: var(--color-panel-divider);
}
.tsd-signatures.active > .tsd-signature {
    cursor: pointer;
}
.tsd-panel > .tsd-signatures {
    margin-left: -20px;
    margin-right: -20px;
    border-width: 1px 0;
}
.tsd-panel > .tsd-signatures .tsd-signature.tsd-kind-icon {
    padding-left: 40px;
}
.tsd-panel > .tsd-signatures .tsd-signature.tsd-kind-icon:before {
    left: 20px;
}
.tsd-panel > a.anchor + .tsd-signatures {
    border-top-width: 0;
    margin-top: -20px;
}

ul.tsd-descriptions {
    position: relative;
    overflow: hidden;
    padding: 0;
    list-style: none;
}
ul.tsd-descriptions.active > .tsd-description {
    display: none;
}
ul.tsd-descriptions.active > .tsd-description.current {
    display: block;
}
ul.tsd-descriptions.active > .tsd-description.fade-in {
    animation: fade-in-delayed 0.3s;
}
ul.tsd-descriptions.active > .tsd-description.fade-out {
    animation: fade-out-delayed 0.3s;
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    right: 0;
    opacity: 0;
    visibility: hidden;
}
ul.tsd-descriptions h4,
ul.tsd-descriptions .tsd-index-panel h3,
.tsd-index-panel ul.tsd-descriptions h3 {
    font-size: 16px;
    margin: 1em 0 0.5em 0;
}

ul.tsd-parameters,
ul.tsd-type-parameters {
    list-style: square;
    margin: 0;
    padding-left: 20px;
}
ul.tsd-parameters > li.tsd-parameter-signature,
ul.tsd-type-parameters > li.tsd-parameter-signature {
    list-style: none;
    margin-left: -20px;
}
ul.tsd-parameters h5,
ul.tsd-type-parameters h5 {
    font-size: 16px;
    margin: 1em 0 0.5em 0;
}
ul.tsd-parameters .tsd-comment,
ul.tsd-type-parameters .tsd-comment {
    margin-top: -0.5em;
}

.tsd-sources {
    font-size: 14px;
    color: var(--color-text-aside);
    margin: 0 0 1em 0;
}
.tsd-sources a {
    color: var(--color-text-aside);
    text-decoration: underline;
}
.tsd-sources ul,
.tsd-sources p {
    margin: 0 !important;
}
.tsd-sources ul {
    list-style: none;
    padding: 0;
}

.tsd-page-toolbar {
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    color: var(--color-toolbar-text);
    background: var(--color-toolbar);
    border-bottom: 1px solid var(--color-panel-divider);
    transition: transform 0.3s linear;
}
.tsd-page-toolbar a {
    color: var(--color-toolbar-text);
    text-decoration: none;
}
.tsd-page-toolbar a.title {
    font-weight: bold;
}
.tsd-page-toolbar a.title:hover {
    text-decoration: underline;
}
.tsd-page-toolbar .table-wrap {
    display: table;
    width: 100%;
    height: 40px;
}
.tsd-page-toolbar .table-cell {
    display: table-cell;
    position: relative;
    white-space: nowrap;
    line-height: 40px;
}
.tsd-page-toolbar .table-cell:first-child {
    width: 100%;
}

.tsd-page-toolbar--hide {
    transform: translateY(-100%);
}

.tsd-select .tsd-select-list li:before,
.tsd-select .tsd-select-label:before,
.tsd-widget:before {
    content: "";
    display: inline-block;
    width: 40px;
    height: 40px;
    margin: 0 -8px 0 0;
    background-image: url(./widgets.png);
    background-repeat: no-repeat;
    text-indent: -1024px;
    vertical-align: bottom;
    filter: var(--icon-filter);
}
@media (-webkit-min-device-pixel-ratio: 1.5), (min-resolution: 144dpi) {
    .tsd-select .tsd-select-list li:before,
    .tsd-select .tsd-select-label:before,
    .tsd-widget:before {
        background-image: url(./<EMAIL>);
        background-size: 320px 40px;
    }
}

.tsd-widget {
    display: inline-block;
    overflow: hidden;
    opacity: 0.8;
    height: 40px;
    transition: opacity 0.1s, background-color 0.2s;
    vertical-align: bottom;
    cursor: pointer;
}
.tsd-widget:hover {
    opacity: 0.9;
}
.tsd-widget.active {
    opacity: 1;
    background-color: var(--color-panel-divider);
}
.tsd-widget.no-caption {
    width: 40px;
}
.tsd-widget.no-caption:before {
    margin: 0;
}
.tsd-widget.search:before {
    background-position: 0 0;
}
.tsd-widget.menu:before {
    background-position: -40px 0;
}
.tsd-widget.options:before {
    background-position: -80px 0;
}
.tsd-widget.options,
.tsd-widget.menu {
    display: none;
}
@media (max-width: 900px) {
    .tsd-widget.options,
    .tsd-widget.menu {
        display: inline-block;
    }
}
input[type="checkbox"] + .tsd-widget:before {
    background-position: -120px 0;
}
input[type="checkbox"]:checked + .tsd-widget:before {
    background-position: -160px 0;
}

.tsd-select {
    position: relative;
    display: inline-block;
    height: 40px;
    transition: opacity 0.1s, background-color 0.2s;
    vertical-align: bottom;
    cursor: pointer;
}
.tsd-select .tsd-select-label {
    opacity: 0.6;
    transition: opacity 0.2s;
}
.tsd-select .tsd-select-label:before {
    background-position: -240px 0;
}
.tsd-select.active .tsd-select-label {
    opacity: 0.8;
}
.tsd-select.active .tsd-select-list {
    visibility: visible;
    opacity: 1;
    transition-delay: 0s;
}
.tsd-select .tsd-select-list {
    position: absolute;
    visibility: hidden;
    top: 40px;
    left: 0;
    margin: 0;
    padding: 0;
    opacity: 0;
    list-style: none;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.25);
    transition: visibility 0s 0.2s, opacity 0.2s;
}
.tsd-select .tsd-select-list li {
    padding: 0 20px 0 0;
    background-color: var(--color-background);
}
.tsd-select .tsd-select-list li:before {
    background-position: 40px 0;
}
.tsd-select .tsd-select-list li:nth-child(even) {
    background-color: var(--color-panel);
}
.tsd-select .tsd-select-list li:hover {
    background-color: var(--color-panel-divider);
}
.tsd-select .tsd-select-list li.selected:before {
    background-position: -200px 0;
}
@media (max-width: 900px) {
    .tsd-select .tsd-select-list {
        top: 0;
        left: auto;
        right: 100%;
        margin-right: -5px;
    }
    .tsd-select .tsd-select-label:before {
        background-position: -280px 0;
    }
}

img {
    max-width: 100%;
}
