<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>SectionPlanePlugin | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="SectionPlanePlugin.html">SectionPlanePlugin</a></li></ul><h1>Class SectionPlanePlugin</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="../modules/math.html" class="tsd-signature-type" data-tsd-kind="Namespace">math</a><ul class="tsd-hierarchy"><li><span class="target">SectionPlanePlugin</span></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="SectionPlanePlugin.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_control" class="tsd-kind-icon">_control</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_inputSubIds" class="tsd-kind-icon">_input<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_needChangeSectionPlane" class="tsd-kind-icon">_need<wbr/>Change<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_popPanel" class="tsd-kind-icon">_pop<wbr/>Panel</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_sectionPlane" class="tsd-kind-icon">_section<wbr/>Plane</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_tooltip" class="tsd-kind-icon">_tooltip</a></li></ul></section><section class="tsd-index-section "><h3>Accessors</h3><ul class="tsd-index-list"><li class="tsd-kind-accessor tsd-parent-kind-class"><a href="SectionPlanePlugin.html#active" class="tsd-kind-icon">active</a></li><li class="tsd-kind-accessor tsd-parent-kind-class"><a href="SectionPlanePlugin.html#visible" class="tsd-kind-icon">visible</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activeAndVisble" class="tsd-kind-icon">active<wbr/>And<wbr/>Visble</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activePopPanel" class="tsd-kind-icon">active<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activeSectionPlane" class="tsd-kind-icon">active<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activeTooltip" class="tsd-kind-icon">active<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#createPopPanel" class="tsd-kind-icon">create<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#createSectionPlane" class="tsd-kind-icon">create<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#createTooltip" class="tsd-kind-icon">create<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePlugin.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#initSectionPlane" class="tsd-kind-icon">init<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#removePopPanel" class="tsd-kind-icon">remove<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#removeTooltip" class="tsd-kind-icon">remove<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePlugin.html#reset" class="tsd-kind-icon">reset</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Section<wbr/>Plane<wbr/>Plugin<span class="tsd-signature-symbol">(</span>viewer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, cfg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">SectionPlaneConfig</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="SectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides Plugin.constructor</p><ul><li>Defined in plugins/SectionPlanePlugin.ts:28</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>viewer: <span class="tsd-signature-type">any</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> cfg: <span class="tsd-signature-type">SectionPlaneConfig</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="SectionPlanePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePlugin</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_active" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _active</h3><div class="tsd-signature tsd-kind-icon">_active<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:20</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_control" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _control</h3><div class="tsd-signature tsd-kind-icon">_control<span class="tsd-signature-symbol">:</span> <a href="PlaneControl.html" class="tsd-signature-type" data-tsd-kind="Class">PlaneControl</a></div><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:22</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_inputSubIds" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _input<wbr/>Sub<wbr/>Ids</h3><div class="tsd-signature tsd-kind-icon">_input<wbr/>Sub<wbr/>Ids<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:24</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_needChangeSectionPlane" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _need<wbr/>Change<wbr/>Section<wbr/>Plane</h3><div class="tsd-signature tsd-kind-icon">_need<wbr/>Change<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:23</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_popPanel" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _pop<wbr/>Panel</h3><div class="tsd-signature tsd-kind-icon">_pop<wbr/>Panel<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="SectionPlanePopPanel.html" class="tsd-signature-type" data-tsd-kind="Class">SectionPlanePopPanel</a></div><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:26</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_sectionPlane" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _section<wbr/>Plane</h3><div class="tsd-signature tsd-kind-icon">_section<wbr/>Plane<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span><span class="tsd-signature-symbol"> = undefined</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:21</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_tooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _tooltip</h3><div class="tsd-signature tsd-kind-icon">_tooltip<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><a href="Tooltip.html" class="tsd-signature-type" data-tsd-kind="Class">Tooltip</a></div><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:25</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Accessors</h2><section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class"><a id="active" class="tsd-anchor"></a><h3>active</h3><ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> active<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> active<span class="tsd-signature-symbol">(</span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:56</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:43</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>value: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-accessor tsd-parent-kind-class"><a id="visible" class="tsd-anchor"></a><h3>visible</h3><ul class="tsd-signatures tsd-kind-accessor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> visible<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> visible<span class="tsd-signature-symbol">(</span>value<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:67</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:60</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>value: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="activeAndVisble" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> active<wbr/>And<wbr/>Visble</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">active<wbr/>And<wbr/>Visble<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:77</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="activePopPanel" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> active<wbr/>Pop<wbr/>Panel</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">active<wbr/>Pop<wbr/>Panel<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:167</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="activeSectionPlane" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> active<wbr/>Section<wbr/>Plane</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">active<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:82</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="activeTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> active<wbr/>Tooltip</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">active<wbr/>Tooltip<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:148</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createPopPanel" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Pop<wbr/>Panel</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Pop<wbr/>Panel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:171</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createSectionPlane" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Section<wbr/>Plane</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">(</span>params<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">CreateSectionPlaneParams</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:88</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>params: <span class="tsd-signature-type">CreateSectionPlaneParams</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Tooltip</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Tooltip<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:152</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:222</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="initSectionPlane" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init<wbr/>Section<wbr/>Plane</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:105</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="removePopPanel" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> remove<wbr/>Pop<wbr/>Panel</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">remove<wbr/>Pop<wbr/>Panel<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:217</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="removeTooltip" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> remove<wbr/>Tooltip</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">remove<wbr/>Tooltip<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:162</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="reset" class="tsd-anchor"></a><h3>reset</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">reset<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/SectionPlanePlugin.ts:71</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="SectionPlanePlugin.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Plugin</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="SectionPlanePlugin.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_control" class="tsd-kind-icon">_control</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_inputSubIds" class="tsd-kind-icon">_input<wbr/>Sub<wbr/>Ids</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_needChangeSectionPlane" class="tsd-kind-icon">_need<wbr/>Change<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_popPanel" class="tsd-kind-icon">_pop<wbr/>Panel</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_sectionPlane" class="tsd-kind-icon">_section<wbr/>Plane</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#_tooltip" class="tsd-kind-icon">_tooltip</a></li><li class="tsd-kind-accessor tsd-parent-kind-class"><a href="SectionPlanePlugin.html#active" class="tsd-kind-icon">active</a></li><li class="tsd-kind-accessor tsd-parent-kind-class"><a href="SectionPlanePlugin.html#visible" class="tsd-kind-icon">visible</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activeAndVisble" class="tsd-kind-icon">active<wbr/>And<wbr/>Visble</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activePopPanel" class="tsd-kind-icon">active<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activeSectionPlane" class="tsd-kind-icon">active<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#activeTooltip" class="tsd-kind-icon">active<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#createPopPanel" class="tsd-kind-icon">create<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#createSectionPlane" class="tsd-kind-icon">create<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#createTooltip" class="tsd-kind-icon">create<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePlugin.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#initSectionPlane" class="tsd-kind-icon">init<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#removePopPanel" class="tsd-kind-icon">remove<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="SectionPlanePlugin.html#removeTooltip" class="tsd-kind-icon">remove<wbr/>Tooltip</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="SectionPlanePlugin.html#reset" class="tsd-kind-icon">reset</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>