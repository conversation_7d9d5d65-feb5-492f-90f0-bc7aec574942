<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Path | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="Path.html">Path</a></li></ul><h1>Class Path</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="CurvePath.html" class="tsd-signature-type" data-tsd-kind="Class">CurvePath</a><ul class="tsd-hierarchy"><li><span class="target">Path</span><ul class="tsd-hierarchy"><li><a href="Shape.html" class="tsd-signature-type" data-tsd-kind="Class">Shape</a></li></ul></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_arcLengthDivisions" class="tsd-kind-icon">_arc<wbr/>Length<wbr/>Divisions</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_autoClose" class="tsd-kind-icon">_auto<wbr/>Close</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_cachedArcLengths" class="tsd-kind-icon">_cached<wbr/>Arc<wbr/>Lengths</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_cachedLengths" class="tsd-kind-icon">_cached<wbr/>Lengths</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="Path.html#_currentPoint" class="tsd-kind-icon">_current<wbr/>Point</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_curves" class="tsd-kind-icon">_curves</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_needsUpdate" class="tsd-kind-icon">_needs<wbr/>Update</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_type" class="tsd-kind-icon">_type</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#absarc" class="tsd-kind-icon">absarc</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#absellipse" class="tsd-kind-icon">absellipse</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#add" class="tsd-kind-icon">add</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#arc" class="tsd-kind-icon">arc</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#bezierCurveTo" class="tsd-kind-icon">bezier<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#closePath" class="tsd-kind-icon">close<wbr/>Path</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#computeFrenetFrames" class="tsd-kind-icon">compute<wbr/>Frenet<wbr/>Frames</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#copy" class="tsd-kind-icon">copy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#ellipse" class="tsd-kind-icon">ellipse</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#fromJSON" class="tsd-kind-icon">fromJSON</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getCurveLengths" class="tsd-kind-icon">get<wbr/>Curve<wbr/>Lengths</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#getCurves" class="tsd-kind-icon">get<wbr/>Curves</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getLength" class="tsd-kind-icon">get<wbr/>Length</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getLengths" class="tsd-kind-icon">get<wbr/>Lengths</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getPoint" class="tsd-kind-icon">get<wbr/>Point</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getPointAt" class="tsd-kind-icon">get<wbr/>Point<wbr/>At</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getPoints" class="tsd-kind-icon">get<wbr/>Points</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getSpacedPoints" class="tsd-kind-icon">get<wbr/>Spaced<wbr/>Points</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getTangent" class="tsd-kind-icon">get<wbr/>Tangent</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getTangentAt" class="tsd-kind-icon">get<wbr/>Tangent<wbr/>At</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getType" class="tsd-kind-icon">get<wbr/>Type</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getUtoTmapping" class="tsd-kind-icon">get<wbr/>Uto<wbr/>Tmapping</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#lineTo" class="tsd-kind-icon">line<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#moveTo" class="tsd-kind-icon">move<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#quadraticCurveTo" class="tsd-kind-icon">quadratic<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#setCurves" class="tsd-kind-icon">set<wbr/>Curves</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#setFromPoints" class="tsd-kind-icon">set<wbr/>From<wbr/>Points</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#splineThru" class="tsd-kind-icon">spline<wbr/>Thru</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#toJSON" class="tsd-kind-icon">toJSON</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#updateArcLengths" class="tsd-kind-icon">update<wbr/>Arc<wbr/>Lengths</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Path<span class="tsd-signature-symbol">(</span>points<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#constructor">constructor</a></p><ul><li>Defined in core/paths/Path.ts:15</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> points: <span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_arcLengthDivisions" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _arc<wbr/>Length<wbr/>Divisions</h3><div class="tsd-signature tsd-kind-icon">_arc<wbr/>Length<wbr/>Divisions<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#_arcLengthDivisions">_arcLengthDivisions</a></p><ul><li>Defined in core/paths/Curve.ts:39</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_autoClose" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _auto<wbr/>Close</h3><div class="tsd-signature tsd-kind-icon">_auto<wbr/>Close<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#_autoClose">_autoClose</a></p><ul><li>Defined in core/paths/CurvePath.ts:16</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_cachedArcLengths" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _cached<wbr/>Arc<wbr/>Lengths</h3><div class="tsd-signature tsd-kind-icon">_cached<wbr/>Arc<wbr/>Lengths<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = []</span></div><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#_cachedArcLengths">_cachedArcLengths</a></p><ul><li>Defined in core/paths/Curve.ts:40</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_cachedLengths" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _cached<wbr/>Lengths</h3><div class="tsd-signature tsd-kind-icon">_cached<wbr/>Lengths<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#_cachedLengths">_cachedLengths</a></p><ul><li>Defined in core/paths/CurvePath.ts:17</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a id="_currentPoint" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _current<wbr/>Point</h3><div class="tsd-signature tsd-kind-icon">_current<wbr/>Point<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">vec2</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:13</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_curves" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _curves</h3><div class="tsd-signature tsd-kind-icon">_curves<span class="tsd-signature-symbol">:</span> <a href="Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#_curves">_curves</a></p><ul><li>Defined in core/paths/CurvePath.ts:15</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_needsUpdate" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _needs<wbr/>Update</h3><div class="tsd-signature tsd-kind-icon">_needs<wbr/>Update<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = true</span></div><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#_needsUpdate">_needsUpdate</a></p><ul><li>Defined in core/paths/Curve.ts:41</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a id="_type" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _type</h3><div class="tsd-signature tsd-kind-icon">_type<span class="tsd-signature-symbol">:</span> <a href="../enums/CurveType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">CurveType</a></div><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#_type">_type</a></p><ul><li>Defined in core/paths/Curve.ts:38</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="absarc" class="tsd-anchor"></a><h3>absarc</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">absarc<span class="tsd-signature-symbol">(</span>aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aRadius<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aStartAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aEndAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aClockwise<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:108</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li><li><h5>aRadius: <span class="tsd-signature-type">number</span></h5></li><li><h5>aStartAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5>aEndAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> aClockwise: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="absellipse" class="tsd-anchor"></a><h3>absellipse</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">absellipse<span class="tsd-signature-symbol">(</span>aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, xRadius<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, yRadius<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aStartAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aEndAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aClockwise<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span>, aRotation<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:132</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li><li><h5>xRadius: <span class="tsd-signature-type">number</span></h5></li><li><h5>yRadius: <span class="tsd-signature-type">number</span></h5></li><li><h5>aStartAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5>aEndAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> aClockwise: <span class="tsd-signature-type">boolean</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> aRotation: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="add" class="tsd-anchor"></a><h3>add</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">add<span class="tsd-signature-symbol">(</span>curve<span class="tsd-signature-symbol">: </span><a href="Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#add">add</a></p><ul><li>Defined in core/paths/CurvePath.ts:28</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>curve: <a href="Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="arc" class="tsd-anchor"></a><h3>arc</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">arc<span class="tsd-signature-symbol">(</span>aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aRadius<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aStartAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aEndAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aClockwise<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:99</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li><li><h5>aRadius: <span class="tsd-signature-type">number</span></h5></li><li><h5>aStartAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5>aEndAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> aClockwise: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="bezierCurveTo" class="tsd-anchor"></a><h3>bezier<wbr/>Curve<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">bezier<wbr/>Curve<wbr/>To<span class="tsd-signature-symbol">(</span>aCP1x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCP1y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCP2x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCP2y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:73</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aCP1x: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCP1y: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCP2x: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCP2y: <span class="tsd-signature-type">number</span></h5></li><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="closePath" class="tsd-anchor"></a><h3>close<wbr/>Path</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">close<wbr/>Path<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#closePath">closePath</a></p><ul><li>Defined in core/paths/CurvePath.ts:32</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="computeFrenetFrames" class="tsd-anchor"></a><h3>compute<wbr/>Frenet<wbr/>Frames</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">compute<wbr/>Frenet<wbr/>Frames<span class="tsd-signature-symbol">(</span>segments<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, closed<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>binormals<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>normals<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tangents<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#computeFrenetFrames">computeFrenetFrames</a></p><ul><li>Defined in core/paths/Curve.ts:242</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>segments: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> closed: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>binormals<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>normals<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">; </span>tangents<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> }</span></h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5>binormals<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span></h5></li><li class="tsd-parameter"><h5>normals<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span></h5></li><li class="tsd-parameter"><h5>tangents<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec3</span><span class="tsd-signature-symbol">[]</span></h5></li></ul></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a id="copy" class="tsd-anchor"></a><h3>copy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">copy<span class="tsd-signature-symbol">(</span>source<span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#copy">copy</a></p><ul><li>Defined in core/paths/Path.ts:161</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>source: <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="ellipse" class="tsd-anchor"></a><h3>ellipse</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">ellipse<span class="tsd-signature-symbol">(</span>aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, xRadius<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, yRadius<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aStartAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aEndAngle<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aClockwise<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span>, aRotation<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:114</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li><li><h5>xRadius: <span class="tsd-signature-type">number</span></h5></li><li><h5>yRadius: <span class="tsd-signature-type">number</span></h5></li><li><h5>aStartAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5>aEndAngle: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> aClockwise: <span class="tsd-signature-type">boolean</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> aRotation: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a id="fromJSON" class="tsd-anchor"></a><h3>fromJSON</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">fromJSON<span class="tsd-signature-symbol">(</span>json<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#fromJSON">fromJSON</a></p><ul><li>Defined in core/paths/Path.ts:177</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>json: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getCurveLengths" class="tsd-anchor"></a><h3>get<wbr/>Curve<wbr/>Lengths</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Curve<wbr/>Lengths<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getCurveLengths">getCurveLengths</a></p><ul><li>Defined in core/paths/CurvePath.ts:103</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="getCurves" class="tsd-anchor"></a><h3>get<wbr/>Curves</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">get<wbr/>Curves<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:40</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getLength" class="tsd-anchor"></a><h3>get<wbr/>Length</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Length<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getLength">getLength</a></p><ul><li>Defined in core/paths/CurvePath.ts:88</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getLengths" class="tsd-anchor"></a><h3>get<wbr/>Lengths</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Lengths<span class="tsd-signature-symbol">(</span>divisions<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getLengths">getLengths</a></p><ul><li>Defined in core/paths/Curve.ts:99</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>divisions: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = ...</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getPoint" class="tsd-anchor"></a><h3>get<wbr/>Point</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Point<span class="tsd-signature-symbol">(</span>t<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getPoint">getPoint</a></p><ul><li>Defined in core/paths/CurvePath.ts:56</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>t: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getPointAt" class="tsd-anchor"></a><h3>get<wbr/>Point<wbr/>At</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Point<wbr/>At<span class="tsd-signature-symbol">(</span>u<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, optionalTarget<span class="tsd-signature-symbol">?: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getPointAt">getPointAt</a></p><ul><li>Defined in core/paths/Curve.ts:61</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>u: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> optionalTarget: <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getPoints" class="tsd-anchor"></a><h3>get<wbr/>Points</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Points<span class="tsd-signature-symbol">(</span>divisions<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getPoints">getPoints</a></p><ul><li>Defined in core/paths/CurvePath.ts:140</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>divisions: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 12</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">[]</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getSpacedPoints" class="tsd-anchor"></a><h3>get<wbr/>Spaced<wbr/>Points</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Spaced<wbr/>Points<span class="tsd-signature-symbol">(</span>divisions<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Float32Array</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getSpacedPoints">getSpacedPoints</a></p><ul><li>Defined in core/paths/CurvePath.ts:126</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>divisions: <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> = 40</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">Float32Array</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">[]</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getTangent" class="tsd-anchor"></a><h3>get<wbr/>Tangent</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Tangent<span class="tsd-signature-symbol">(</span>t<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, optionalTarget<span class="tsd-signature-symbol">?: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getTangent">getTangent</a></p><ul><li>Defined in core/paths/Curve.ts:202</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>t: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> optionalTarget: <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getTangentAt" class="tsd-anchor"></a><h3>get<wbr/>Tangent<wbr/>At</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Tangent<wbr/>At<span class="tsd-signature-symbol">(</span>u<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, optionalTarget<span class="tsd-signature-symbol">?: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getTangentAt">getTangentAt</a></p><ul><li>Defined in core/paths/Curve.ts:237</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>u: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> optionalTarget: <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getType" class="tsd-anchor"></a><h3>get<wbr/>Type</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Type<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../enums/CurveType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">CurveType</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getType">getType</a></p><ul><li>Defined in core/paths/Curve.ts:49</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="../enums/CurveType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">CurveType</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="getUtoTmapping" class="tsd-anchor"></a><h3>get<wbr/>Uto<wbr/>Tmapping</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">get<wbr/>Uto<wbr/>Tmapping<span class="tsd-signature-symbol">(</span>u<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, distance<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#getUtoTmapping">getUtoTmapping</a></p><ul><li>Defined in core/paths/Curve.ts:136</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>u: <span class="tsd-signature-type">number</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> distance: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="lineTo" class="tsd-anchor"></a><h3>line<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">line<wbr/>To<span class="tsd-signature-symbol">(</span>x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:50</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>x: <span class="tsd-signature-type">number</span></h5></li><li><h5>y: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="moveTo" class="tsd-anchor"></a><h3>move<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">move<wbr/>To<span class="tsd-signature-symbol">(</span>x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:44</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>x: <span class="tsd-signature-type">number</span></h5></li><li><h5>y: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="quadraticCurveTo" class="tsd-anchor"></a><h3>quadratic<wbr/>Curve<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">quadratic<wbr/>Curve<wbr/>To<span class="tsd-signature-symbol">(</span>aCPx<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCPy<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:59</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aCPx: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCPy: <span class="tsd-signature-type">number</span></h5></li><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setCurves" class="tsd-anchor"></a><h3>set<wbr/>Curves</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Curves<span class="tsd-signature-symbol">(</span>value<span class="tsd-signature-symbol">: </span><a href="Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:36</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>value: <a href="Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="../modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setFromPoints" class="tsd-anchor"></a><h3>set<wbr/>From<wbr/>Points</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>From<wbr/>Points<span class="tsd-signature-symbol">(</span>points<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:26</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>points: <span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="splineThru" class="tsd-anchor"></a><h3>spline<wbr/>Thru</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">spline<wbr/>Thru<span class="tsd-signature-symbol">(</span>pts<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Path.ts:88</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>pts: <span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a id="toJSON" class="tsd-anchor"></a><h3>toJSON</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">toJSON<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#toJSON">toJSON</a></p><ul><li>Defined in core/paths/Path.ts:169</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">any</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a id="updateArcLengths" class="tsd-anchor"></a><h3>update<wbr/>Arc<wbr/>Lengths</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><li class="tsd-signature tsd-kind-icon">update<wbr/>Arc<wbr/>Lengths<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Inherited from <a href="CurvePath.html">CurvePath</a>.<a href="CurvePath.html#updateArcLengths">updateArcLengths</a></p><ul><li>Defined in core/paths/CurvePath.ts:94</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="Path.html" class="tsd-kind-icon">Path</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_arcLengthDivisions" class="tsd-kind-icon">_arc<wbr/>Length<wbr/>Divisions</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_autoClose" class="tsd-kind-icon">_auto<wbr/>Close</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_cachedArcLengths" class="tsd-kind-icon">_cached<wbr/>Arc<wbr/>Lengths</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_cachedLengths" class="tsd-kind-icon">_cached<wbr/>Lengths</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="Path.html#_currentPoint" class="tsd-kind-icon">_current<wbr/>Point</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_curves" class="tsd-kind-icon">_curves</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_needsUpdate" class="tsd-kind-icon">_needs<wbr/>Update</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited tsd-is-protected"><a href="Path.html#_type" class="tsd-kind-icon">_type</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#absarc" class="tsd-kind-icon">absarc</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#absellipse" class="tsd-kind-icon">absellipse</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#add" class="tsd-kind-icon">add</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#arc" class="tsd-kind-icon">arc</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#bezierCurveTo" class="tsd-kind-icon">bezier<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#closePath" class="tsd-kind-icon">close<wbr/>Path</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#computeFrenetFrames" class="tsd-kind-icon">compute<wbr/>Frenet<wbr/>Frames</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#copy" class="tsd-kind-icon">copy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#ellipse" class="tsd-kind-icon">ellipse</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#fromJSON" class="tsd-kind-icon">fromJSON</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getCurveLengths" class="tsd-kind-icon">get<wbr/>Curve<wbr/>Lengths</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#getCurves" class="tsd-kind-icon">get<wbr/>Curves</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getLength" class="tsd-kind-icon">get<wbr/>Length</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getLengths" class="tsd-kind-icon">get<wbr/>Lengths</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getPoint" class="tsd-kind-icon">get<wbr/>Point</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getPointAt" class="tsd-kind-icon">get<wbr/>Point<wbr/>At</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getPoints" class="tsd-kind-icon">get<wbr/>Points</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getSpacedPoints" class="tsd-kind-icon">get<wbr/>Spaced<wbr/>Points</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getTangent" class="tsd-kind-icon">get<wbr/>Tangent</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getTangentAt" class="tsd-kind-icon">get<wbr/>Tangent<wbr/>At</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getType" class="tsd-kind-icon">get<wbr/>Type</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#getUtoTmapping" class="tsd-kind-icon">get<wbr/>Uto<wbr/>Tmapping</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#lineTo" class="tsd-kind-icon">line<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#moveTo" class="tsd-kind-icon">move<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#quadraticCurveTo" class="tsd-kind-icon">quadratic<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#setCurves" class="tsd-kind-icon">set<wbr/>Curves</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#setFromPoints" class="tsd-kind-icon">set<wbr/>From<wbr/>Points</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Path.html#splineThru" class="tsd-kind-icon">spline<wbr/>Thru</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-overwrite"><a href="Path.html#toJSON" class="tsd-kind-icon">toJSON</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><a href="Path.html#updateArcLengths" class="tsd-kind-icon">update<wbr/>Arc<wbr/>Lengths</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>