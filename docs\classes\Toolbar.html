<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Toolbar | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="Toolbar.html">Toolbar</a></li></ul><h1>Class Toolbar</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>A customized toolbar.</p>
<p>For example:</p>

<a href="#example-1" id="example-1" style="color: inherit; text-decoration: none;">
  <h4>Example 1:</h4>
</a>
<p>Using <a href="Toolbar.html#updateMenu">updateMenu</a> to modify the toolbar configuration</p>
<pre><code class="language-typescript"><span class="hl-1">    </span><span class="hl-4">const</span><span class="hl-1"> </span><span class="hl-5">toolbar</span><span class="hl-1"> = </span><span class="hl-4">this</span><span class="hl-1">.</span><span class="hl-0">bimViewer</span><span class="hl-1">.</span><span class="hl-0">toolbar</span><span class="hl-1">;</span><br/><span class="hl-1">    </span><span class="hl-0">toolbar</span><span class="hl-1">.</span><span class="hl-7">updateMenu</span><span class="hl-1">(</span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Viewpoint</span><span class="hl-1">, { </span><span class="hl-0">onActive:</span><span class="hl-1"> </span><span class="hl-4">this</span><span class="hl-1">.</span><span class="hl-0">handleActive</span><span class="hl-1"> });</span><br/><span class="hl-1">    </span><span class="hl-0">toolbar</span><span class="hl-1">.</span><span class="hl-7">updateMenu</span><span class="hl-1">(</span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Annotation</span><span class="hl-1">, { </span><span class="hl-0">visible:</span><span class="hl-1"> </span><span class="hl-4">false</span><span class="hl-1"> });</span>
</code></pre>

<a href="#example-2" id="example-2" style="color: inherit; text-decoration: none;">
  <h4>Example 2:</h4>
</a>
<p>Using <a href="Toolbar.html#addMenu">addMenu</a> to add a new menu to the toolbar with specific position.</p>
<pre><code class="language-typescript"><span class="hl-1">    </span><span class="hl-4">const</span><span class="hl-1"> </span><span class="hl-5">toolbar</span><span class="hl-1"> = </span><span class="hl-4">this</span><span class="hl-1">.</span><span class="hl-0">bimViewer</span><span class="hl-1">.</span><span class="hl-0">toolbar</span><span class="hl-1">;</span><br/><span class="hl-1">    </span><span class="hl-0">toolbar</span><span class="hl-1">.</span><span class="hl-7">addMenu</span><span class="hl-1">(</span><br/><span class="hl-1">        </span><span class="hl-3">&quot;newMenu&quot;</span><span class="hl-1">,</span><br/><span class="hl-1">        { </span><span class="hl-0">icon:</span><span class="hl-1"> { </span><span class="hl-0">default:</span><span class="hl-1"> </span><span class="hl-3">&quot;icon-new&quot;</span><span class="hl-1"> }, </span><span class="hl-0">menuName:</span><span class="hl-1"> </span><span class="hl-3">&quot;新菜单&quot;</span><span class="hl-1">, </span><span class="hl-0">controller:</span><span class="hl-1"> </span><span class="hl-0">BimTreeController</span><span class="hl-1"> },</span><br/><span class="hl-1">        [</span><span class="hl-6">2</span><span class="hl-1">, </span><span class="hl-6">5</span><span class="hl-1">]</span><br/><span class="hl-1">    );</span>
</code></pre>

<a href="#example-3" id="example-3" style="color: inherit; text-decoration: none;">
  <h4>Example 3:</h4>
</a>
<p>Modify the configuration in to custmize the toolbar directly, and then <a href="Toolbar.html#refresh">refresh</a> the whole toolbar.</p>
<pre><code class="language-typescript"><span class="hl-1">    </span><span class="hl-4">const</span><span class="hl-1"> </span><span class="hl-5">toolbar</span><span class="hl-1"> = </span><span class="hl-4">this</span><span class="hl-1">.</span><span class="hl-0">bimViewer</span><span class="hl-1">.</span><span class="hl-0">toolbar</span><span class="hl-1">;</span><br/><span class="hl-1">    </span><span class="hl-4">const</span><span class="hl-1"> </span><span class="hl-5">toolbarGroupConfig</span><span class="hl-1"> = [</span><br/><span class="hl-1">        [</span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">OrthoMode</span><span class="hl-1">, </span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">FullScreen</span><span class="hl-1">],</span><br/><span class="hl-1">        [</span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Measure</span><span class="hl-1">, </span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Section</span><span class="hl-1">],</span><br/><span class="hl-1">        [</span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">BimTree</span><span class="hl-1">, </span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Viewpoint</span><span class="hl-1">, </span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Annotation</span><span class="hl-1">, </span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Property</span><span class="hl-1">],</span><br/><span class="hl-1">        [</span><span class="hl-0">ToolbarMenuId</span><span class="hl-1">.</span><span class="hl-0">Setting</span><span class="hl-1">, </span><span class="hl-3">&quot;newMenu&quot;</span><span class="hl-1">],</span><br/><span class="hl-1">    ];</span><br/><span class="hl-1">    </span><span class="hl-0">toolbar</span><span class="hl-1">.</span><span class="hl-0">toolbarGroupConfig</span><span class="hl-1"> = </span><span class="hl-0">toolbarGroupConfig</span><span class="hl-1">;</span><br/><span class="hl-1">    </span><span class="hl-0">toolbar</span><span class="hl-1">.</span><span class="hl-7">refresh</span><span class="hl-1">();</span>
</code></pre>
</dd></dl></div></section><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">Toolbar</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="Toolbar.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_bimViewer" class="tsd-kind-icon">_bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_controllers" class="tsd-kind-icon">_controllers</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_element" class="tsd-kind-icon">_element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_groupConfig" class="tsd-kind-icon">_group<wbr/>Config</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_menuConfig" class="tsd-kind-icon">_menu<wbr/>Config</a></li></ul></section><section class="tsd-index-section "><h3>Accessors</h3><ul class="tsd-index-list"><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Toolbar.html#controllers" class="tsd-kind-icon">controllers</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Toolbar.html#element" class="tsd-kind-icon">element</a></li><li class="tsd-kind-set-signature tsd-parent-kind-class"><a href="Toolbar.html#groupConfig" class="tsd-kind-icon">group<wbr/>Config</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Toolbar.html#menuConfig" class="tsd-kind-icon">menu<wbr/>Config</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#addMenu" class="tsd-kind-icon">add<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#createToolbarMenu" class="tsd-kind-icon">create<wbr/>Toolbar<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#init" class="tsd-kind-icon">init</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#refresh" class="tsd-kind-icon">refresh</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#updateMenu" class="tsd-kind-icon">update<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#updateMenus" class="tsd-kind-icon">update<wbr/>Menus</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">new <wbr/>Toolbar<span class="tsd-signature-symbol">(</span>bimViewer<span class="tsd-signature-symbol">: </span><a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Toolbar.html" class="tsd-signature-type" data-tsd-kind="Class">Toolbar</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:53</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>bimViewer: <a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Toolbar.html" class="tsd-signature-type" data-tsd-kind="Class">Toolbar</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_bimViewer" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _bim<wbr/>Viewer</h3><div class="tsd-signature tsd-kind-icon">_bim<wbr/>Viewer<span class="tsd-signature-symbol">:</span> <a href="BimViewer.html" class="tsd-signature-type" data-tsd-kind="Class">BimViewer</a></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:48</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_controllers" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _controllers</h3><div class="tsd-signature tsd-kind-icon">_controllers<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol"> = {}</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:50</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter-index-signature"><h5><span class="tsd-signature-symbol">[</span>id: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><a href="ToolbarMenuBaseController.html" class="tsd-signature-type" data-tsd-kind="Class">ToolbarMenuBaseController</a></h5></li></ul></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_element" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _element</h3><div class="tsd-signature tsd-kind-icon">_element<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">HTMLDivElement</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:49</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_groupConfig" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _group<wbr/>Config</h3><div class="tsd-signature tsd-kind-icon">_group<wbr/>Config<span class="tsd-signature-symbol">:</span> <a href="../enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:51</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_menuConfig" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _menu<wbr/>Config</h3><div class="tsd-signature tsd-kind-icon">_menu<wbr/>Config<span class="tsd-signature-symbol">:</span> <a href="../modules.html#ToolbarConfig" class="tsd-signature-type" data-tsd-kind="Type alias">ToolbarConfig</a></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:47</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Accessors</h2><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="controllers" class="tsd-anchor"></a><h3>controllers</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> controllers<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:73</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{}</span></h4><ul class="tsd-parameters"><li class="tsd-parameter-index-signature"><h5><span class="tsd-signature-symbol">[</span>id: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">]: </span><a href="ToolbarMenuBaseController.html" class="tsd-signature-type" data-tsd-kind="Class">ToolbarMenuBaseController</a></h5></li></ul></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="element" class="tsd-anchor"></a><h3>element</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> element<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">HTMLDivElement</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:69</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">HTMLDivElement</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-set-signature tsd-parent-kind-class"><a id="groupConfig" class="tsd-anchor"></a><h3>group<wbr/>Config</h3><ul class="tsd-signatures tsd-kind-set-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">set</span> groupConfig<span class="tsd-signature-symbol">(</span>groupConfig<span class="tsd-signature-symbol">: </span><a href="../enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:65</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>groupConfig: <a href="../enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-get-signature tsd-parent-kind-class"><a id="menuConfig" class="tsd-anchor"></a><h3>menu<wbr/>Config</h3><ul class="tsd-signatures tsd-kind-get-signature tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">get</span> menuConfig<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="../modules.html#ToolbarConfig" class="tsd-signature-type" data-tsd-kind="Type alias">ToolbarConfig</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:61</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="../modules.html#ToolbarConfig" class="tsd-signature-type" data-tsd-kind="Type alias">ToolbarConfig</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="addMenu" class="tsd-anchor"></a><h3>add<wbr/>Menu</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">add<wbr/>Menu<span class="tsd-signature-symbol">(</span>menuId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, config<span class="tsd-signature-symbol">: </span><a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a>, insertPosition<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:177</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Add a custmized menu to toolbar.</p>
</dd><dt>memberof</dt><dd><p>Toolbar</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>menuId: <span class="tsd-signature-type">string</span></h5></li><li><h5>config: <a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> insertPosition: <span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="createToolbarMenu" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> create<wbr/>Toolbar<wbr/>Menu</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">create<wbr/>Toolbar<wbr/>Menu<span class="tsd-signature-symbol">(</span>parent<span class="tsd-signature-symbol">: </span><a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a>, menuId<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, cfg<span class="tsd-signature-symbol">: </span><a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a>, isChild<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>controller<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>node<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLDivElement</span><span class="tsd-signature-symbol"> }</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:113</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>parent: <a href="Controller.html" class="tsd-signature-type" data-tsd-kind="Class">Controller</a></h5></li><li><h5>menuId: <span class="tsd-signature-type">string</span></h5></li><li><h5>cfg: <a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a></h5></li><li><h5>isChild: <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">{ </span>controller<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span><span class="tsd-signature-symbol">; </span>node<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLDivElement</span><span class="tsd-signature-symbol"> }</span></h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5>controller<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{}</span></h5><ul class="tsd-parameters"></ul></li><li class="tsd-parameter"><h5>node<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">HTMLDivElement</span></h5></li></ul></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:210</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="init" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> init</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">init<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:77</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="refresh" class="tsd-anchor"></a><h3>refresh</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">refresh<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:204</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Update the whole toolbar element with the current configuration.</p>
</dd><dt>memberof</dt><dd><p>Toolbar</p>
</dd></dl></div><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="updateMenu" class="tsd-anchor"></a><h3>update<wbr/>Menu</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">update<wbr/>Menu<span class="tsd-signature-symbol">(</span>menuId<span class="tsd-signature-symbol">: </span><a href="../enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a>, config<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:152</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Modify the menu configuration and update the toolbar.</p>
</dd><dt>memberof</dt><dd><p>Toolbar</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>menuId: <a href="../enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a></h5></li><li><h5>config: <span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a><span class="tsd-signature-symbol">&gt;</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="updateMenus" class="tsd-anchor"></a><h3>update<wbr/>Menus</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">update<wbr/>Menus<span class="tsd-signature-symbol">(</span>configs<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>config<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>menuId<span class="tsd-signature-symbol">: </span><a href="../enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/Toolbar.ts:162</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Modify the menu configuration and update the toolbar.</p>
</dd><dt>memberof</dt><dd><p>Toolbar</p>
</dd></dl></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>configs: <span class="tsd-signature-symbol">{ </span>config<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">Partial</span><span class="tsd-signature-symbol">&lt;</span><a href="../interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">; </span>menuId<span class="tsd-signature-symbol">: </span><a href="../enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="Toolbar.html" class="tsd-kind-icon">Toolbar</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="Toolbar.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_bimViewer" class="tsd-kind-icon">_bim<wbr/>Viewer</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_controllers" class="tsd-kind-icon">_controllers</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_element" class="tsd-kind-icon">_element</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_groupConfig" class="tsd-kind-icon">_group<wbr/>Config</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#_menuConfig" class="tsd-kind-icon">_menu<wbr/>Config</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Toolbar.html#controllers" class="tsd-kind-icon">controllers</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Toolbar.html#element" class="tsd-kind-icon">element</a></li><li class="tsd-kind-set-signature tsd-parent-kind-class"><a href="Toolbar.html#groupConfig" class="tsd-kind-icon">group<wbr/>Config</a></li><li class="tsd-kind-get-signature tsd-parent-kind-class"><a href="Toolbar.html#menuConfig" class="tsd-kind-icon">menu<wbr/>Config</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#addMenu" class="tsd-kind-icon">add<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#createToolbarMenu" class="tsd-kind-icon">create<wbr/>Toolbar<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="Toolbar.html#init" class="tsd-kind-icon">init</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#refresh" class="tsd-kind-icon">refresh</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#updateMenu" class="tsd-kind-icon">update<wbr/>Menu</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="Toolbar.html#updateMenus" class="tsd-kind-icon">update<wbr/>Menus</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>