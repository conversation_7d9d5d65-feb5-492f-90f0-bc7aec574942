# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.16.0.tgz?cache=0&sync_timestamp=1635560663383&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fcode-frame%2Fdownload%2F%40babel%2Fcode-frame-7.16.0.tgz#0dfc80309beec8411e65e706461c408b0bb9b431"
  integrity sha1-DfyAMJvuyEEeZecGRhxAiwu5tDE=
  dependencies:
    "@babel/highlight" "^7.16.0"

"@babel/helper-validator-identifier@^7.15.7":
  version "7.15.7"
  resolved "https://registry.nlark.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.15.7.tgz?cache=0&sync_timestamp=1631920110587&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40babel%2Fhelper-validator-identifier%2Fdownload%2F%40babel%2Fhelper-validator-identifier-7.15.7.tgz#220df993bfe904a4a6b02ab4f3385a5ebf6e2389"
  integrity sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=

"@babel/highlight@^7.16.0":
  version "7.16.0"
  resolved "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.16.0.tgz?cache=0&sync_timestamp=1635560940881&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40babel%2Fhighlight%2Fdownload%2F%40babel%2Fhighlight-7.16.0.tgz#6ceb32b2ca4b8f5f361fb7fd821e3fddf4a1725a"
  integrity sha1-bOsysspLj182H7f9gh4/3fShclo=
  dependencies:
    "@babel/helper-validator-identifier" "^7.15.7"
    chalk "^2.0.0"
    js-tokens "^4.0.0"

"@discoveryjs/json-ext@^0.5.0":
  version "0.5.5"
  resolved "https://registry.nlark.com/@discoveryjs/json-ext/download/@discoveryjs/json-ext-0.5.5.tgz?cache=0&sync_timestamp=1631662047041&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40discoveryjs%2Fjson-ext%2Fdownload%2F%40discoveryjs%2Fjson-ext-0.5.5.tgz#9283c9ce5b289a3c4f61c12757469e59377f81f3"
  integrity sha1-koPJzlsomjxPYcEnV0aeWTd/gfM=

"@eslint/eslintrc@^1.0.4":
  version "1.0.4"
  resolved "https://registry.npmmirror.com/@eslint/eslintrc/download/@eslint/eslintrc-1.0.4.tgz?cache=0&sync_timestamp=1636146895324&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40eslint%2Feslintrc%2Fdownload%2F%40eslint%2Feslintrc-1.0.4.tgz#dfe0ff7ba270848d10c5add0715e04964c034b31"
  integrity sha1-3+D/e6JwhI0Qxa3QcV4ElkwDSzE=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.0.0"
    globals "^13.9.0"
    ignore "^4.0.6"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.0.4"
    strip-json-comments "^3.1.1"

"@humanwhocodes/config-array@^0.6.0":
  version "0.6.0"
  resolved "https://registry.npmmirror.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.6.0.tgz?cache=0&sync_timestamp=1635880461804&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40humanwhocodes%2Fconfig-array%2Fdownload%2F%40humanwhocodes%2Fconfig-array-0.6.0.tgz#b5621fdb3b32309d2d16575456cbc277fa8f021a"
  integrity sha1-tWIf2zsyMJ0tFldUVsvCd/qPAho=
  dependencies:
    "@humanwhocodes/object-schema" "^1.2.0"
    debug "^4.1.1"
    minimatch "^3.0.4"

"@humanwhocodes/object-schema@^1.2.0":
  version "1.2.1"
  resolved "https://registry.npmmirror.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-1.2.1.tgz#b520529ec21d8e5945a1851dfd1c32e94e39ff45"
  integrity sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U=

"@mrmlnc/readdir-enhanced@^2.2.1":
  version "2.2.1"
  resolved "https://registry.npm.taobao.org/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz#524af240d1a360527b730475ecfa1344aa540dde"
  integrity sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=
  dependencies:
    call-me-maybe "^1.0.1"
    glob-to-regexp "^0.3.0"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://registry.nlark.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://registry.nlark.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz?cache=0&sync_timestamp=1622792655362&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nodelib%2Ffs.stat%2Fdownload%2F%40nodelib%2Ffs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.stat@^1.1.2":
  version "1.1.3"
  resolved "https://registry.nlark.com/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz?cache=0&sync_timestamp=1622792655362&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nodelib%2Ffs.stat%2Fdownload%2F%40nodelib%2Ffs.stat-1.1.3.tgz#2b5a3ab3f918cca48a8c754c08168e3f03eba61b"
  integrity sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  resolved "https://registry.nlark.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz?cache=0&sync_timestamp=1625769815389&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40nodelib%2Ffs.walk%2Fdownload%2F%40nodelib%2Ffs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@types/detect-indent@0.1.30":
  version "0.1.30"
  resolved "https://registry.npmmirror.com/@types/detect-indent/download/@types/detect-indent-0.1.30.tgz#dc682bb412b4e65ba098e70edad73b4833fb910d"
  integrity sha1-3GgrtBK05lugmOcO2tc7SDP7kQ0=

"@types/earcut@^2.1.1":
  version "2.1.1"
  resolved "https://registry.npmmirror.com/@types/earcut/download/@types/earcut-2.1.1.tgz#573a0af609f17005c751f6f4ffec49cfe358ea51"
  integrity sha1-VzoK9gnxcAXHUfb0/+xJz+NY6lE=

"@types/eslint-scope@^3.7.0":
  version "3.7.1"
  resolved "https://registry.npmmirror.com/@types/eslint-scope/download/@types/eslint-scope-3.7.1.tgz#8dc390a7b4f9dd9f1284629efce982e41612116e"
  integrity sha1-jcOQp7T53Z8ShGKe/OmC5BYSEW4=
  dependencies:
    "@types/eslint" "*"
    "@types/estree" "*"

"@types/eslint@*":
  version "8.2.0"
  resolved "https://registry.npmmirror.com/@types/eslint/download/@types/eslint-8.2.0.tgz?cache=0&sync_timestamp=1637183141843&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Feslint%2Fdownload%2F%40types%2Feslint-8.2.0.tgz#afd0519223c29c347087542cbaee2fedc0873b16"
  integrity sha512-74hbvsnc+7TEDa1z5YLSe4/q8hGYB3USNvCuzHUJrjPV6hXaq8IXcngCrHkuvFt0+8rFz7xYXrHgNayIX0UZvQ==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/eslint@^7.28.2":
  version "7.29.0"
  resolved "https://registry.npmmirror.com/@types/eslint/download/@types/eslint-7.29.0.tgz?cache=0&sync_timestamp=1637183141843&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Feslint%2Fdownload%2F%40types%2Feslint-7.29.0.tgz#e56ddc8e542815272720bb0b4ccc2aff9c3e1c78"
  integrity sha512-VNcvioYDH8/FxaeTKkM4/TiTwt6pBV9E3OfGmvaw8tPl0rrHCJ4Ll15HRT+pMiFAf/MLQvAzC+6RzUMEL9Ceng==
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^0.0.50":
  version "0.0.50"
  resolved "https://registry.npmmirror.com/@types/estree/download/@types/estree-0.0.50.tgz?cache=0&sync_timestamp=1637264753186&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Festree%2Fdownload%2F%40types%2Festree-0.0.50.tgz#1e0caa9364d3fccd2931c3ed96fdbeaa5d4cca83"
  integrity sha1-Hgyqk2TT/M0pMcPtlv2+ql1MyoM=

"@types/glob@5.0.30":
  version "5.0.30"
  resolved "https://registry.npmmirror.com/@types/glob/download/@types/glob-5.0.30.tgz#1026409c5625a8689074602808d082b2867b8a51"
  integrity sha1-ECZAnFYlqGiQdGAoCNCCsoZ7ilE=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/glob@^7.1.1":
  version "7.2.0"
  resolved "https://registry.npmmirror.com/@types/glob/download/@types/glob-7.2.0.tgz#bc1b5bf3aa92f25bd5dd39f35c57361bdce5b2eb"
  integrity sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=
  dependencies:
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/html-minifier-terser@^6.0.0":
  version "6.0.0"
  resolved "https://registry.npmmirror.com/@types/html-minifier-terser/download/@types/html-minifier-terser-6.0.0.tgz?cache=0&sync_timestamp=1637266134288&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fhtml-minifier-terser%2Fdownload%2F%40types%2Fhtml-minifier-terser-6.0.0.tgz#563c1c6c132cd204e71512f9c0b394ff90d3fae7"
  integrity sha1-VjwcbBMs0gTnFRL5wLOU/5DT+uc=

"@types/http-proxy@^1.17.5":
  version "1.17.7"
  resolved "https://registry.npmmirror.com/@types/http-proxy/download/@types/http-proxy-1.17.7.tgz?cache=0&sync_timestamp=1637266134966&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fhttp-proxy%2Fdownload%2F%40types%2Fhttp-proxy-1.17.7.tgz#30ea85cc2c868368352a37f0d0d3581e24834c6f"
  integrity sha1-MOqFzCyGg2g1Kjfw0NNYHiSDTG8=
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.8", "@types/json-schema@^7.0.9":
  version "7.0.9"
  resolved "https://registry.npmmirror.com/@types/json-schema/download/@types/json-schema-7.0.9.tgz#97edc9037ea0c38585320b28964dde3b39e4660d"
  integrity sha1-l+3JA36gw4WFMgsolk3eOznkZg0=

"@types/lodash@^4.14.175":
  version "4.14.177"
  resolved "https://registry.npmmirror.com/@types/lodash/download/@types/lodash-4.14.177.tgz?cache=0&sync_timestamp=1637266715360&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Flodash%2Fdownload%2F%40types%2Flodash-4.14.177.tgz#f70c0d19c30fab101cad46b52be60363c43c4578"
  integrity sha512-0fDwydE2clKe9MNfvXHBHF9WEahRuj+msTuQqOmAApNORFvhMYZKNGGJdCzuhheVjMps/ti0Ak/iJPACMaevvw==

"@types/minimatch@*", "@types/minimatch@^3.0.3":
  version "3.0.5"
  resolved "https://registry.npmmirror.com/@types/minimatch/download/@types/minimatch-3.0.5.tgz#1001cc5e6a3704b83c236027e77f2f58ea010f40"
  integrity sha1-EAHMXmo3BLg8I2An538vWOoBD0A=

"@types/mkdirp@0.3.29":
  version "0.3.29"
  resolved "https://registry.npmmirror.com/@types/mkdirp/download/@types/mkdirp-0.3.29.tgz#7f2ad7ec55f914482fc9b1ec4bb1ae6028d46066"
  integrity sha1-fyrX7FX5FEgvybHsS7GuYCjUYGY=

"@types/node@*":
  version "16.11.9"
  resolved "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.9.tgz?cache=0&sync_timestamp=1637310817584&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.9.tgz#879be3ad7af29f4c1a5c433421bf99fab7047185"
  integrity sha512-MKmdASMf3LtPzwLyRrFjtFFZ48cMf8jmX5VRYrDQiJa8Ybu5VAmkqBWqKU8fdCwD8ysw4mQ9nrEHvzg6gunR7A==

"@types/node@8.0.0":
  version "8.0.0"
  resolved "https://registry.npmmirror.com/@types/node/download/@types/node-8.0.0.tgz?cache=0&sync_timestamp=1637310817584&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-8.0.0.tgz#acaa89247afddc7967e9902fd11761dadea1a555"
  integrity sha1-rKqJJHr93Hln6ZAv0Rdh2t6hpVU=

"@types/normalize-package-data@^2.4.0":
  version "2.4.1"
  resolved "https://registry.npmmirror.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.1.tgz?cache=0&sync_timestamp=1637268858625&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnormalize-package-data%2Fdownload%2F%40types%2Fnormalize-package-data-2.4.1.tgz#d3357479a0fdfdd5907fe67e17e0a85c906e1301"
  integrity sha1-0zV0eaD9/dWQf+Z+F+CoXJBuEwE=

"@types/retry@^0.12.0":
  version "0.12.1"
  resolved "https://registry.npmmirror.com/@types/retry/download/@types/retry-0.12.1.tgz?cache=0&sync_timestamp=1637270319130&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fretry%2Fdownload%2F%40types%2Fretry-0.12.1.tgz#d8f1c0d0dc23afad6dc16a9e993a0865774b4065"
  integrity sha1-2PHA0Nwjr61twWqemToIZXdLQGU=

"@typescript-eslint/eslint-plugin@^5.0.0":
  version "5.4.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.4.0.tgz?cache=0&sync_timestamp=1637271077001&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40typescript-eslint%2Feslint-plugin%2Fdownload%2F%40typescript-eslint%2Feslint-plugin-5.4.0.tgz#05e711a2e7b68342661fde61bccbd1531c19521a"
  integrity sha512-9/yPSBlwzsetCsGEn9j24D8vGQgJkOTr4oMLas/w886ZtzKIs1iyoqFrwsX2fqYEeUwsdBpC21gcjRGo57u0eg==
  dependencies:
    "@typescript-eslint/experimental-utils" "5.4.0"
    "@typescript-eslint/scope-manager" "5.4.0"
    debug "^4.3.2"
    functional-red-black-tree "^1.0.1"
    ignore "^5.1.8"
    regexpp "^3.2.0"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/experimental-utils@5.4.0":
  version "5.4.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/experimental-utils/download/@typescript-eslint/experimental-utils-5.4.0.tgz?cache=0&sync_timestamp=1637270276186&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40typescript-eslint%2Fexperimental-utils%2Fdownload%2F%40typescript-eslint%2Fexperimental-utils-5.4.0.tgz#238a7418d2da3b24874ba35385eb21cc61d2a65e"
  integrity sha512-Nz2JDIQUdmIGd6p33A+naQmwfkU5KVTLb/5lTk+tLVTDacZKoGQisj8UCxk7onJcrgjIvr8xWqkYI+DbI3TfXg==
  dependencies:
    "@types/json-schema" "^7.0.9"
    "@typescript-eslint/scope-manager" "5.4.0"
    "@typescript-eslint/types" "5.4.0"
    "@typescript-eslint/typescript-estree" "5.4.0"
    eslint-scope "^5.1.1"
    eslint-utils "^3.0.0"

"@typescript-eslint/parser@^5.0.0":
  version "5.4.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.4.0.tgz?cache=0&sync_timestamp=1637271649550&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40typescript-eslint%2Fparser%2Fdownload%2F%40typescript-eslint%2Fparser-5.4.0.tgz#3aa83ce349d66e39b84151f6d5464928044ca9e3"
  integrity sha512-JoB41EmxiYpaEsRwpZEYAJ9XQURPFer8hpkIW9GiaspVLX8oqbqNM8P4EP8HOZg96yaALiLEVWllA2E8vwsIKw==
  dependencies:
    "@typescript-eslint/scope-manager" "5.4.0"
    "@typescript-eslint/types" "5.4.0"
    "@typescript-eslint/typescript-estree" "5.4.0"
    debug "^4.3.2"

"@typescript-eslint/scope-manager@5.4.0":
  version "5.4.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.4.0.tgz?cache=0&sync_timestamp=1637271007196&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40typescript-eslint%2Fscope-manager%2Fdownload%2F%40typescript-eslint%2Fscope-manager-5.4.0.tgz#aaab08415f4a9cf32b870c7750ae8ba4607126a1"
  integrity sha512-pRxFjYwoi8R+n+sibjgF9iUiAELU9ihPBtHzocyW8v8D8G8KeQvXTsW7+CBYIyTYsmhtNk50QPGLE3vrvhM5KA==
  dependencies:
    "@typescript-eslint/types" "5.4.0"
    "@typescript-eslint/visitor-keys" "5.4.0"

"@typescript-eslint/types@5.4.0":
  version "5.4.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/types/download/@typescript-eslint/types-5.4.0.tgz?cache=0&sync_timestamp=1637256710360&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40typescript-eslint%2Ftypes%2Fdownload%2F%40typescript-eslint%2Ftypes-5.4.0.tgz#b1c130f4b381b77bec19696c6e3366f9781ce8f2"
  integrity sha512-GjXNpmn+n1LvnttarX+sPD6+S7giO+9LxDIGlRl4wK3a7qMWALOHYuVSZpPTfEIklYjaWuMtfKdeByx0AcaThA==

"@typescript-eslint/typescript-estree@5.4.0":
  version "5.4.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.4.0.tgz#fe524fb308973c68ebeb7428f3b64499a6ba5fc0"
  integrity sha512-nhlNoBdhKuwiLMx6GrybPT3SFILm5Gij2YBdPEPFlYNFAXUJWX6QRgvi/lwVoadaQEFsizohs6aFRMqsXI2ewA==
  dependencies:
    "@typescript-eslint/types" "5.4.0"
    "@typescript-eslint/visitor-keys" "5.4.0"
    debug "^4.3.2"
    globby "^11.0.4"
    is-glob "^4.0.3"
    semver "^7.3.5"
    tsutils "^3.21.0"

"@typescript-eslint/visitor-keys@5.4.0":
  version "5.4.0"
  resolved "https://registry.npmmirror.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.4.0.tgz?cache=0&sync_timestamp=1637271604809&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40typescript-eslint%2Fvisitor-keys%2Fdownload%2F%40typescript-eslint%2Fvisitor-keys-5.4.0.tgz#09bc28efd3621f292fe88c86eef3bf4893364c8c"
  integrity sha512-PVbax7MeE7tdLfW5SA0fs8NGVVr+buMPrcj+CWYWPXsZCH8qZ1THufDzbXm1xrZ2b2PA1iENJ0sRq5fuUtvsJg==
  dependencies:
    "@typescript-eslint/types" "5.4.0"
    eslint-visitor-keys "^3.0.0"

"@webassemblyjs/ast@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.11.1.tgz?cache=0&sync_timestamp=1625473459015&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fast%2Fdownload%2F%40webassemblyjs%2Fast-1.11.1.tgz#2bfd767eae1a6996f432ff7e8d7fc75679c0b6a7"
  integrity sha1-K/12fq4aaZb0Mv9+jX/HVnnAtqc=
  dependencies:
    "@webassemblyjs/helper-numbers" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"

"@webassemblyjs/floating-point-hex-parser@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.11.1.tgz#f6c61a705f0fd7a6aecaa4e8198f23d9dc179e4f"
  integrity sha1-9sYacF8P16auyqToGY8j2dwXnk8=

"@webassemblyjs/helper-api-error@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.11.1.tgz?cache=0&sync_timestamp=1625473346773&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-api-error%2Fdownload%2F%40webassemblyjs%2Fhelper-api-error-1.11.1.tgz#1a63192d8788e5c012800ba6a7a46c705288fd16"
  integrity sha1-GmMZLYeI5cASgAump6RscFKI/RY=

"@webassemblyjs/helper-buffer@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.11.1.tgz?cache=0&sync_timestamp=1625473344792&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-buffer%2Fdownload%2F%40webassemblyjs%2Fhelper-buffer-1.11.1.tgz#832a900eb444884cde9a7cad467f81500f5e5ab5"
  integrity sha1-gyqQDrREiEzemnytRn+BUA9eWrU=

"@webassemblyjs/helper-numbers@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-numbers/download/@webassemblyjs/helper-numbers-1.11.1.tgz?cache=0&sync_timestamp=1625473485159&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-numbers%2Fdownload%2F%40webassemblyjs%2Fhelper-numbers-1.11.1.tgz#64d81da219fbbba1e3bd1bfc74f6e8c4e10a62ae"
  integrity sha1-ZNgdohn7u6HjvRv8dPboxOEKYq4=
  dependencies:
    "@webassemblyjs/floating-point-hex-parser" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/helper-wasm-bytecode@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.11.1.tgz#f328241e41e7b199d0b20c18e88429c4433295e1"
  integrity sha1-8ygkHkHnsZnQsgwY6IQpxEMyleE=

"@webassemblyjs/helper-wasm-section@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.11.1.tgz?cache=0&sync_timestamp=1625473466570&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fhelper-wasm-section%2Fdownload%2F%40webassemblyjs%2Fhelper-wasm-section-1.11.1.tgz#21ee065a7b635f319e738f0dd73bfbda281c097a"
  integrity sha1-Ie4GWntjXzGec48N1zv72igcCXo=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"

"@webassemblyjs/ieee754@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.11.1.tgz#963929e9bbd05709e7e12243a099180812992614"
  integrity sha1-ljkp6bvQVwnn4SJDoJkYCBKZJhQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.11.1.tgz?cache=0&sync_timestamp=1625473342433&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fleb128%2Fdownload%2F%40webassemblyjs%2Fleb128-1.11.1.tgz#ce814b45574e93d76bae1fb2644ab9cdd9527aa5"
  integrity sha1-zoFLRVdOk9drrh+yZEq5zdlSeqU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.11.1.tgz#d1f8b764369e7c6e6bae350e854dec9a59f0a3ff"
  integrity sha1-0fi3ZDaefG5rrjUOhU3smlnwo/8=

"@webassemblyjs/wasm-edit@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.11.1.tgz?cache=0&sync_timestamp=1625473463093&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-edit%2Fdownload%2F%40webassemblyjs%2Fwasm-edit-1.11.1.tgz#ad206ebf4bf95a058ce9880a8c092c5dec8193d6"
  integrity sha1-rSBuv0v5WgWM6YgKjAksXeyBk9Y=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/helper-wasm-section" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-opt" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    "@webassemblyjs/wast-printer" "1.11.1"

"@webassemblyjs/wasm-gen@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.11.1.tgz?cache=0&sync_timestamp=1625473361759&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-gen%2Fdownload%2F%40webassemblyjs%2Fwasm-gen-1.11.1.tgz#86c5ea304849759b7d88c47a32f4f039ae3c8f76"
  integrity sha1-hsXqMEhJdZt9iMR6MvTwOa48j3Y=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wasm-opt@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.11.1.tgz?cache=0&sync_timestamp=1625473467198&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-opt%2Fdownload%2F%40webassemblyjs%2Fwasm-opt-1.11.1.tgz#657b4c2202f4cf3b345f8a4c6461c8c2418985f2"
  integrity sha1-ZXtMIgL0zzs0X4pMZGHIwkGJhfI=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-buffer" "1.11.1"
    "@webassemblyjs/wasm-gen" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"

"@webassemblyjs/wasm-parser@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.11.1.tgz?cache=0&sync_timestamp=1625473464593&other_urls=https%3A%2F%2Fregistry.nlark.com%2F%40webassemblyjs%2Fwasm-parser%2Fdownload%2F%40webassemblyjs%2Fwasm-parser-1.11.1.tgz#86ca734534f417e9bd3c67c7a1c75d8be41fb199"
  integrity sha1-hspzRTT0F+m9PGfHocddi+QfsZk=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/helper-api-error" "1.11.1"
    "@webassemblyjs/helper-wasm-bytecode" "1.11.1"
    "@webassemblyjs/ieee754" "1.11.1"
    "@webassemblyjs/leb128" "1.11.1"
    "@webassemblyjs/utf8" "1.11.1"

"@webassemblyjs/wast-printer@1.11.1":
  version "1.11.1"
  resolved "https://registry.nlark.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.11.1.tgz#d0c73beda8eec5426f10ae8ef55cee5e7084c2f0"
  integrity sha1-0Mc77ajuxUJvEK6O9VzuXnCEwvA=
  dependencies:
    "@webassemblyjs/ast" "1.11.1"
    "@xtuc/long" "4.2.2"

"@webpack-cli/configtest@^1.1.0":
  version "1.1.0"
  resolved "https://registry.npmmirror.com/@webpack-cli/configtest/download/@webpack-cli/configtest-1.1.0.tgz?cache=0&sync_timestamp=1633553239176&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40webpack-cli%2Fconfigtest%2Fdownload%2F%40webpack-cli%2Fconfigtest-1.1.0.tgz#8342bef0badfb7dfd3b576f2574ab80c725be043"
  integrity sha1-g0K+8Lrft9/TtXbyV0q4DHJb4EM=

"@webpack-cli/generators@^2.3.0":
  version "2.4.1"
  resolved "https://registry.npmmirror.com/@webpack-cli/generators/download/@webpack-cli/generators-2.4.1.tgz#738c0b2f209d7d9e0393f2d10f1a4569072825be"
  integrity sha1-c4wLLyCdfZ4Dk/LRDxpFaQcoJb4=
  dependencies:
    webpack-cli "^4.9.1"
    yeoman-environment "^2.10.3"
    yeoman-generator "^4.12.0"

"@webpack-cli/info@^1.4.0":
  version "1.4.0"
  resolved "https://registry.npmmirror.com/@webpack-cli/info/download/@webpack-cli/info-1.4.0.tgz?cache=0&sync_timestamp=1633553269200&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40webpack-cli%2Finfo%2Fdownload%2F%40webpack-cli%2Finfo-1.4.0.tgz#b9179c3227ab09cbbb149aa733475fcf99430223"
  integrity sha1-uRecMierCcu7FJqnM0dfz5lDAiM=
  dependencies:
    envinfo "^7.7.3"

"@webpack-cli/serve@^1.6.0":
  version "1.6.0"
  resolved "https://registry.npmmirror.com/@webpack-cli/serve/download/@webpack-cli/serve-1.6.0.tgz?cache=0&sync_timestamp=1633553365700&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40webpack-cli%2Fserve%2Fdownload%2F%40webpack-cli%2Fserve-1.6.0.tgz#2c275aa05c895eccebbfc34cfb223c6e8bd591a2"
  integrity sha1-LCdaoFyJXszrv8NM+yI8bovVkaI=

"@xeokit/xeokit-sdk@^2.0.7":
  version "2.0.14"
  resolved "https://registry.npmmirror.com/@xeokit/xeokit-sdk/download/@xeokit/xeokit-sdk-2.0.14.tgz#5e05ab6b03bdb005eda25ecd3ea4844f97eafdfe"
  integrity sha1-XgWrawO9sAXtol7NPqSET5fq/f4=
  dependencies:
    web-ifc "0.0.26"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "https://registry.nlark.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "https://registry.nlark.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

JSONStream@^1.2.1, JSONStream@^1.3.5:
  version "1.3.5"
  resolved "https://registry.npm.taobao.org/JSONStream/download/JSONStream-1.3.5.tgz#3208c1f08d3a4d99261ab64f92302bc15e111ca0"
  integrity sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

accepts@~1.3.4, accepts@~1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "https://registry.npm.taobao.org/accepts/download/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-import-assertions@^1.7.6:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/acorn-import-assertions/download/acorn-import-assertions-1.8.0.tgz?cache=0&sync_timestamp=1633349660084&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn-import-assertions%2Fdownload%2Facorn-import-assertions-1.8.0.tgz#ba2b5939ce62c238db6d93d81c9b111b29b855e9"
  integrity sha1-uitZOc5iwjjbbZPYHJsRGym4Vek=

acorn-jsx@^5.3.1:
  version "5.3.2"
  resolved "https://registry.nlark.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.4.1, acorn@^8.5.0:
  version "8.6.0"
  resolved "https://registry.npmmirror.com/acorn/download/acorn-8.6.0.tgz?cache=0&sync_timestamp=1637225522161&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Facorn%2Fdownload%2Facorn-8.6.0.tgz#e3692ba0eb1a0c83eaa4f37f5fa7368dd7142895"
  integrity sha512-U1riIR+lBSNi3IbxtaHOIKdH8sLFv3NYfNv8sg7ZsNhcfl4HF2++BfqqrNAxoCLQW1iiylOj76ecnaUxz+z9yw==

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/aggregate-error/download/aggregate-error-3.1.0.tgz?cache=0&sync_timestamp=1622604530325&other_urls=https%3A%2F%2Fregistry.nlark.com%2Faggregate-error%2Fdownload%2Faggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-formats@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/ajv-formats/download/ajv-formats-2.1.1.tgz#6e669400659eb74973bbf2e33327180a0996b520"
  integrity sha1-bmaUAGWet0lzu/LjMycYCgmWtSA=
  dependencies:
    ajv "^8.0.0"

ajv-keywords@^3.5.2:
  version "3.5.2"
  resolved "https://registry.nlark.com/ajv-keywords/download/ajv-keywords-3.5.2.tgz#31f29da5ab6e00d1c2d329acf7b5929614d5014d"
  integrity sha1-MfKdpatuANHC0yms97WSlhTVAU0=

ajv-keywords@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/ajv-keywords/download/ajv-keywords-5.0.0.tgz#d01b3b21715b2f63d02aa511b82fc6eb3b30083c"
  integrity sha1-0Bs7IXFbL2PQKqURuC/G6zswCDw=
  dependencies:
    fast-deep-equal "^3.1.3"

ajv@^6.10.0, ajv@^6.12.4, ajv@^6.12.5:
  version "6.12.6"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-6.12.6.tgz?cache=0&sync_timestamp=1637094066870&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0, ajv@^8.8.0:
  version "8.8.1"
  resolved "https://registry.npmmirror.com/ajv/download/ajv-8.8.1.tgz?cache=0&sync_timestamp=1637094066870&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fajv%2Fdownload%2Fajv-8.8.1.tgz#e73dd88eeb4b10bbcd82bee136e6fbe801664d18"
  integrity sha512-6CiMNDrzv0ZR916u2T+iRunnD60uWmNn8SkdB44/6stVORUg0aAkWO7PkOhpCmjmW8f2I/G/xnowD66fxGyQJg==
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ansi-colors@^4.1.1:
  version "4.1.1"
  resolved "https://registry.nlark.com/ansi-colors/download/ansi-colors-4.1.1.tgz#cbb9ae256bf750af1eab344f229aa27fe94ba348"
  integrity sha1-y7muJWv3UK8eqzRPIpqif+lLo0g=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "https://registry.nlark.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz?cache=0&sync_timestamp=1618847144938&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-escapes%2Fdownload%2Fansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-html-community@^0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/ansi-html-community/download/ansi-html-community-0.0.8.tgz?cache=0&sync_timestamp=1631016068950&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-html-community%2Fdownload%2Fansi-html-community-0.0.8.tgz#69fbc4d6ccbe383f9736934ae34c3f8290f1bf41"
  integrity sha1-afvE1sy+OD+XNpNK40w/gpDxv0E=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.0.1"
  resolved "https://registry.nlark.com/ansi-regex/download/ansi-regex-6.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fansi-regex%2Fdownload%2Fansi-regex-6.0.1.tgz#3183e38fae9a65d7cb5e53945cd5897d0260a06a"
  integrity sha1-MYPjj66aZdfLXlOUXNWJfQJgoGo=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

anymatch@~3.1.2:
  version "3.1.2"
  resolved "https://registry.nlark.com/anymatch/download/anymatch-3.1.2.tgz#c0557c096af32f106198f4f4e2a383537e378716"
  integrity sha1-wFV8CWrzLxBhmPT04qODU343hxY=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/arr-flatten/download/arr-flatten-1.1.0.tgz?cache=0&sync_timestamp=1618846805394&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farr-flatten%2Fdownload%2Farr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-differ@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/array-differ/download/array-differ-3.0.0.tgz#3cbb3d0f316810eafcc47624734237d6aee4ae6b"
  integrity sha1-PLs9DzFoEOr8xHYkc0I31q7krms=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-flatten@^2.1.0:
  version "2.1.2"
  resolved "https://registry.nlark.com/array-flatten/download/array-flatten-2.1.2.tgz#24ef80a28c1a893617e2149b0c6d0d788293b099"
  integrity sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=

array-union@^1.0.1, array-union@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.nlark.com/array-uniq/download/array-uniq-1.0.3.tgz?cache=0&sync_timestamp=1622605321175&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farray-uniq%2Fdownload%2Farray-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "https://registry.nlark.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/arrify/download/arrify-1.0.1.tgz?cache=0&sync_timestamp=1619599497996&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farrify%2Fdownload%2Farrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

arrify@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/arrify/download/arrify-2.0.1.tgz?cache=0&sync_timestamp=1619599497996&other_urls=https%3A%2F%2Fregistry.nlark.com%2Farrify%2Fdownload%2Farrify-2.0.1.tgz#c9655e9331e0abcd588d2a7cad7e9956f66701fa"
  integrity sha1-yWVekzHgq81YjSp8rX6ZVvZnAfo=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

async@0.9.x:
  version "0.9.2"
  resolved "https://registry.npmmirror.com/async/download/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"
  integrity sha1-rqdNXmHB+JlhO/ZL2mbUx48v0X0=

async@^2.6.2:
  version "2.6.3"
  resolved "https://registry.npmmirror.com/async/download/async-2.6.3.tgz#d72625e2344a3656e3a3ad4fa749fa83299d82ff"
  integrity sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=
  dependencies:
    lodash "^4.17.14"

atob@^2.1.2:
  version "2.1.2"
  resolved "https://registry.nlark.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

axios@^0.21.1:
  version "0.21.4"
  resolved "https://registry.npmmirror.com/axios/download/axios-0.21.4.tgz?cache=0&sync_timestamp=1635213960429&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Faxios%2Fdownload%2Faxios-0.21.4.tgz#c67b90dc0568e5c1cf2b0b858c43ba28e2eda575"
  integrity sha1-xnuQ3AVo5cHPKwuFjEO6KOLtpXU=
  dependencies:
    follow-redirects "^1.14.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base@^0.11.1:
  version "0.11.2"
  resolved "https://registry.npm.taobao.org/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

batch@0.6.1:
  version "0.6.1"
  resolved "https://registry.nlark.com/batch/download/batch-0.6.1.tgz#dc34314f4e679318093fc760272525f94bf25c16"
  integrity sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=

binary-extensions@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/binary-extensions/download/binary-extensions-2.2.0.tgz#75f502eeaf9ffde42fc98829645be4ea76bd9e2d"
  integrity sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=

binaryextensions@^2.1.2:
  version "2.3.0"
  resolved "https://registry.nlark.com/binaryextensions/download/binaryextensions-2.3.0.tgz?cache=0&sync_timestamp=1627596687862&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fbinaryextensions%2Fdownload%2Fbinaryextensions-2.3.0.tgz#1d269cbf7e6243ea886aa41453c3651ccbe13c22"
  integrity sha1-HSacv35iQ+qIaqQUU8NlHMvhPCI=

body-parser@1.19.0:
  version "1.19.0"
  resolved "https://registry.nlark.com/body-parser/download/body-parser-1.19.0.tgz#96b2709e57c9c4e09a6fd66a8fd979844f69f08a"
  integrity sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

bonjour@^3.5.0:
  version "3.5.0"
  resolved "https://registry.nlark.com/bonjour/download/bonjour-3.5.0.tgz#8e890a183d8ee9a2393b3844c691a42bcf7bc9f5"
  integrity sha1-jokKGD2O6aI5OzhExpGkK897yfU=
  dependencies:
    array-flatten "^2.1.0"
    deep-equal "^1.0.1"
    dns-equal "^1.0.0"
    dns-txt "^2.0.2"
    multicast-dns "^6.0.1"
    multicast-dns-service-types "^1.1.0"

boolbase@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "https://registry.nlark.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1:
  version "2.3.2"
  resolved "https://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@~3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

browserslist@^4.14.5:
  version "4.18.1"
  resolved "https://registry.npmmirror.com/browserslist/download/browserslist-4.18.1.tgz#60d3920f25b6860eb917c6c7b185576f4d8b017f"
  integrity sha512-8ScCzdpPwR2wQh8IT82CA2VgDwjHyqMovPBZSNH54+tm4Jk2pCuv90gmAdH6J84OCRWi0b4gMe6O6XPXuJnjgQ==
  dependencies:
    caniuse-lite "^1.0.30001280"
    electron-to-chromium "^1.3.896"
    escalade "^3.1.1"
    node-releases "^2.0.1"
    picocolors "^1.0.0"

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://registry.nlark.com/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer-indexof@^1.0.0:
  version "1.1.1"
  resolved "https://registry.nlark.com/buffer-indexof/download/buffer-indexof-1.1.1.tgz#52fabcc6a606d1a00302802648ef68f639da268c"
  integrity sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.0.0.tgz?cache=0&sync_timestamp=1637015063162&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"
  integrity sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=

bytes@3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz?cache=0&sync_timestamp=1637015063162&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbytes%2Fdownload%2Fbytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/cache-base/download/cache-base-1.0.1.tgz?cache=0&sync_timestamp=1636237266442&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcache-base%2Fdownload%2Fcache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

call-bind@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/call-bind/download/call-bind-1.0.2.tgz#b1d4e89e688119c3c9a903ad30abb2f6a919be3c"
  integrity sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=
  dependencies:
    function-bind "^1.1.1"
    get-intrinsic "^1.0.2"

call-me-maybe@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/call-me-maybe/download/call-me-maybe-1.0.1.tgz#26d208ea89e37b5cbde60250a15f031c16a4d66b"
  integrity sha1-JtII6onje1y95gJQoV8DHBak1ms=

callsites@^3.0.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/callsites/download/callsites-3.1.0.tgz?cache=0&sync_timestamp=1628464722297&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcallsites%2Fdownload%2Fcallsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camel-case@^4.1.2:
  version "4.1.2"
  resolved "https://registry.nlark.com/camel-case/download/camel-case-4.1.2.tgz#9728072a954f805228225a6deea6b38461e1bd5a"
  integrity sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=
  dependencies:
    pascal-case "^3.1.2"
    tslib "^2.0.3"

caniuse-lite@^1.0.30001280:
  version "1.0.30001282"
  resolved "https://registry.npmmirror.com/caniuse-lite/download/caniuse-lite-1.0.30001282.tgz?cache=0&sync_timestamp=1637135323561&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcaniuse-lite%2Fdownload%2Fcaniuse-lite-1.0.30001282.tgz#38c781ee0a90ccfe1fe7fefd00e43f5ffdcb96fd"
  integrity sha512-YhF/hG6nqBEllymSIjLtR2iWDDnChvhnVJqp+vloyt2tEHFG1yBR+ac2B/rOw0qOK0m0lEXU2dv4E/sMk5P9Kg==

capture-stack-trace@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/capture-stack-trace/download/capture-stack-trace-1.0.1.tgz#a6c0bbe1f38f3aa0b92238ecb6ff42c344d4135d"
  integrity sha1-psC74fOPOqC5Ijjstv9Cw0TUE10=

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1627646697260&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.2"
  resolved "https://registry.nlark.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646697260&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://registry.npmmirror.com/chardet/download/chardet-0.7.0.tgz?cache=0&sync_timestamp=1634639141514&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchardet%2Fdownload%2Fchardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

"chokidar@>=3.0.0 <4.0.0", chokidar@^3.5.2:
  version "3.5.2"
  resolved "https://registry.npmmirror.com/chokidar/download/chokidar-3.5.2.tgz#dba3976fcadb016f66fd365021d91600d01c1e75"
  integrity sha1-26OXb8rbAW9m/TZQIdkWANAcHnU=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chrome-trace-event@^1.0.2:
  version "1.0.3"
  resolved "https://registry.nlark.com/chrome-trace-event/download/chrome-trace-event-1.0.3.tgz#1015eced4741e15d06664a957dbbf50d041e26ac"
  integrity sha1-EBXs7UdB4V0GZkqVfbv1DQQeJqw=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "https://registry.nlark.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

clean-css@^5.1.5:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/clean-css/download/clean-css-5.2.2.tgz?cache=0&sync_timestamp=1634992314911&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fclean-css%2Fdownload%2Fclean-css-5.2.2.tgz#d3a7c6ee2511011e051719838bdcf8314dc4548d"
  integrity sha1-06fG7iURAR4FFxmDi9z4MU3EVI0=
  dependencies:
    source-map "~0.6.0"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/clean-stack/download/clean-stack-2.2.0.tgz?cache=0&sync_timestamp=1622605328444&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fclean-stack%2Fdownload%2Fclean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/cli-cursor/download/cli-cursor-3.1.0.tgz?cache=0&sync_timestamp=1629747481175&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcli-cursor%2Fdownload%2Fcli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-table@^0.3.1:
  version "0.3.8"
  resolved "https://registry.npmmirror.com/cli-table/download/cli-table-0.3.8.tgz#d3629cbf893459a8ad99671554e702a1c6d48a01"
  integrity sha512-5IO15fJRzgM+hHZjvQlqD6UPRuVGWR4Ny5ZzaM5VJxJEQqSIEVyVh9dMAUN6CBAVfMc4/6CFEzbhnRftLRCyug==
  dependencies:
    colors "1.0.3"
    strip-ansi "^6.0.1"

cli-width@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

clone-buffer@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/clone-buffer/download/clone-buffer-1.0.0.tgz#e3e25b207ac4e701af721e2cb5a16792cac3dc58"
  integrity sha1-4+JbIHrE5wGvch4staFnksrD3Fg=

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "https://registry.nlark.com/clone-deep/download/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone-stats@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/clone-stats/download/clone-stats-1.0.0.tgz#b3782dff8bb5474e18b9b6bf0fdfe782f8777680"
  integrity sha1-s3gt/4u1R04Yuba/D9/ngvh3doA=

clone@^2.1.1:
  version "2.1.2"
  resolved "https://registry.nlark.com/clone/download/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

cloneable-readable@^1.0.0:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/cloneable-readable/download/cloneable-readable-1.1.3.tgz#120a00cb053bfb63a222e709f9683ea2e11d8cec"
  integrity sha1-EgoAywU7+2OiIucJ+Wg+ouEdjOw=
  dependencies:
    inherits "^2.0.1"
    process-nextick-args "^2.0.0"
    readable-stream "^2.3.5"

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorette@^2.0.10, colorette@^2.0.14:
  version "2.0.16"
  resolved "https://registry.npmmirror.com/colorette/download/colorette-2.0.16.tgz#713b9af84fdb000139f04546bd4a93f62a5085da"
  integrity sha1-cTua+E/bAAE58EVGvUqT9ipQhdo=

colors@1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/colors/download/colors-1.0.3.tgz#0433f44d809680fdeb60ed260f1b0c262e82a40b"
  integrity sha1-BDP0TYCWgP3rYO0mDxsMJi6CpAs=

commander@^2.20.0, commander@^2.9.0:
  version "2.20.3"
  resolved "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^7.0.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-7.2.0.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-7.2.0.tgz#a36cb57d0b501ce108e4d20559a150a391d97ab7"
  integrity sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=

commander@^8.1.0:
  version "8.3.0"
  resolved "https://registry.npmmirror.com/commander/download/commander-8.3.0.tgz?cache=0&sync_timestamp=1634886396986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-8.3.0.tgz#4837ea1b2da67b9c616a67afbb0fafee567bca66"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "https://registry.nlark.com/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compressible@~2.0.16:
  version "2.0.18"
  resolved "https://registry.nlark.com/compressible/download/compressible-2.0.18.tgz#af53cca6b070d4c3c0750fbd77286a6d7cc46fba"
  integrity sha1-r1PMprBw1MPAdQ+9dyhqbXzEb7o=
  dependencies:
    mime-db ">= 1.43.0 < 2"

compression@^1.7.4:
  version "1.7.4"
  resolved "https://registry.npm.taobao.org/compression/download/compression-1.7.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcompression%2Fdownload%2Fcompression-1.7.4.tgz#95523eff170ca57c29a0ca41e6fe131f41e5bb8f"
  integrity sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=
  dependencies:
    accepts "~1.3.5"
    bytes "3.0.0"
    compressible "~2.0.16"
    debug "2.6.9"
    on-headers "~1.0.2"
    safe-buffer "5.1.2"
    vary "~1.1.2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.nlark.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

connect-history-api-fallback@^1.6.0:
  version "1.6.0"
  resolved "https://registry.nlark.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz?cache=0&sync_timestamp=1618847040596&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fconnect-history-api-fallback%2Fdownload%2Fconnect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

content-disposition@0.5.3:
  version "0.5.3"
  resolved "https://registry.nlark.com/content-disposition/download/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/cookie/download/cookie-0.4.0.tgz#beb437e7022b3b6d49019d088665303ebe9c14ba"
  integrity sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://registry.nlark.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cp-file@^9.1.0:
  version "9.1.0"
  resolved "https://registry.nlark.com/cp-file/download/cp-file-9.1.0.tgz#e98e30db72d57d47b5b1d444deb70d05e5684921"
  integrity sha1-6Y4w23LVfUe1sdRE3rcNBeVoSSE=
  dependencies:
    graceful-fs "^4.1.2"
    make-dir "^3.0.0"
    nested-error-stacks "^2.0.0"
    p-event "^4.1.0"

create-error-class@^3.0.0:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/create-error-class/download/create-error-class-3.0.2.tgz#06be7abef947a3f14a30fd610671d401bca8b7b6"
  integrity sha1-Br56vvlHo/FKMP1hBnHUAbyot7Y=
  dependencies:
    capture-stack-trace "^1.0.0"

cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "https://registry.nlark.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0, cross-spawn@^7.0.2, cross-spawn@^7.0.3:
  version "7.0.3"
  resolved "https://registry.nlark.com/cross-spawn/download/cross-spawn-7.0.3.tgz#f73a85b9d5d41d045551c177e2882d4ac85728a6"
  integrity sha1-9zqFudXUHQRVUcF34ogtSshXKKY=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-loader@^6.2.0:
  version "6.5.1"
  resolved "https://registry.npmmirror.com/css-loader/download/css-loader-6.5.1.tgz?cache=0&sync_timestamp=1635967924209&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcss-loader%2Fdownload%2Fcss-loader-6.5.1.tgz#0c43d4fbe0d97f699c91e9818cb585759091d1b1"
  integrity sha1-DEPU++DZf2mckemBjLWFdZCR0bE=
  dependencies:
    icss-utils "^5.1.0"
    postcss "^8.2.15"
    postcss-modules-extract-imports "^3.0.0"
    postcss-modules-local-by-default "^4.0.0"
    postcss-modules-scope "^3.0.0"
    postcss-modules-values "^4.0.0"
    postcss-value-parser "^4.1.0"
    semver "^7.3.5"

css-select@^4.1.3:
  version "4.1.3"
  resolved "https://registry.nlark.com/css-select/download/css-select-4.1.3.tgz?cache=0&sync_timestamp=1622994276976&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fcss-select%2Fdownload%2Fcss-select-4.1.3.tgz#a70440f70317f2669118ad74ff105e65849c7067"
  integrity sha1-pwRA9wMX8maRGK10/xBeZYSccGc=
  dependencies:
    boolbase "^1.0.0"
    css-what "^5.0.0"
    domhandler "^4.2.0"
    domutils "^2.6.0"
    nth-check "^2.0.0"

css-what@^5.0.0:
  version "5.1.0"
  resolved "https://registry.npmmirror.com/css-what/download/css-what-5.1.0.tgz#3f7b707aadf633baf62c2ceb8579b545bb40f7fe"
  integrity sha1-P3tweq32M7r2LCzrhXm1RbtA9/4=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

dargs@^6.1.0:
  version "6.1.0"
  resolved "https://registry.nlark.com/dargs/download/dargs-6.1.0.tgz?cache=0&sync_timestamp=1625033394899&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdargs%2Fdownload%2Fdargs-6.1.0.tgz#1f3b9b56393ecf8caa7cbfd6c31496ffcfb9b272"
  integrity sha1-HzubVjk+z4yqfL/WwxSW/8+5snI=

dateformat@^3.0.3:
  version "3.0.3"
  resolved "https://registry.npmmirror.com/dateformat/download/dateformat-3.0.3.tgz?cache=0&sync_timestamp=1634391114229&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdateformat%2Fdownload%2Fdateformat-3.0.3.tgz#a6e37499a4d9a9cf85ef5872044d62901c9889ae"
  integrity sha1-puN0maTZqc+F71hyBE1ikByYia4=

debug@2.6.9, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1636300872595&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@^3.1.1:
  version "3.2.7"
  resolved "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1636300872595&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.2:
  version "4.3.2"
  resolved "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz?cache=0&sync_timestamp=1636300872595&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-4.3.2.tgz#f0a49c18ac8779e31d4a0c6029dfb76873c7428b"
  integrity sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=
  dependencies:
    ms "2.1.2"

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

deep-equal@^1.0.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/deep-equal/download/deep-equal-1.1.1.tgz?cache=0&sync_timestamp=1606859714626&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.1.1.tgz#b5c98c942ceffaf7cb051e24e1434a25a2e6076a"
  integrity sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=
  dependencies:
    is-arguments "^1.0.4"
    is-date-object "^1.0.1"
    is-regex "^1.0.4"
    object-is "^1.0.1"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.2.0"

deep-extend@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npm.taobao.org/deep-extend/download/deep-extend-0.6.0.tgz#c4fa7c95404a17a9c3e8ca7e1537312b736330ac"
  integrity sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://registry.nlark.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

default-gateway@^6.0.0:
  version "6.0.3"
  resolved "https://registry.npmmirror.com/default-gateway/download/default-gateway-6.0.3.tgz#819494c888053bdb743edbf343d6cdf7f2943a71"
  integrity sha1-gZSUyIgFO9t0PtvzQ9bN9/KUOnE=
  dependencies:
    execa "^5.0.0"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/define-lazy-prop/download/define-lazy-prop-2.0.0.tgz#3f7ae421129bcaaac9bc74905c98a0009ec9ee7f"
  integrity sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=

define-properties@^1.1.3:
  version "1.1.3"
  resolved "https://registry.nlark.com/define-properties/download/define-properties-1.1.3.tgz?cache=0&sync_timestamp=1618847174317&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdefine-properties%2Fdownload%2Fdefine-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npm.taobao.org/del/download/del-6.0.0.tgz?cache=0&sync_timestamp=1601076882347&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdel%2Fdownload%2Fdel-6.0.0.tgz#0b40d0332cea743f1614f818be4feb717714c952"
  integrity sha1-C0DQMyzqdD8WFPgYvk/rcXcUyVI=
  dependencies:
    globby "^11.0.1"
    graceful-fs "^4.2.4"
    is-glob "^4.0.1"
    is-path-cwd "^2.2.0"
    is-path-inside "^3.0.2"
    p-map "^4.0.0"
    rimraf "^3.0.2"
    slash "^3.0.0"

depd@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/destroy/download/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-indent@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/detect-indent/download/detect-indent-0.2.0.tgz?cache=0&sync_timestamp=1628462018641&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdetect-indent%2Fdownload%2Fdetect-indent-0.2.0.tgz#042914498979ac2d9f3c73e4ff3e6877d3bc92b6"
  integrity sha1-BCkUSYl5rC2fPHPk/z5od9O8krY=
  dependencies:
    get-stdin "^0.1.0"
    minimist "^0.1.0"

detect-node@^2.0.4:
  version "2.1.0"
  resolved "https://registry.nlark.com/detect-node/download/detect-node-2.1.0.tgz?cache=0&sync_timestamp=1621146954463&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdetect-node%2Fdownload%2Fdetect-node-2.1.0.tgz#c9c70775a49c3d03bc2c06d9a73be550f978f8b1"
  integrity sha1-yccHdaScPQO8LAbZpzvlUPl4+LE=

diff@^3.5.0:
  version "3.5.0"
  resolved "https://registry.nlark.com/diff/download/diff-3.5.0.tgz?cache=0&sync_timestamp=1622685824253&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdiff%2Fdownload%2Fdiff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

diff@^4.0.1:
  version "4.0.2"
  resolved "https://registry.nlark.com/diff/download/diff-4.0.2.tgz?cache=0&sync_timestamp=1622685824253&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdiff%2Fdownload%2Fdiff-4.0.2.tgz#60f3aecb89d5fae520c11aa19efc2bb982aade7d"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dir-glob@2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.0.0.tgz#0b205d2b6aef98238ca286598a8204d29d0a0034"
  integrity sha1-CyBdK2rvmCOMooZZioIE0p0KADQ=
  dependencies:
    arrify "^1.0.1"
    path-type "^3.0.0"

dir-glob@^2.2.2:
  version "2.2.2"
  resolved "https://registry.npm.taobao.org/dir-glob/download/dir-glob-2.2.2.tgz#fa09f0694153c8918b18ba0deafae94769fc50c4"
  integrity sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=
  dependencies:
    path-type "^3.0.0"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dns-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/dns-equal/download/dns-equal-1.0.0.tgz#b39e7f1da6eb0a75ba9c17324b34753c47e0654d"
  integrity sha1-s55/HabrCnW6nBcySzR1PEfgZU0=

dns-packet@^1.3.1:
  version "1.3.4"
  resolved "https://registry.nlark.com/dns-packet/download/dns-packet-1.3.4.tgz#e3455065824a2507ba886c55a89963bb107dec6f"
  integrity sha1-40VQZYJKJQe6iGxVqJljuxB97G8=
  dependencies:
    ip "^1.1.0"
    safe-buffer "^5.0.1"

dns-txt@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/dns-txt/download/dns-txt-2.0.2.tgz#b91d806f5d27188e4ab3e7d107d881a1cc4642b6"
  integrity sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=
  dependencies:
    buffer-indexof "^1.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-converter@^0.2.0:
  version "0.2.0"
  resolved "https://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz#6721a9daee2e293682955b6afe416771627bb768"
  integrity sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=
  dependencies:
    utila "~0.4"

dom-serializer@^1.0.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/dom-serializer/download/dom-serializer-1.3.2.tgz?cache=0&sync_timestamp=1621256830355&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdom-serializer%2Fdownload%2Fdom-serializer-1.3.2.tgz#6206437d32ceefaec7161803230c7a20bc1b4d91"
  integrity sha1-YgZDfTLO767HFhgDIwx6ILwbTZE=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.2.0"
    entities "^2.0.0"

domelementtype@^2.0.1, domelementtype@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/domelementtype/download/domelementtype-2.2.0.tgz?cache=0&sync_timestamp=1617298545989&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdomelementtype%2Fdownload%2Fdomelementtype-2.2.0.tgz#9a0b6c2782ed6a1c7323d42267183df9bd8b1d57"
  integrity sha1-mgtsJ4LtahxzI9QiZxg9+b2LHVc=

domhandler@^4.0.0, domhandler@^4.2.0:
  version "4.2.2"
  resolved "https://registry.nlark.com/domhandler/download/domhandler-4.2.2.tgz?cache=0&sync_timestamp=1630246778110&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomhandler%2Fdownload%2Fdomhandler-4.2.2.tgz#e825d721d19a86b8c201a35264e226c678ee755f"
  integrity sha1-6CXXIdGahrjCAaNSZOImxnjudV8=
  dependencies:
    domelementtype "^2.2.0"

domutils@^2.5.2, domutils@^2.6.0:
  version "2.8.0"
  resolved "https://registry.nlark.com/domutils/download/domutils-2.8.0.tgz?cache=0&sync_timestamp=1630106606599&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fdomutils%2Fdownload%2Fdomutils-2.8.0.tgz#4437def5db6e2d1f5d6ee859bd95ca7d02048135"
  integrity sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=
  dependencies:
    dom-serializer "^1.0.1"
    domelementtype "^2.2.0"
    domhandler "^4.2.0"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/dot-case/download/dot-case-3.0.4.tgz?cache=0&sync_timestamp=1606867327042&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fdot-case%2Fdownload%2Fdot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

download-stats@^0.3.4:
  version "0.3.4"
  resolved "https://registry.npm.taobao.org/download-stats/download/download-stats-0.3.4.tgz#67ea0c32f14acd9f639da704eef509684ba2dae7"
  integrity sha1-Z+oMMvFKzZ9jnacE7vUJaEui2uc=
  dependencies:
    JSONStream "^1.2.1"
    lazy-cache "^2.0.1"
    moment "^2.15.1"

dts-bundle-webpack@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/dts-bundle-webpack/download/dts-bundle-webpack-1.0.2.tgz#c50439a89e71fd9e42800092561c05d5c3ea9ea6"
  integrity sha1-xQQ5qJ5x/Z5CgACSVhwF1cPqnqY=
  dependencies:
    dts-bundle "^0.7.3"

dts-bundle@^0.7.3:
  version "0.7.3"
  resolved "https://registry.nlark.com/dts-bundle/download/dts-bundle-0.7.3.tgz#372b7bb69c820782e6382f400739a69dced3d59a"
  integrity sha1-Nyt7tpyCB4LmOC9ABzmmnc7T1Zo=
  dependencies:
    "@types/detect-indent" "0.1.30"
    "@types/glob" "5.0.30"
    "@types/mkdirp" "0.3.29"
    "@types/node" "8.0.0"
    commander "^2.9.0"
    detect-indent "^0.2.0"
    glob "^6.0.4"
    mkdirp "^0.5.0"

duplexer3@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npmmirror.com/duplexer3/download/duplexer3-0.1.4.tgz#ee01dd1cac0ed3cbc7fdbea37dc0a8f1ce002ce2"
  integrity sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=

dxf-parser@^1.0.0-alpha.2:
  version "1.1.2"
  resolved "https://registry.npmmirror.com/dxf-parser/download/dxf-parser-1.1.2.tgz#0b3ba561cd84b5945bee17403e235599bee8c50b"
  integrity sha512-GPTumUvRkounlIazLIyJMmTWt+nlg+ksS0Hdm8jWvejmZKBTz6gvHTam76wRm4PQMma5sgKLThblQyeIJcH79Q==
  dependencies:
    loglevel "^1.7.1"

earcut@^2.2.3:
  version "2.2.3"
  resolved "https://registry.nlark.com/earcut/download/earcut-2.2.3.tgz#d44ced2ff5a18859568e327dd9c7d46b16f55cf4"
  integrity sha1-1EztL/WhiFlWjjJ92cfUaxb1XPQ=

editions@^2.2.0:
  version "2.3.1"
  resolved "https://registry.nlark.com/editions/download/editions-2.3.1.tgz#3bc9962f1978e801312fbd0aebfed63b49bfe698"
  integrity sha1-O8mWLxl46AExL70K6/7WO0m/5pg=
  dependencies:
    errlop "^2.0.0"
    semver "^6.3.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.4"
  resolved "https://registry.npmmirror.com/ejs/download/ejs-2.7.4.tgz#48661287573dcc53e366c7a1ae52c3a120eec9ba"
  integrity sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=

ejs@^3.1.5:
  version "3.1.6"
  resolved "https://registry.npmmirror.com/ejs/download/ejs-3.1.6.tgz#5bfd0a0689743bb5268b3550cceeebbc1702822a"
  integrity sha1-W/0KBol0O7UmizVQzO7rvBcCgio=
  dependencies:
    jake "^10.6.1"

electron-to-chromium@^1.3.896:
  version "1.3.903"
  resolved "https://registry.npmmirror.com/electron-to-chromium/download/electron-to-chromium-1.3.903.tgz?cache=0&sync_timestamp=1637290996282&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Felectron-to-chromium%2Fdownload%2Felectron-to-chromium-1.3.903.tgz#e2d3c3809f4ef05fdbe5cc88969dfc94b1bd15b9"
  integrity sha512-+PnYAyniRRTkNq56cqYDLq9LyklZYk0hqoDy9GpcU11H5QjRmFZVDbxtgHUMK/YzdNTcn1XWP5gb+hFlSCr20g==

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

encodeurl@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "https://registry.nlark.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^5.0.0, enhanced-resolve@^5.8.3:
  version "5.8.3"
  resolved "https://registry.nlark.com/enhanced-resolve/download/enhanced-resolve-5.8.3.tgz?cache=0&sync_timestamp=1632130769099&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fenhanced-resolve%2Fdownload%2Fenhanced-resolve-5.8.3.tgz#6d552d465cce0423f5b3d718511ea53826a7b2f0"
  integrity sha1-bVUtRlzOBCP1s9cYUR6lOCansvA=
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enquirer@^2.3.5:
  version "2.3.6"
  resolved "https://registry.nlark.com/enquirer/download/enquirer-2.3.6.tgz#2a7fe5dd634a1e4125a975ec994ff5456dc3734d"
  integrity sha1-Kn/l3WNKHkElqXXsmU/1RW3Dc00=
  dependencies:
    ansi-colors "^4.1.1"

entities@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/entities/download/entities-2.2.0.tgz?cache=0&sync_timestamp=1628508189267&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fentities%2Fdownload%2Fentities-2.2.0.tgz#098dc90ebb83d8dffa089d55256b351d34c4da55"
  integrity sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=

envinfo@^7.7.3:
  version "7.8.1"
  resolved "https://registry.npm.taobao.org/envinfo/download/envinfo-7.8.1.tgz?cache=0&sync_timestamp=1617673061720&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fenvinfo%2Fdownload%2Fenvinfo-7.8.1.tgz#06377e3e5f4d379fea7ac592d5ad8927e0c4d475"
  integrity sha1-Bjd+Pl9NN5/qesWS1a2JJ+DE1HU=

errlop@^2.0.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/errlop/download/errlop-2.2.0.tgz#1ff383f8f917ae328bebb802d6ca69666a42d21b"
  integrity sha1-H/OD+PkXrjKL67gC1sppZmpC0hs=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://registry.nlark.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error@^7.0.2:
  version "7.2.1"
  resolved "https://registry.npm.taobao.org/error/download/error-7.2.1.tgz#eab21a4689b5f684fc83da84a0e390de82d94894"
  integrity sha1-6rIaRom19oT8g9qEoOOQ3oLZSJQ=
  dependencies:
    string-template "~0.2.1"

es-module-lexer@^0.9.0:
  version "0.9.3"
  resolved "https://registry.npmmirror.com/es-module-lexer/download/es-module-lexer-0.9.3.tgz?cache=0&sync_timestamp=1633645560577&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fes-module-lexer%2Fdownload%2Fes-module-lexer-0.9.3.tgz#6f13db00cc38417137daf74366f535c8eb438f19"
  integrity sha1-bxPbAMw4QXE32vdDZvU1yOtDjxk=

escalade@^3.1.1:
  version "3.1.1"
  resolved "https://registry.nlark.com/escalade/download/escalade-3.1.1.tgz#d8cfdc7000965c5a0174b4a82eaa5c0552742e40"
  integrity sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-plugin-prettier@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/eslint-plugin-prettier/download/eslint-plugin-prettier-4.0.0.tgz#8b99d1e4b8b24a762472b4567992023619cb98e0"
  integrity sha1-i5nR5LiySnYkcrRWeZICNhnLmOA=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-scope@5.1.1, eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-5.1.1.tgz?cache=0&sync_timestamp=1637044418832&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/eslint-scope/download/eslint-scope-6.0.0.tgz?cache=0&sync_timestamp=1637044418832&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-scope%2Fdownload%2Feslint-scope-6.0.0.tgz#9cf45b13c5ac8f3d4c50f46a5121f61b3e318978"
  integrity sha1-nPRbE8Wsjz1MUPRqUSH2Gz4xiXg=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/eslint-utils/download/eslint-utils-3.0.0.tgz#8aebaface7345bb33559db0a1f13a1d2d48c3672"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-2.1.0.tgz#f65328259305927392c938ed44eb0a5c9b2bd303"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/eslint-visitor-keys/download/eslint-visitor-keys-3.1.0.tgz#eee4acea891814cda67a7d8812d9647dd0179af2"
  integrity sha512-yWJFpu4DtjsWKkt5GeNBBuZMlNcYVs6vRCLoCVEJrTjaSB6LC98gFipNK/erM2Heg/E8mIK+hXG/pJMLK+eRZA==

eslint-webpack-plugin@^3.0.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/eslint-webpack-plugin/download/eslint-webpack-plugin-3.1.1.tgz?cache=0&sync_timestamp=1636733170890&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint-webpack-plugin%2Fdownload%2Feslint-webpack-plugin-3.1.1.tgz#83dad2395e5f572d6f4d919eedaa9cf902890fcb"
  integrity sha512-xSucskTN9tOkfW7so4EaiFIkulWLXwCB/15H917lR6pTv0Zot6/fetFucmENRb7J5whVSFKIvwnrnsa78SG2yg==
  dependencies:
    "@types/eslint" "^7.28.2"
    jest-worker "^27.3.1"
    micromatch "^4.0.4"
    normalize-path "^3.0.0"
    schema-utils "^3.1.1"

eslint@^8.0.0:
  version "8.2.0"
  resolved "https://registry.npmmirror.com/eslint/download/eslint-8.2.0.tgz?cache=0&sync_timestamp=1636156099337&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Feslint%2Fdownload%2Feslint-8.2.0.tgz#44d3fb506d0f866a506d97a0fc0e90ee6d06a815"
  integrity sha1-RNP7UG0PhmpQbZeg/A6Q7m0GqBU=
  dependencies:
    "@eslint/eslintrc" "^1.0.4"
    "@humanwhocodes/config-array" "^0.6.0"
    ajv "^6.10.0"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    enquirer "^2.3.5"
    escape-string-regexp "^4.0.0"
    eslint-scope "^6.0.0"
    eslint-utils "^3.0.0"
    eslint-visitor-keys "^3.0.0"
    espree "^9.0.0"
    esquery "^1.4.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^6.0.1"
    globals "^13.6.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.0.4"
    natural-compare "^1.4.0"
    optionator "^0.9.1"
    progress "^2.0.0"
    regexpp "^3.2.0"
    semver "^7.2.1"
    strip-ansi "^6.0.1"
    strip-json-comments "^3.1.0"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^9.0.0:
  version "9.0.0"
  resolved "https://registry.nlark.com/espree/download/espree-9.0.0.tgz?cache=0&sync_timestamp=1631307190960&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fespree%2Fdownload%2Fespree-9.0.0.tgz#e90a2965698228502e771c7a58489b1a9d107090"
  integrity sha1-6QopZWmCKFAudxx6WEibGp0QcJA=
  dependencies:
    acorn "^8.5.0"
    acorn-jsx "^5.3.1"
    eslint-visitor-keys "^3.0.0"

esquery@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/esquery/download/esquery-1.4.0.tgz#2148ffc38b82e8c7057dfed48425b3e61f0f24a5"
  integrity sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-4.3.0.tgz?cache=0&sync_timestamp=1635237706876&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "https://registry.npmmirror.com/estraverse/download/estraverse-5.3.0.tgz?cache=0&sync_timestamp=1635237706876&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Festraverse%2Fdownload%2Festraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://registry.nlark.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.nlark.com/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^4.0.0:
  version "4.0.7"
  resolved "https://registry.nlark.com/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

events@^3.2.0:
  version "3.3.0"
  resolved "https://registry.npmmirror.com/events/download/events-3.3.0.tgz#31a95ad0a924e2d2c419a813aeb2c4e878ea7400"
  integrity sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=

execa@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/execa/download/execa-4.1.0.tgz?cache=0&sync_timestamp=1637147262608&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-4.1.0.tgz#4e5491ad1572f2f17a77d388c6c857135b22847a"
  integrity sha1-TlSRrRVy8vF6d9OIxshXE1sihHo=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

execa@^5.0.0:
  version "5.1.1"
  resolved "https://registry.npmmirror.com/execa/download/execa-5.1.1.tgz?cache=0&sync_timestamp=1637147262608&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fexeca%2Fdownload%2Fexeca-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "https://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

express@^4.17.1:
  version "4.17.1"
  resolved "https://registry.nlark.com/express/download/express-4.17.1.tgz?cache=0&sync_timestamp=1618847120573&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fexpress%2Fdownload%2Fexpress-4.17.1.tgz#4491fc38605cf51f8629d39c2b5d026f98a4c134"
  integrity sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

external-editor@^3.0.3:
  version "3.1.0"
  resolved "https://registry.nlark.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/fast-diff/download/fast-diff-1.2.0.tgz#73ee11982d86caaf7959828d519cfe927fac5f03"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-glob@^2.0.2, fast-glob@^2.2.6:
  version "2.2.7"
  resolved "https://registry.nlark.com/fast-glob/download/fast-glob-2.2.7.tgz#6953857c3afa475fff92ee6015d52da70a4cd39d"
  integrity sha1-aVOFfDr6R1//ku5gFdUtpwpM050=
  dependencies:
    "@mrmlnc/readdir-enhanced" "^2.2.1"
    "@nodelib/fs.stat" "^1.1.2"
    glob-parent "^3.1.0"
    is-glob "^4.0.0"
    merge2 "^1.2.3"
    micromatch "^3.1.10"

fast-glob@^3.1.1:
  version "3.2.7"
  resolved "https://registry.nlark.com/fast-glob/download/fast-glob-3.2.7.tgz#fd6cb7a2d7e9aa7a7846111e85a196d6b2f766a1"
  integrity sha1-/Wy3otfpqnp4RhEehaGW1rL3ZqE=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastest-levenshtein@^1.0.12:
  version "1.0.12"
  resolved "https://registry.npm.taobao.org/fastest-levenshtein/download/fastest-levenshtein-1.0.12.tgz#9990f7d3a88cc5a9ffd1f1745745251700d497e2"
  integrity sha1-mZD306iMxan/0fF0V0UlFwDUl+I=

fastq@^1.6.0:
  version "1.13.0"
  resolved "https://registry.nlark.com/fastq/download/fastq-1.13.0.tgz#616760f88a7526bdfc596b7cab8c18938c36b98c"
  integrity sha1-YWdg+Ip1Jr38WWt8q4wYk4w2uYw=
  dependencies:
    reusify "^1.0.4"

faye-websocket@^0.11.3:
  version "0.11.4"
  resolved "https://registry.nlark.com/faye-websocket/download/faye-websocket-0.11.4.tgz#7f0d9275cfdd86a1c963dc8b65fcc451edcbb1da"
  integrity sha1-fw2Sdc/dhqHJY9yLZfzEUe3Lsdo=
  dependencies:
    websocket-driver ">=0.5.1"

figures@^3.0.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/figures/download/figures-3.2.0.tgz?cache=0&sync_timestamp=1625254307578&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffigures%2Fdownload%2Ffigures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-6.0.1.tgz?cache=0&sync_timestamp=1613794357372&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffile-entry-cache%2Fdownload%2Ffile-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

filelist@^1.0.1:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/filelist/download/filelist-1.0.2.tgz#80202f21462d4d1c2e214119b1807c1bc0380e5b"
  integrity sha1-gCAvIUYtTRwuIUEZsYB8G8A4Dls=
  dependencies:
    minimatch "^3.0.4"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "https://registry.nlark.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/finalhandler/download/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0:
  version "4.1.0"
  resolved "https://registry.npmmirror.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

first-chunk-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/first-chunk-stream/download/first-chunk-stream-2.0.0.tgz#1bdecdb8e083c0664b91945581577a43a9f31d70"
  integrity sha1-G97NuOCDwGZLkZRVgVd6Q6nzHXA=
  dependencies:
    readable-stream "^2.0.2"

flat-cache@^3.0.4:
  version "3.0.4"
  resolved "https://registry.nlark.com/flat-cache/download/flat-cache-3.0.4.tgz#61b0338302b2fe9f957dcc32fc2a87f1c3048b11"
  integrity sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=
  dependencies:
    flatted "^3.1.0"
    rimraf "^3.0.2"

flatted@^3.1.0:
  version "3.2.4"
  resolved "https://registry.npmmirror.com/flatted/download/flatted-3.2.4.tgz?cache=0&sync_timestamp=1636473847692&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflatted%2Fdownload%2Fflatted-3.2.4.tgz#28d9969ea90661b5134259f312ab6aa7929ac5e2"
  integrity sha512-8/sOawo8tJ4QOBX8YlQBMxL8+RLZfxMQOif9o0KUKTNTjMYElWPE0r/m5VNFxTRd0NSw8qSy8dajrwX4RYI1Hw==

follow-redirects@^1.0.0, follow-redirects@^1.14.0:
  version "1.14.5"
  resolved "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.14.5.tgz?cache=0&sync_timestamp=1635857764332&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.14.5.tgz#f09a5848981d3c772b5392309778523f8d85c381"
  integrity sha1-8JpYSJgdPHcrU5Iwl3hSP42Fw4E=

for-in@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forwarded@0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/forwarded/download/forwarded-0.2.0.tgz?cache=0&sync_timestamp=1622503408398&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fforwarded%2Fdownload%2Fforwarded-0.2.0.tgz#2269936428aad4c15c7ebe9779a84bf0b2a81811"
  integrity sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "https://registry.nlark.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

fs-monkey@1.0.3:
  version "1.0.3"
  resolved "https://registry.npm.taobao.org/fs-monkey/download/fs-monkey-1.0.3.tgz?cache=0&sync_timestamp=1617593290175&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Ffs-monkey%2Fdownload%2Ffs-monkey-1.0.3.tgz#ae3ac92d53bb328efe0e9a1d9541f6ad8d48e2d3"
  integrity sha1-rjrJLVO7Mo7+DpodlUH2rY1I4tM=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.2"
  resolved "https://registry.npmmirror.com/fsevents/download/fsevents-2.3.2.tgz#8a526f78b8fdf4623b709e0b975c52c24c02fd1a"
  integrity sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=

function-bind@^1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz?cache=0&sync_timestamp=1622605293770&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ffunctional-red-black-tree%2Fdownload%2Ffunctional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

get-intrinsic@^1.0.2:
  version "1.1.1"
  resolved "https://registry.nlark.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz#15f59f376f855c446963948f0d24cd3637b4abc6"
  integrity sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=
  dependencies:
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.1"

get-stdin@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/get-stdin/download/get-stdin-0.1.0.tgz#5998af24aafc802d15c82c685657eeb8b10d4a91"
  integrity sha1-WZivJKr8gC0VyCxoVlfuuLENSpE=

get-stream@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz#8e943d1358dc37555054ecbe2edb05aa174ede14"
  integrity sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=

get-stream@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-5.2.0.tgz#4966a1795ee5ace65e706c4b7beb71257d6e22d3"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-stream@^6.0.0:
  version "6.0.1"
  resolved "https://registry.npm.taobao.org/get-stream/download/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "https://registry.nlark.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

gh-got@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/gh-got/download/gh-got-5.0.0.tgz#ee95be37106fd8748a96f8d1db4baea89e1bfa8a"
  integrity sha1-7pW+NxBv2HSKlvjR20uuqJ4b+oo=
  dependencies:
    got "^6.2.0"
    is-plain-obj "^1.1.0"

github-username@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/github-username/download/github-username-3.0.0.tgz#0a772219b3130743429f2456d0bdd3db55dce7b1"
  integrity sha1-CnciGbMTB0NCnyRW0L3T21Xc57E=
  dependencies:
    gh-got "^5.0.0"

gl-matrix@^3.3.0:
  version "3.4.3"
  resolved "https://registry.npmmirror.com/gl-matrix/download/gl-matrix-3.4.3.tgz#fc1191e8320009fd4d20e9339595c6041ddc22c9"
  integrity sha1-/BGR6DIACf1NIOkzlZXGBB3cIsk=

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-3.1.0.tgz?cache=0&sync_timestamp=1632953697891&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz?cache=0&sync_timestamp=1632953697891&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.1:
  version "6.0.2"
  resolved "https://registry.npmmirror.com/glob-parent/download/glob-parent-6.0.2.tgz?cache=0&sync_timestamp=1632953697891&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob-parent%2Fdownload%2Fglob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob-to-regexp@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz#8c5a1494d2066c570cc3bfe4496175acc4d502ab"
  integrity sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=

glob-to-regexp@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/glob-to-regexp/download/glob-to-regexp-0.4.1.tgz#c75297087c851b9a578bd217dd59a92f59fe546e"
  integrity sha1-x1KXCHyFG5pXi9IX3VmpL1n+VG4=

glob@^6.0.4:
  version "6.0.4"
  resolved "https://registry.npmmirror.com/glob/download/glob-6.0.4.tgz#0f08860f6a155127b2fadd4f9ce24b1aab6e4d22"
  integrity sha1-DwiGD2oVUSey+t1PnOJLGqtuTSI=
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.0, glob@^7.1.2, glob@^7.1.3, glob@^7.1.4, glob@^7.2.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz#d15535af7732e02e948f4c41628bd910293f6023"
  integrity sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^13.6.0, globals@^13.9.0:
  version "13.12.0"
  resolved "https://registry.npmmirror.com/globals/download/globals-13.12.0.tgz?cache=0&sync_timestamp=1635390798667&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglobals%2Fdownload%2Fglobals-13.12.0.tgz#4d733760304230a0082ed96e21e5c565f898089e"
  integrity sha1-TXM3YDBCMKAILtluIeXFZfiYCJ4=
  dependencies:
    type-fest "^0.20.2"

globby@^11.0.1, globby@^11.0.4:
  version "11.0.4"
  resolved "https://registry.nlark.com/globby/download/globby-11.0.4.tgz#2cbaff77c2f2a62e71e9b2813a67b97a3a3001a5"
  integrity sha1-LLr/d8Lypi5x6bKBOme5ejowAaU=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.1.1"
    ignore "^5.1.4"
    merge2 "^1.3.0"
    slash "^3.0.0"

globby@^8.0.1:
  version "8.0.2"
  resolved "https://registry.nlark.com/globby/download/globby-8.0.2.tgz#5697619ccd95c5275dbb2d6faa42087c1a941d8d"
  integrity sha1-VpdhnM2VxSdduy1vqkIIfBqUHY0=
  dependencies:
    array-union "^1.0.1"
    dir-glob "2.0.0"
    fast-glob "^2.0.2"
    glob "^7.1.2"
    ignore "^3.3.5"
    pify "^3.0.0"
    slash "^1.0.0"

globby@^9.2.0:
  version "9.2.0"
  resolved "https://registry.nlark.com/globby/download/globby-9.2.0.tgz#fd029a706c703d29bdd170f4b6db3a3f7a7cb63d"
  integrity sha1-/QKacGxwPSm90XD0tts6P3p8tj0=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^1.0.2"
    dir-glob "^2.2.2"
    fast-glob "^2.2.6"
    glob "^7.1.3"
    ignore "^4.0.3"
    pify "^4.0.1"
    slash "^2.0.0"

got@^6.2.0:
  version "6.7.1"
  resolved "https://registry.npmmirror.com/got/download/got-6.7.1.tgz?cache=0&sync_timestamp=1637211971121&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fgot%2Fdownload%2Fgot-6.7.1.tgz#240cd05785a9a18e561dc1b44b41c763ef1e8db0"
  integrity sha1-JAzQV4WpoY5WHcG0S0HHY+8ejbA=
  dependencies:
    create-error-class "^3.0.0"
    duplexer3 "^0.1.4"
    get-stream "^3.0.0"
    is-redirect "^1.0.0"
    is-retry-allowed "^1.0.0"
    is-stream "^1.0.0"
    lowercase-keys "^1.0.0"
    safe-buffer "^5.0.1"
    timed-out "^4.0.0"
    unzip-response "^2.0.1"
    url-parse-lax "^1.0.0"

graceful-fs@^4.1.2, graceful-fs@^4.2.4, graceful-fs@^4.2.6:
  version "4.2.8"
  resolved "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz#e412b8d33f5e006593cbd3cee6df9f2cebbe802a"
  integrity sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo=

grouped-queue@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/grouped-queue/download/grouped-queue-1.1.0.tgz#63e3f9ca90af952269d1d40879e41221eacc74cb"
  integrity sha1-Y+P5ypCvlSJp0dQIeeQSIerMdMs=
  dependencies:
    lodash "^4.17.15"

handle-thing@^2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/handle-thing/download/handle-thing-2.0.1.tgz#857f79ce359580c340d43081cc648970d0bb234e"
  integrity sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.1, has-symbols@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/has-symbols/download/has-symbols-1.0.2.tgz#165d3070c00309752a1236a479331e3ac56f1423"
  integrity sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=

has-tostringtag@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz?cache=0&sync_timestamp=1628196402801&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhas-tostringtag%2Fdownload%2Fhas-tostringtag-1.0.0.tgz#7e133818a7d394734f941e73c3d3f9291e658b25"
  integrity sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=
  dependencies:
    has-symbols "^1.0.2"

has-value@^0.3.1:
  version "0.3.1"
  resolved "https://registry.nlark.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.3:
  version "1.0.3"
  resolved "https://registry.nlark.com/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

he@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/he/download/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://registry.nlark.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

hpack.js@^2.1.6:
  version "2.1.6"
  resolved "https://registry.nlark.com/hpack.js/download/hpack.js-2.1.6.tgz#87774c0949e513f42e84575b3c45681fade2a0b2"
  integrity sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=
  dependencies:
    inherits "^2.0.1"
    obuf "^1.0.0"
    readable-stream "^2.0.1"
    wbuf "^1.1.0"

html-entities@^2.3.2:
  version "2.3.2"
  resolved "https://registry.nlark.com/html-entities/download/html-entities-2.3.2.tgz#760b404685cb1d794e4f4b744332e3b00dcfe488"
  integrity sha1-dgtARoXLHXlOT0t0QzLjsA3P5Ig=

html-minifier-terser@^6.0.2:
  version "6.0.2"
  resolved "https://registry.nlark.com/html-minifier-terser/download/html-minifier-terser-6.0.2.tgz#14059ad64b69bf9f8b8a33f25b53411d8321e75d"
  integrity sha1-FAWa1ktpv5+LijPyW1NBHYMh510=
  dependencies:
    camel-case "^4.1.2"
    clean-css "^5.1.5"
    commander "^8.1.0"
    he "^1.2.0"
    param-case "^3.0.4"
    relateurl "^0.2.7"
    terser "^5.7.2"

html-webpack-plugin@^5.3.2:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/html-webpack-plugin/download/html-webpack-plugin-5.5.0.tgz#c3911936f57681c1f9f4d8b68c158cd9dfe52f50"
  integrity sha1-w5EZNvV2gcH59Ni2jBWM2d/lL1A=
  dependencies:
    "@types/html-minifier-terser" "^6.0.0"
    html-minifier-terser "^6.0.2"
    lodash "^4.17.21"
    pretty-error "^4.0.0"
    tapable "^2.0.0"

htmlparser2@^6.1.0:
  version "6.1.0"
  resolved "https://registry.npmmirror.com/htmlparser2/download/htmlparser2-6.1.0.tgz?cache=0&sync_timestamp=1636640940074&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhtmlparser2%2Fdownload%2Fhtmlparser2-6.1.0.tgz#c4d762b6c3371a05dbe65e94ae43a9f845fb8fb7"
  integrity sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=
  dependencies:
    domelementtype "^2.0.1"
    domhandler "^4.0.0"
    domutils "^2.5.2"
    entities "^2.0.0"

http-deceiver@^1.2.7:
  version "1.2.7"
  resolved "https://registry.nlark.com/http-deceiver/download/http-deceiver-1.2.7.tgz#fa7168944ab9a519d337cb0bec7284dc3e723d87"
  integrity sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=

http-errors@1.7.2:
  version "1.7.2"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.2.tgz?cache=0&sync_timestamp=1636932154238&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.2.tgz#4f5029cf13239f31036e5b2e55292bcfbcc85c8f"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1636932154238&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-errors@~1.7.2:
  version "1.7.3"
  resolved "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1636932154238&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-parser-js@>=0.5.1:
  version "0.5.3"
  resolved "https://registry.npm.taobao.org/http-parser-js/download/http-parser-js-0.5.3.tgz#01d2709c79d41698bb01d4decc5e9da4e4a033d9"
  integrity sha1-AdJwnHnUFpi7AdTezF6dpOSgM9k=

http-proxy-middleware@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/http-proxy-middleware/download/http-proxy-middleware-2.0.1.tgz#7ef3417a479fb7666a571e09966c66a39bd2c15f"
  integrity sha1-fvNBekeft2ZqVx4Jlmxmo5vSwV8=
  dependencies:
    "@types/http-proxy" "^1.17.5"
    http-proxy "^1.18.1"
    is-glob "^4.0.1"
    is-plain-obj "^3.0.0"
    micromatch "^4.0.2"

http-proxy@^1.18.1:
  version "1.18.1"
  resolved "https://registry.nlark.com/http-proxy/download/http-proxy-1.18.1.tgz#401541f0534884bbf95260334e72f88ee3976549"
  integrity sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=
  dependencies:
    eventemitter3 "^4.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

human-signals@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/human-signals/download/human-signals-1.1.1.tgz?cache=0&sync_timestamp=1624364695595&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhuman-signals%2Fdownload%2Fhuman-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/human-signals/download/human-signals-2.1.0.tgz?cache=0&sync_timestamp=1624364695595&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fhuman-signals%2Fdownload%2Fhuman-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

iconv-lite@0.4.24, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://registry.nlark.com/iconv-lite/download/iconv-lite-0.4.24.tgz?cache=0&sync_timestamp=1621826342262&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ficonv-lite%2Fdownload%2Ficonv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^5.0.0, icss-utils@^5.1.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/icss-utils/download/icss-utils-5.1.0.tgz#c6be6858abd013d768e98366ae47e25d5887b1ae"
  integrity sha1-xr5oWKvQE9do6YNmrkfiXViHsa4=

ignore@^3.3.5:
  version "3.3.10"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-3.3.10.tgz?cache=0&sync_timestamp=1635926632542&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-3.3.10.tgz#0a97fb876986e8081c631160f8f9f389157f0043"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.3, ignore@^4.0.6:
  version "4.0.6"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-4.0.6.tgz?cache=0&sync_timestamp=1635926632542&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.1.4, ignore@^5.1.8:
  version "5.1.9"
  resolved "https://registry.npmmirror.com/ignore/download/ignore-5.1.9.tgz?cache=0&sync_timestamp=1635926632542&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fignore%2Fdownload%2Fignore-5.1.9.tgz#9ec1a5cbe8e1446ec60d4420060d43aa6e7382fb"
  integrity sha1-nsGly+jhRG7GDUQgBg1Dqm5zgvs=

import-fresh@^3.0.0, import-fresh@^3.2.1:
  version "3.3.0"
  resolved "https://registry.nlark.com/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.0.3"
  resolved "https://registry.npmmirror.com/import-local/download/import-local-3.0.3.tgz?cache=0&sync_timestamp=1633327317807&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fimport-local%2Fdownload%2Fimport-local-3.0.3.tgz#4d51c2c495ca9393da259ec66b62e022920211e0"
  integrity sha1-TVHCxJXKk5PaJZ7Ga2LgIpICEeA=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/indent-string/download/indent-string-4.0.0.tgz?cache=0&sync_timestamp=1622604582006&other_urls=https%3A%2F%2Fregistry.nlark.com%2Findent-string%2Fdownload%2Findent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.nlark.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.3:
  version "2.0.3"
  resolved "https://registry.nlark.com/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^7.1.0:
  version "7.3.3"
  resolved "https://registry.npmmirror.com/inquirer/download/inquirer-7.3.3.tgz?cache=0&sync_timestamp=1633472964335&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Finquirer%2Fdownload%2Finquirer-7.3.3.tgz#04d176b2af04afc157a83fd7c100e98ee0aad003"
  integrity sha1-BNF2sq8Er8FXqD/XwQDpjuCq0AM=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.19"
    mute-stream "0.0.8"
    run-async "^2.4.0"
    rxjs "^6.6.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"

internal-ip@^6.2.0:
  version "6.2.0"
  resolved "https://registry.npmmirror.com/internal-ip/download/internal-ip-6.2.0.tgz?cache=0&sync_timestamp=1634404775654&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Finternal-ip%2Fdownload%2Finternal-ip-6.2.0.tgz#d5541e79716e406b74ac6b07b856ef18dc1621c1"
  integrity sha1-1VQeeXFuQGt0rGsHuFbvGNwWIcE=
  dependencies:
    default-gateway "^6.0.0"
    ipaddr.js "^1.9.1"
    is-ip "^3.1.0"
    p-event "^4.2.0"

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

interpret@^2.2.0:
  version "2.2.0"
  resolved "https://registry.npm.taobao.org/interpret/download/interpret-2.2.0.tgz#1a78a0b5965c40a5416d007ad6f50ad27c417df9"
  integrity sha1-GnigtZZcQKVBbQB61vUK0nxBffk=

ip-regex@^4.0.0:
  version "4.3.0"
  resolved "https://registry.nlark.com/ip-regex/download/ip-regex-4.3.0.tgz#687275ab0f57fa76978ff8f4dddc8a23d5990db5"
  integrity sha1-aHJ1qw9X+naXj/j03dyKI9WZDbU=

ip@^1.1.0:
  version "1.1.5"
  resolved "https://registry.nlark.com/ip/download/ip-1.1.5.tgz#bdded70114290828c0a039e72ef25f5aaec4354a"
  integrity sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=

ipaddr.js@1.9.1, ipaddr.js@^1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/ipaddr.js/download/ipaddr.js-1.9.1.tgz#bff38543eeb8984825079ff3a2a8e6cbd46781b3"
  integrity sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=

ipaddr.js@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/ipaddr.js/download/ipaddr.js-2.0.1.tgz#eca256a7a877e917aeb368b0a7497ddf42ef81c0"
  integrity sha1-7KJWp6h36Reus2iwp0l930LvgcA=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arguments@^1.0.4:
  version "1.1.1"
  resolved "https://registry.nlark.com/is-arguments/download/is-arguments-1.1.1.tgz?cache=0&sync_timestamp=1628201919104&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-arguments%2Fdownload%2Fis-arguments-1.1.1.tgz#15b3f88fda01f2a97fec84ca761a560f123efa9b"
  integrity sha1-FbP4j9oB8ql/7ITKdhpWDxI++ps=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://registry.nlark.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "https://registry.nlark.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-core-module@^2.2.0:
  version "2.8.0"
  resolved "https://registry.npmmirror.com/is-core-module/download/is-core-module-2.8.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-core-module%2Fdownload%2Fis-core-module-2.8.0.tgz#0321336c3d0925e497fd97f5d95cb114a5ccd548"
  integrity sha1-AyEzbD0JJeSX/Zf12VyxFKXM1Ug=
  dependencies:
    has "^1.0.3"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.5"
  resolved "https://registry.nlark.com/is-date-object/download/is-date-object-1.0.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-date-object%2Fdownload%2Fis-date-object-1.0.5.tgz#0841d5536e724c25597bf6ea62e1bd38298df31f"
  integrity sha1-CEHVU25yTCVZe/bqYuG9OCmN8x8=
  dependencies:
    has-tostringtag "^1.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/is-docker/download/is-docker-2.2.1.tgz?cache=0&sync_timestamp=1630451108035&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-docker%2Fdownload%2Fis-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^3.1.0:
  version "3.1.0"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-ip@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/is-ip/download/is-ip-3.1.0.tgz#2ae5ddfafaf05cb8008a62093cf29734f657c5d8"
  integrity sha1-KuXd+vrwXLgAimIJPPKXNPZXxdg=
  dependencies:
    ip-regex "^4.0.0"

is-number@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://registry.npm.taobao.org/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-cwd@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz?cache=0&sync_timestamp=1628686555851&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-cwd%2Fdownload%2Fis-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-inside@^3.0.2:
  version "3.0.3"
  resolved "https://registry.nlark.com/is-path-inside/download/is-path-inside-3.0.3.tgz?cache=0&sync_timestamp=1620046845369&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-path-inside%2Fdownload%2Fis-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-plain-obj@^1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz?cache=0&sync_timestamp=1618600489644&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-plain-obj%2Fdownload%2Fis-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-obj@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-3.0.0.tgz?cache=0&sync_timestamp=1618600489644&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-plain-obj%2Fdownload%2Fis-plain-obj-3.0.0.tgz#af6f2ea14ac5a646183a5bbdb5baabbc156ad9d7"
  integrity sha1-r28uoUrFpkYYOlu9tbqrvBVq2dc=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "https://registry.nlark.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-redirect@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-redirect/download/is-redirect-1.0.0.tgz#1d03dded53bd8db0f30c26e4f95d36fc7c87dc24"
  integrity sha1-HQPd7VO9jbDzDCbk+V02/HyH3CQ=

is-regex@^1.0.4:
  version "1.1.4"
  resolved "https://registry.nlark.com/is-regex/download/is-regex-1.1.4.tgz?cache=0&sync_timestamp=1628221905423&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-regex%2Fdownload%2Fis-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-retry-allowed@^1.0.0:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/is-retry-allowed/download/is-retry-allowed-1.2.0.tgz#d778488bd0a4666a3be8a1482b9f2baafedea8b4"
  integrity sha1-13hIi9CkZmo76KFIK58rqv7eqLQ=

is-scoped@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/is-scoped/download/is-scoped-1.0.0.tgz?cache=0&sync_timestamp=1618685443209&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fis-scoped%2Fdownload%2Fis-scoped-1.0.0.tgz#449ca98299e713038256289ecb2b540dc437cb30"
  integrity sha1-RJypgpnnEwOCViieyytUDcQ3yzA=
  dependencies:
    scoped-regex "^1.0.0"

is-stream@^1.0.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/is-stream/download/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-utf8@^0.2.0, is-utf8@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"
  integrity sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/is-windows/download/is-windows-1.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fis-windows%2Fdownload%2Fis-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/is-wsl/download/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isbinaryfile@^4.0.0:
  version "4.0.8"
  resolved "https://registry.nlark.com/isbinaryfile/download/isbinaryfile-4.0.8.tgz#5d34b94865bd4946633ecc78a026fc76c5b11fcf"
  integrity sha1-XTS5SGW9SUZjPsx4oCb8dsWxH88=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

istextorbinary@^2.5.1:
  version "2.6.0"
  resolved "https://registry.nlark.com/istextorbinary/download/istextorbinary-2.6.0.tgz?cache=0&sync_timestamp=1627748914140&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fistextorbinary%2Fdownload%2Fistextorbinary-2.6.0.tgz#60776315fb0fa3999add276c02c69557b9ca28ab"
  integrity sha1-YHdjFfsPo5ma3SdsAsaVV7nKKKs=
  dependencies:
    binaryextensions "^2.1.2"
    editions "^2.2.0"
    textextensions "^2.5.0"

jake@^10.6.1:
  version "10.8.2"
  resolved "https://registry.nlark.com/jake/download/jake-10.8.2.tgz#ebc9de8558160a66d82d0eadc6a2e58fbc500a7b"
  integrity sha1-68nehVgWCmbYLQ6txqLlj7xQCns=
  dependencies:
    async "0.9.x"
    chalk "^2.4.2"
    filelist "^1.0.1"
    minimatch "^3.0.4"

jest-worker@^27.0.6, jest-worker@^27.3.1:
  version "27.3.1"
  resolved "https://registry.npmmirror.com/jest-worker/download/jest-worker-27.3.1.tgz?cache=0&sync_timestamp=1634626737887&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjest-worker%2Fdownload%2Fjest-worker-27.3.1.tgz#0def7feae5b8042be38479799aeb7b5facac24b2"
  integrity sha1-De9/6uW4BCvjhHl5mut7X6ysJLI=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/js-tokens/download/js-tokens-4.0.0.tgz?cache=0&sync_timestamp=1619345098261&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fjs-tokens%2Fdownload%2Fjs-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://registry.npm.taobao.org/js-yaml/download/js-yaml-4.1.0.tgz?cache=0&sync_timestamp=1618435004368&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjs-yaml%2Fdownload%2Fjs-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://registry.nlark.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://registry.nlark.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

jsonc-parser@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/jsonc-parser/download/jsonc-parser-3.0.0.tgz?cache=0&sync_timestamp=1605281904422&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fjsonc-parser%2Fdownload%2Fjsonc-parser-3.0.0.tgz#abdd785701c7e7eaca8a9ec8cf070ca51a745a22"
  integrity sha1-q914VwHH5+rKip7IzwcMpRp0WiI=

jsonparse@^1.2.0:
  version "1.3.1"
  resolved "https://registry.npm.taobao.org/jsonparse/download/jsonparse-1.3.1.tgz#3f4dae4a91fac315f71062f8521cc239f1366280"
  integrity sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.3"
  resolved "https://registry.nlark.com/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

klona@^2.0.4:
  version "2.0.5"
  resolved "https://registry.npmmirror.com/klona/download/klona-2.0.5.tgz?cache=0&sync_timestamp=1635385383825&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fklona%2Fdownload%2Fklona-2.0.5.tgz#d166574d90076395d9963aa7a928fabb8d76afbc"
  integrity sha1-0WZXTZAHY5XZljqnqSj6u412r7w=

lazy-cache@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/lazy-cache/download/lazy-cache-2.0.2.tgz#b9190a4f913354694840859f8a8f7084d8822264"
  integrity sha1-uRkKT5EzVGlIQIWfio9whNiCImQ=
  dependencies:
    set-getter "^0.1.0"

levn@^0.4.1:
  version "0.4.1"
  resolved "https://registry.npm.taobao.org/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "https://registry.nlark.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

loader-runner@^4.2.0:
  version "4.2.0"
  resolved "https://registry.nlark.com/loader-runner/download/loader-runner-4.2.0.tgz#d7022380d66d14c5fb1d496b89864ebcfd478384"
  integrity sha1-1wIjgNZtFMX7HUlriYZOvP1Hg4Q=

locate-path@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-3.0.0.tgz?cache=0&sync_timestamp=1629895724478&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flocate-path%2Fdownload%2Flocate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/locate-path/download/locate-path-5.0.0.tgz?cache=0&sync_timestamp=1629895724478&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flocate-path%2Fdownload%2Flocate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://registry.npm.taobao.org/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.20, lodash@^4.17.21:
  version "4.17.21"
  resolved "https://registry.nlark.com/lodash/download/lodash-4.17.21.tgz?cache=0&sync_timestamp=1624543041613&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flodash%2Fdownload%2Flodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/log-symbols/download/log-symbols-2.2.0.tgz?cache=0&sync_timestamp=1618847128438&other_urls=https%3A%2F%2Fregistry.nlark.com%2Flog-symbols%2Fdownload%2Flog-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

loglevel@^1.7.1:
  version "1.8.0"
  resolved "https://registry.npmmirror.com/loglevel/download/loglevel-1.8.0.tgz#e7ec73a57e1e7b419cb6c6ac06bf050b67356114"
  integrity sha512-G6A/nJLRgWOuuwdNuA6koovfEV1YpqqAG4pRUlFaz3jj2QNZ8M4vBqnVA+HBTmU/AMNUtlOsMmSpF6NyOjztbA==

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/lower-case/download/lower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lowercase-keys@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz?cache=0&sync_timestamp=1634551715073&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flowercase-keys%2Fdownload%2Flowercase-keys-1.0.1.tgz#6f9e30b47084d971a7c820ff15a6c5167b74c26f"
  integrity sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/lru-cache/download/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

lunr@^2.3.9:
  version "2.3.9"
  resolved "https://registry.npm.taobao.org/lunr/download/lunr-2.3.9.tgz#18b123142832337dd6e964df1a5a7707b25d35e1"
  integrity sha1-GLEjFCgyM33W6WTfGlp3B7JdNeE=

make-dir@^3.0.0:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/make-dir/download/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "https://registry.nlark.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

marked@^3.0.8:
  version "3.0.8"
  resolved "https://registry.npmmirror.com/marked/download/marked-3.0.8.tgz?cache=0&sync_timestamp=1636778105511&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmarked%2Fdownload%2Fmarked-3.0.8.tgz#2785f0dc79cbdc6034be4bb4f0f0a396bd3f8aeb"
  integrity sha1-J4Xw3HnL3GA0vku08PCjlr0/ius=

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

mem-fs-editor@^6.0.0:
  version "6.0.0"
  resolved "https://registry.npmmirror.com/mem-fs-editor/download/mem-fs-editor-6.0.0.tgz#d63607cf0a52fe6963fc376c6a7aa52db3edabab"
  integrity sha1-1jYHzwpS/mlj/DdsanqlLbPtq6s=
  dependencies:
    commondir "^1.0.1"
    deep-extend "^0.6.0"
    ejs "^2.6.1"
    glob "^7.1.4"
    globby "^9.2.0"
    isbinaryfile "^4.0.0"
    mkdirp "^0.5.0"
    multimatch "^4.0.0"
    rimraf "^2.6.3"
    through2 "^3.0.1"
    vinyl "^2.2.0"

mem-fs-editor@^7.0.1:
  version "7.1.0"
  resolved "https://registry.npmmirror.com/mem-fs-editor/download/mem-fs-editor-7.1.0.tgz#2a16f143228df87bf918874556723a7ee73bfe88"
  integrity sha1-KhbxQyKN+Hv5GIdFVnI6fuc7/og=
  dependencies:
    commondir "^1.0.1"
    deep-extend "^0.6.0"
    ejs "^3.1.5"
    glob "^7.1.4"
    globby "^9.2.0"
    isbinaryfile "^4.0.0"
    mkdirp "^1.0.0"
    multimatch "^4.0.0"
    rimraf "^3.0.0"
    through2 "^3.0.2"
    vinyl "^2.2.1"

mem-fs@^1.1.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/mem-fs/download/mem-fs-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmem-fs%2Fdownload%2Fmem-fs-1.2.0.tgz#5f29b2d02a5875cd14cd836c388385892d556cde"
  integrity sha1-Xymy0CpYdc0UzYNsOIOFiS1VbN4=
  dependencies:
    through2 "^3.0.0"
    vinyl "^2.0.1"
    vinyl-file "^3.0.0"

memfs@^3.2.2:
  version "3.3.0"
  resolved "https://registry.nlark.com/memfs/download/memfs-3.3.0.tgz?cache=0&sync_timestamp=1632066657842&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fmemfs%2Fdownload%2Fmemfs-3.3.0.tgz#4da2d1fc40a04b170a56622c7164c6be2c4cbef2"
  integrity sha1-TaLR/ECgSxcKVmIscWTGvixMvvI=
  dependencies:
    fs-monkey "1.0.3"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3, merge2@^1.3.0:
  version "1.4.1"
  resolved "https://registry.npm.taobao.org/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@^3.1.10:
  version "3.1.10"
  resolved "https://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz?cache=0&sync_timestamp=1618054740956&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

micromatch@^4.0.0, micromatch@^4.0.2, micromatch@^4.0.4:
  version "4.0.4"
  resolved "https://registry.npm.taobao.org/micromatch/download/micromatch-4.0.4.tgz?cache=0&sync_timestamp=1618054740956&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmicromatch%2Fdownload%2Fmicromatch-4.0.4.tgz#896d519dfe9db25fce94ceb7a500919bf881ebf9"
  integrity sha1-iW1Rnf6dsl/OlM63pQCRm/iB6/k=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.2.3"

mime-db@1.51.0, "mime-db@>= 1.43.0 < 2":
  version "1.51.0"
  resolved "https://registry.npmmirror.com/mime-db/download/mime-db-1.51.0.tgz?cache=0&sync_timestamp=1636425951030&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-db%2Fdownload%2Fmime-db-1.51.0.tgz#d9ff62451859b18342d960850dc3cfb77e63fb0c"
  integrity sha512-5y8A56jg7XVQx2mbv1lu49NR4dokRnhZYTtL+KGfaa27uq4pSTXkwQkFJl4pkRMyNFz/EtYDSkiiEHx3F7UN6g==

mime-types@^2.1.27, mime-types@^2.1.31, mime-types@~2.1.17, mime-types@~2.1.24:
  version "2.1.34"
  resolved "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.34.tgz?cache=0&sync_timestamp=1636432355847&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.34.tgz#5a712f9ec1503511a945803640fafe09d3793c24"
  integrity sha512-6cP692WwGIs9XXdOO4++N+7qjqv0rqxxVvJ3VHPh/Sc9mVZcQP+ZGhkKiTvWMQRr2tbHkJP/Yn7Y0npb3ZBs4A==
  dependencies:
    mime-db "1.51.0"

mime@1.6.0:
  version "1.6.0"
  resolved "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

minimalistic-assert@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

"minimatch@2 || 3", minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://registry.nlark.com/minimatch/download/minimatch-3.0.4.tgz?cache=0&sync_timestamp=1618846754554&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fminimatch%2Fdownload%2Fminimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/minimist/download/minimist-0.1.0.tgz#99df657a52574c21c9057497df742790b2b4c0de"
  integrity sha1-md9lelJXTCHJBXSX33QnkLK0wN4=

minimist@^1.2.5:
  version "1.2.5"
  resolved "https://registry.nlark.com/minimist/download/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "https://registry.nlark.com/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.0, mkdirp@^0.5.5:
  version "0.5.5"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

mkdirp@^1.0.0:
  version "1.0.4"
  resolved "https://registry.npmmirror.com/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

moment@^2.15.1, moment@^2.24.0:
  version "2.29.1"
  resolved "https://registry.nlark.com/moment/download/moment-2.29.1.tgz#b2be769fa31940be9eeea6469c075e35006fa3d3"
  integrity sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M=

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@2.1.2:
  version "2.1.2"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

ms@^2.1.1:
  version "2.1.3"
  resolved "https://registry.npmmirror.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

multicast-dns-service-types@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz#899f11d9686e5e05cb91b35d5f0e63b773cfc901"
  integrity sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=

multicast-dns@^6.0.1:
  version "6.2.3"
  resolved "https://registry.npmmirror.com/multicast-dns/download/multicast-dns-6.2.3.tgz#a0ec7bd9055c4282f790c3c82f4e28db3b31b229"
  integrity sha1-oOx72QVcQoL3kMPIL04o2zsxsik=
  dependencies:
    dns-packet "^1.3.1"
    thunky "^1.0.2"

multimatch@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/multimatch/download/multimatch-4.0.0.tgz?cache=0&sync_timestamp=1634555800022&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmultimatch%2Fdownload%2Fmultimatch-4.0.0.tgz#8c3c0f6e3e8449ada0af3dd29efb491a375191b3"
  integrity sha1-jDwPbj6ESa2grz3SnvtJGjdRkbM=
  dependencies:
    "@types/minimatch" "^3.0.3"
    array-differ "^3.0.0"
    array-union "^2.1.0"
    arrify "^2.0.1"
    minimatch "^3.0.4"

mute-stream@0.0.8:
  version "0.0.8"
  resolved "https://registry.nlark.com/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

nanoid@^3.1.30:
  version "3.1.30"
  resolved "https://registry.npmmirror.com/nanoid/download/nanoid-3.1.30.tgz?cache=0&sync_timestamp=1634166192601&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnanoid%2Fdownload%2Fnanoid-3.1.30.tgz#63f93cc548d2a113dc5dfbc63bfa09e2b9b64362"
  integrity sha1-Y/k8xUjSoRPcXfvGO/oJ4rm2Q2I=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "https://registry.nlark.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "https://registry.npm.taobao.org/negotiator/download/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.6.2:
  version "2.6.2"
  resolved "https://registry.nlark.com/neo-async/download/neo-async-2.6.2.tgz#b4aafb93e3aeb2d8174ca53cf163ab7d7308305f"
  integrity sha1-tKr7k+OustgXTKU88WOrfXMIMF8=

nested-error-stacks@^2.0.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/nested-error-stacks/download/nested-error-stacks-2.1.0.tgz#0fbdcf3e13fe4994781280524f8b96b0cdff9c61"
  integrity sha1-D73PPhP+SZR4EoBST4uWsM3/nGE=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "https://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz?cache=0&sync_timestamp=1614510016909&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fnice-try%2Fdownload%2Fnice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.npm.taobao.org/no-case/download/no-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-fetch@^2.6.0:
  version "2.6.6"
  resolved "https://registry.npmmirror.com/node-fetch/download/node-fetch-2.6.6.tgz?cache=0&sync_timestamp=1636395469986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-fetch%2Fdownload%2Fnode-fetch-2.6.6.tgz#1751a7c01834e8e1697758732e9efb6eeadfaf89"
  integrity sha1-F1GnwBg06OFpd1hzLp77burfr4k=
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^0.10.0:
  version "0.10.0"
  resolved "https://registry.nlark.com/node-forge/download/node-forge-0.10.0.tgz#32dea2afb3e9926f02ee5ce8794902691a676bf3"
  integrity sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=

node-releases@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/node-releases/download/node-releases-2.0.1.tgz?cache=0&sync_timestamp=1634806960337&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-releases%2Fdownload%2Fnode-releases-2.0.1.tgz#3d1d395f204f1f2f29a54358b9fb678765ad2fc5"
  integrity sha1-PR05XyBPHy8ppUNYuftnh2WtL8U=

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://registry.nlark.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1629301872905&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

npm-api@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/npm-api/download/npm-api-1.0.1.tgz#3def9b51afedca57db14ca0c970d92442d21c9c5"
  integrity sha1-Pe+bUa/tylfbFMoMlw2SRC0hycU=
  dependencies:
    JSONStream "^1.3.5"
    clone-deep "^4.0.1"
    download-stats "^0.3.4"
    moment "^2.24.0"
    node-fetch "^2.6.0"
    paged-request "^2.0.1"

npm-run-path@^4.0.0, npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

nth-check@^2.0.0:
  version "2.0.1"
  resolved "https://registry.nlark.com/nth-check/download/nth-check-2.0.1.tgz#2efe162f5c3da06a28959fbd3db75dbeea9f0fc2"
  integrity sha1-Lv4WL1w9oGoolZ+9PbddvuqfD8I=
  dependencies:
    boolbase "^1.0.0"

object-copy@^0.1.0:
  version "0.1.0"
  resolved "https://registry.nlark.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-is@^1.0.1:
  version "1.1.5"
  resolved "https://registry.npm.taobao.org/object-is/download/object-is-1.1.5.tgz?cache=0&sync_timestamp=1613857698573&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fobject-is%2Fdownload%2Fobject-is-1.1.5.tgz#b9deeaa5fc7f1846a0faecdceec138e5778f53ac"
  integrity sha1-ud7qpfx/GEag+uzc7sE45XePU6w=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "https://registry.nlark.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

obuf@^1.0.0, obuf@^1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/obuf/download/obuf-1.1.2.tgz#09bea3343d41859ebd446292d11c9d4db619084e"
  integrity sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/on-headers/download/on-headers-1.0.2.tgz#772b0ae6aaa525c399e489adfad90c403eb3c28f"
  integrity sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "https://registry.nlark.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://registry.npm.taobao.org/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

onigasm@^2.2.5:
  version "2.2.5"
  resolved "https://registry.npm.taobao.org/onigasm/download/onigasm-2.2.5.tgz#cc4d2a79a0fa0b64caec1f4c7ea367585a676892"
  integrity sha1-zE0qeaD6C2TK7B9MfqNnWFpnaJI=
  dependencies:
    lru-cache "^5.1.1"

open@^8.0.9:
  version "8.4.0"
  resolved "https://registry.npmmirror.com/open/download/open-8.4.0.tgz#345321ae18f8138f82565a910fdc6b39e8c244f8"
  integrity sha1-NFMhrhj4E4+CVlqRD9xrOejCRPg=
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

optionator@^0.9.1:
  version "0.9.1"
  resolved "https://registry.nlark.com/optionator/download/optionator-0.9.1.tgz#4f236a6373dae0566a6d43e1326674f50c291499"
  integrity sha1-TyNqY3Pa4FZqbUPhMmZ09QwpFJk=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.3"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-event@^4.1.0, p-event@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/p-event/download/p-event-4.2.0.tgz?cache=0&sync_timestamp=1635954052656&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-event%2Fdownload%2Fp-event-4.2.0.tgz#af4b049c8acd91ae81083ebd1e6f5cae2044c1b5"
  integrity sha1-r0sEnIrNka6BCD69Hm9criBEwbU=
  dependencies:
    p-timeout "^3.1.0"

p-finally@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://registry.nlark.com/p-limit/download/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/p-map/download/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-retry@^4.5.0:
  version "4.6.1"
  resolved "https://registry.npmmirror.com/p-retry/download/p-retry-4.6.1.tgz#8fcddd5cdf7a67a0911a9cf2ef0e5df7f602316c"
  integrity sha1-j83dXN96Z6CRGpzy7w5d9/YCMWw=
  dependencies:
    "@types/retry" "^0.12.0"
    retry "^0.13.1"

p-timeout@^3.1.0:
  version "3.2.0"
  resolved "https://registry.npmmirror.com/p-timeout/download/p-timeout-3.2.0.tgz#c7e17abc971d2a7962ef83626b35d635acf23dfe"
  integrity sha1-x+F6vJcdKnli74NiazXWNazyPf4=
  dependencies:
    p-finally "^1.0.0"

p-try@^2.0.0, p-try@^2.1.0:
  version "2.2.0"
  resolved "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz?cache=0&sync_timestamp=1633364462890&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-try%2Fdownload%2Fp-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

paged-request@^2.0.1:
  version "2.0.2"
  resolved "https://registry.npm.taobao.org/paged-request/download/paged-request-2.0.2.tgz#4d621a08b8d6bee4440a0a92112354eeece5b5b0"
  integrity sha1-TWIaCLjWvuRECgqSESNU7uzltbA=
  dependencies:
    axios "^0.21.1"

param-case@^3.0.4:
  version "3.0.4"
  resolved "https://registry.nlark.com/param-case/download/param-case-3.0.4.tgz#7d17fe4aa12bde34d4a77d91acfb6219caad01c5"
  integrity sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npmmirror.com/parent-module/download/parent-module-1.0.1.tgz?cache=0&sync_timestamp=1633337513286&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparent-module%2Fdownload%2Fparent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.0.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/parse-json/download/parse-json-5.2.0.tgz?cache=0&sync_timestamp=1636944602935&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fparse-json%2Fdownload%2Fparse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parseurl@~1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "https://registry.nlark.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascal-case@^3.1.2:
  version "3.1.2"
  resolved "https://registry.npm.taobao.org/pascal-case/download/pascal-case-3.1.2.tgz?cache=0&sync_timestamp=1606867325163&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpascal-case%2Fdownload%2Fpascal-case-3.1.2.tgz#b48e0ef2b98e205e7c1dae747d0b1508237660eb"
  integrity sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "https://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "https://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz?cache=0&sync_timestamp=1617971695678&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/path-key/download/path-key-3.1.1.tgz?cache=0&sync_timestamp=1617971695678&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpath-key%2Fdownload%2Fpath-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.7"
  resolved "https://registry.nlark.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-type@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/path-type/download/path-type-3.0.0.tgz#cef31dc8e0a1a3bb0d105c0cd97cf3bf47f4e36f"
  integrity sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=
  dependencies:
    pify "^3.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

picocolors@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/picocolors/download/picocolors-1.0.0.tgz?cache=0&sync_timestamp=1634093442271&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicocolors%2Fdownload%2Fpicocolors-1.0.0.tgz#cb5bdc74ff3f51892236eaf79d68bc44564ab81c"
  integrity sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3:
  version "2.3.0"
  resolved "https://registry.nlark.com/picomatch/download/picomatch-2.3.0.tgz#f1f061de8f6a4bf022892e2d128234fb98302972"
  integrity sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=

pify@^2.3.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://registry.npm.taobao.org/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://registry.npmmirror.com/pkg-dir/download/pkg-dir-4.2.0.tgz?cache=0&sync_timestamp=1633498133295&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpkg-dir%2Fdownload%2Fpkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

portfinder@^1.0.28:
  version "1.0.28"
  resolved "https://registry.nlark.com/portfinder/download/portfinder-1.0.28.tgz#67c4622852bd5374dd1dd900f779f53462fac778"
  integrity sha1-Z8RiKFK9U3TdHdkA93n1NGL6x3g=
  dependencies:
    async "^2.6.2"
    debug "^3.1.1"
    mkdirp "^0.5.5"

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-modules-extract-imports@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-3.0.0.tgz#cda1f047c0ae80c97dbe28c3e76a43b88025741d"
  integrity sha1-zaHwR8CugMl9vijD52pDuIAldB0=

postcss-modules-local-by-default@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-4.0.0.tgz#ebbb54fae1598eecfdf691a02b3ff3b390a5a51c"
  integrity sha1-67tU+uFZjuz99pGgKz/zs5ClpRw=
  dependencies:
    icss-utils "^5.0.0"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.1.0"

postcss-modules-scope@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/postcss-modules-scope/download/postcss-modules-scope-3.0.0.tgz#9ef3151456d3bbfa120ca44898dfca6f2fa01f06"
  integrity sha1-nvMVFFbTu/oSDKRImN/Kby+gHwY=
  dependencies:
    postcss-selector-parser "^6.0.4"

postcss-modules-values@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/postcss-modules-values/download/postcss-modules-values-4.0.0.tgz#d7c5e7e68c3bb3c9b27cbf48ca0bb3ffb4602c9c"
  integrity sha1-18Xn5ow7s8myfL9Iyguz/7RgLJw=
  dependencies:
    icss-utils "^5.0.0"

postcss-selector-parser@^6.0.2, postcss-selector-parser@^6.0.4:
  version "6.0.6"
  resolved "https://registry.nlark.com/postcss-selector-parser/download/postcss-selector-parser-6.0.6.tgz?cache=0&sync_timestamp=1620752939806&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-selector-parser%2Fdownload%2Fpostcss-selector-parser-6.0.6.tgz#2c5bba8174ac2f6981ab631a42ab0ee54af332ea"
  integrity sha1-LFu6gXSsL2mBq2MaQqsO5UrzMuo=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.1.0:
  version "4.1.0"
  resolved "https://registry.nlark.com/postcss-value-parser/download/postcss-value-parser-4.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpostcss-value-parser%2Fdownload%2Fpostcss-value-parser-4.1.0.tgz#443f6a20ced6481a2bda4fa8532a6e55d789a2cb"
  integrity sha1-RD9qIM7WSBor2k+oUypuVdeJoss=

postcss@^8.2.15:
  version "8.3.11"
  resolved "https://registry.npmmirror.com/postcss/download/postcss-8.3.11.tgz#c3beca7ea811cd5e1c4a3ec6d2e7599ef1f8f858"
  integrity sha1-w77KfqgRzV4cSj7G0udZnvH4+Fg=
  dependencies:
    nanoid "^3.1.30"
    picocolors "^1.0.0"
    source-map-js "^0.6.2"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://registry.nlark.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prepend-http@^1.0.1:
  version "1.0.4"
  resolved "https://registry.nlark.com/prepend-http/download/prepend-http-1.0.4.tgz?cache=0&sync_timestamp=1628547439455&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprepend-http%2Fdownload%2Fprepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^2.4.1:
  version "2.4.1"
  resolved "https://registry.nlark.com/prettier/download/prettier-2.4.1.tgz?cache=0&sync_timestamp=1631777138030&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fprettier%2Fdownload%2Fprettier-2.4.1.tgz#671e11c89c14a4cfc876ce564106c4a6726c9f5c"
  integrity sha1-Zx4RyJwUpM/Ids5WQQbEpnJsn1w=

pretty-bytes@^5.2.0:
  version "5.6.0"
  resolved "https://registry.npm.taobao.org/pretty-bytes/download/pretty-bytes-5.6.0.tgz#356256f643804773c82f64723fe78c92c62beaeb"
  integrity sha1-NWJW9kOAR3PIL2RyP+eMksYr6us=

pretty-error@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/pretty-error/download/pretty-error-4.0.0.tgz#90a703f46dd7234adb46d0f84823e9d1cb8f10d6"
  integrity sha1-kKcD9G3XI0rbRtD4SCPp0cuPENY=
  dependencies:
    lodash "^4.17.20"
    renderkid "^3.0.0"

process-nextick-args@^2.0.0, process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

progress@^2.0.0:
  version "2.0.3"
  resolved "https://registry.nlark.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

proxy-addr@~2.0.5:
  version "2.0.7"
  resolved "https://registry.nlark.com/proxy-addr/download/proxy-addr-2.0.7.tgz#f19fe69ceab311eeb94b42e70e8c2070f9ba1025"
  integrity sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@1.3.2:
  version "1.3.2"
  resolved "https://registry.nlark.com/punycode/download/punycode-1.3.2.tgz?cache=0&sync_timestamp=1622604519710&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpunycode%2Fdownload%2Fpunycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^2.1.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/punycode/download/punycode-2.1.1.tgz?cache=0&sync_timestamp=1622604519710&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fpunycode%2Fdownload%2Fpunycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

qs@6.7.0:
  version "6.7.0"
  resolved "https://registry.nlark.com/qs/download/qs-6.7.0.tgz#41dc1a015e3d581f1621776be31afb2876a9b1bc"
  integrity sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://registry.npmmirror.com/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://registry.nlark.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "https://registry.npm.taobao.org/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"
  resolved "https://registry.npm.taobao.org/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.0:
  version "2.4.0"
  resolved "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.0.tgz?cache=0&sync_timestamp=1637116848060&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fraw-body%2Fdownload%2Fraw-body-2.4.0.tgz#a1ce6fb9c9bc356ca52e89256ab59059e13d0332"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

read-chunk@^3.2.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/read-chunk/download/read-chunk-3.2.0.tgz#2984afe78ca9bfbbdb74b19387bf9e86289c16ca"
  integrity sha1-KYSv54ypv7vbdLGTh7+ehiicFso=
  dependencies:
    pify "^4.0.1"
    with-open-file "^0.1.6"

read-pkg-up@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-5.0.0.tgz#b6a6741cb144ed3610554f40162aa07a6db621b8"
  integrity sha1-tqZ0HLFE7TYQVU9AFiqgem22Ibg=
  dependencies:
    find-up "^3.0.0"
    read-pkg "^5.0.0"

read-pkg@^5.0.0:
  version "5.2.0"
  resolved "https://registry.nlark.com/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

"readable-stream@2 || 3", readable-stream@^3.0.6:
  version "3.6.0"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-3.6.0.tgz#337bbda3adc0706bd3e024426a286d4b4b2c9198"
  integrity sha1-M3u9o63AcGvT4CRCaihtS0sskZg=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.3.5:
  version "2.3.7"
  resolved "https://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.7.tgz#1eca1cf711aef814c04f62252a36a62f6cb23b57"
  integrity sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://registry.nlark.com/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://registry.nlark.com/rechoir/download/rechoir-0.6.2.tgz?cache=0&sync_timestamp=1627101702123&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frechoir%2Fdownload%2Frechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

rechoir@^0.7.0:
  version "0.7.1"
  resolved "https://registry.nlark.com/rechoir/download/rechoir-0.7.1.tgz?cache=0&sync_timestamp=1627101702123&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frechoir%2Fdownload%2Frechoir-0.7.1.tgz#9478a96a1ca135b5e88fc027f03ee92d6c645686"
  integrity sha1-lHipahyhNbXoj8An8D7pLWxkVoY=
  dependencies:
    resolve "^1.9.0"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "https://registry.nlark.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp.prototype.flags@^1.2.0:
  version "1.3.1"
  resolved "https://registry.nlark.com/regexp.prototype.flags/download/regexp.prototype.flags-1.3.1.tgz#7ef352ae8d159e758c0eadca6f8fcb4eef07be26"
  integrity sha1-fvNSro0VnnWMDq3Kb4/LTu8HviY=
  dependencies:
    call-bind "^1.0.2"
    define-properties "^1.1.3"

regexpp@^3.2.0:
  version "3.2.0"
  resolved "https://registry.nlark.com/regexpp/download/regexpp-3.2.0.tgz?cache=0&sync_timestamp=1623669331185&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fregexpp%2Fdownload%2Fregexpp-3.2.0.tgz#0425a2768d8f23bad70ca4b90461fa2f1213e1b2"
  integrity sha1-BCWido2PI7rXDKS5BGH6LxIT4bI=

relateurl@^0.2.7:
  version "0.2.7"
  resolved "https://registry.nlark.com/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"
  integrity sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "https://registry.nlark.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

renderkid@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npmmirror.com/renderkid/download/renderkid-3.0.0.tgz#5fd823e4d6951d37358ecc9a58b1f06836b6268a"
  integrity sha1-X9gj5NaVHTc1jsyaWLHwaDa2Joo=
  dependencies:
    css-select "^4.1.3"
    dom-converter "^0.2.0"
    htmlparser2 "^6.1.0"
    lodash "^4.17.21"
    strip-ansi "^6.0.1"

repeat-element@^1.1.2:
  version "1.1.4"
  resolved "https://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.4.tgz#be681520847ab58c7568ac75fbfad28ed42d39e9"
  integrity sha1-vmgVIIR6tYx1aKx1+/rSjtQtOek=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "https://registry.nlark.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

replace-ext@^1.0.0:
  version "1.0.1"
  resolved "https://registry.npm.taobao.org/replace-ext/download/replace-ext-1.0.1.tgz#2d6d996d04a15855d967443631dd5f77825b016a"
  integrity sha1-LW2ZbQShWFXZZ0Q2Md1fd4JbAWo=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://registry.nlark.com/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://registry.nlark.com/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "https://registry.npmmirror.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.1.6, resolve@^1.10.0, resolve@^1.9.0:
  version "1.20.0"
  resolved "https://registry.npm.taobao.org/resolve/download/resolve-1.20.0.tgz#629a013fb3f70755d6f0b7935cc1c2c5378b1975"
  integrity sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=
  dependencies:
    is-core-module "^2.2.0"
    path-parse "^1.0.6"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://registry.nlark.com/restore-cursor/download/restore-cursor-3.1.0.tgz?cache=0&sync_timestamp=1629746923086&other_urls=https%3A%2F%2Fregistry.nlark.com%2Frestore-cursor%2Fdownload%2Frestore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "https://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

retry@^0.13.1:
  version "0.13.1"
  resolved "https://registry.nlark.com/retry/download/retry-0.13.1.tgz#185b1587acf67919d63b357349e03537b2484658"
  integrity sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=

reusify@^1.0.4:
  version "1.0.4"
  resolved "https://registry.nlark.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rimraf@^2.6.3:
  version "2.7.1"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

run-async@^2.0.0, run-async@^2.4.0:
  version "2.4.1"
  resolved "https://registry.nlark.com/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://registry.npm.taobao.org/run-parallel/download/run-parallel-1.2.0.tgz?cache=0&sync_timestamp=1612925943325&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frun-parallel%2Fdownload%2Frun-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^6.6.0:
  version "6.6.7"
  resolved "https://registry.npmmirror.com/rxjs/download/rxjs-6.6.7.tgz?cache=0&sync_timestamp=1633554235070&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Frxjs%2Fdownload%2Frxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@>=5.1.0, safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "https://registry.nlark.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "https://registry.nlark.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://registry.nlark.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-loader@^12.1.0:
  version "12.3.0"
  resolved "https://registry.npmmirror.com/sass-loader/download/sass-loader-12.3.0.tgz#93278981c189c36a58cbfc37d4b9cef0cdc02871"
  integrity sha1-kyeJgcGJw2pYy/w31LnO8M3AKHE=
  dependencies:
    klona "^2.0.4"
    neo-async "^2.6.2"

sass@^1.40.0:
  version "1.43.4"
  resolved "https://registry.npmmirror.com/sass/download/sass-1.43.4.tgz?cache=0&sync_timestamp=1635300346226&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsass%2Fdownload%2Fsass-1.43.4.tgz#68c7d6a1b004bef49af0d9caf750e9b252105d1f"
  integrity sha1-aMfWobAEvvSa8NnK91DpslIQXR8=
  dependencies:
    chokidar ">=3.0.0 <4.0.0"

schema-utils@^3.1.0, schema-utils@^3.1.1:
  version "3.1.1"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-3.1.1.tgz?cache=0&sync_timestamp=1637075997986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-3.1.1.tgz#bc74c4b6b6995c1d88f76a8b77bea7219e0c8281"
  integrity sha1-vHTEtraZXB2I92qLd76nIZ4MgoE=
  dependencies:
    "@types/json-schema" "^7.0.8"
    ajv "^6.12.5"
    ajv-keywords "^3.5.2"

schema-utils@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/schema-utils/download/schema-utils-4.0.0.tgz?cache=0&sync_timestamp=1637075997986&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fschema-utils%2Fdownload%2Fschema-utils-4.0.0.tgz#60331e9e3ae78ec5d16353c467c34b3a0a1d3df7"
  integrity sha512-1edyXKgh6XnJsJSQ8mKWXnN/BVaIbFMLpouRUrXgVq7WYne5kw3MW7UPhO44uRXQSIpTSXoJbmrR2X0w9kUTyg==
  dependencies:
    "@types/json-schema" "^7.0.9"
    ajv "^8.8.0"
    ajv-formats "^2.1.1"
    ajv-keywords "^5.0.0"

scoped-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.npm.taobao.org/scoped-regex/download/scoped-regex-1.0.0.tgz#a346bb1acd4207ae70bd7c0c7ca9e566b6baddb8"
  integrity sha1-o0a7Gs1CB65wvXwMfKnlZra63bg=

select-hose@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/select-hose/download/select-hose-2.0.0.tgz#625d8658f865af43ec962bfc376a37359a4994ca"
  integrity sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=

selfsigned@^1.10.11:
  version "1.10.11"
  resolved "https://registry.nlark.com/selfsigned/download/selfsigned-1.10.11.tgz#24929cd906fe0f44b6d01fb23999a739537acbe9"
  integrity sha1-JJKc2Qb+D0S20B+yOZmnOVN6y+k=
  dependencies:
    node-forge "^0.10.0"

"semver@2 || 3 || 4 || 5", semver@^5.5.0:
  version "5.7.1"
  resolved "https://registry.nlark.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1622604551741&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@^6.0.0, semver@^6.3.0:
  version "6.3.0"
  resolved "https://registry.nlark.com/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1622604551741&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.1.3, semver@^7.2.1, semver@^7.3.4, semver@^7.3.5:
  version "7.3.5"
  resolved "https://registry.nlark.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1622604551741&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz#0b621c879348d8998e4b0e4be94b3f12e6018ef7"
  integrity sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=
  dependencies:
    lru-cache "^6.0.0"

send@0.17.1:
  version "0.17.1"
  resolved "https://registry.nlark.com/send/download/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^6.0.0:
  version "6.0.0"
  resolved "https://registry.nlark.com/serialize-javascript/download/serialize-javascript-6.0.0.tgz#efae5d88f45d7924141da8b5c3a7a7e663fefeb8"
  integrity sha1-765diPRdeSQUHai1w6en5mP+/rg=
  dependencies:
    randombytes "^2.1.0"

serve-index@^1.9.1:
  version "1.9.1"
  resolved "https://registry.nlark.com/serve-index/download/serve-index-1.9.1.tgz#d3768d69b1e7d82e5ce050fff5b453bea12a9239"
  integrity sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=
  dependencies:
    accepts "~1.3.4"
    batch "0.6.1"
    debug "2.6.9"
    escape-html "~1.0.3"
    http-errors "~1.6.2"
    mime-types "~2.1.17"
    parseurl "~1.3.2"

serve-static@1.14.1:
  version "1.14.1"
  resolved "https://registry.npm.taobao.org/serve-static/download/serve-static-1.14.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fserve-static%2Fdownload%2Fserve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-getter@^0.1.0:
  version "0.1.1"
  resolved "https://registry.nlark.com/set-getter/download/set-getter-0.1.1.tgz#a3110e1b461d31a9cfc8c5c9ee2e9737ad447102"
  integrity sha1-oxEOG0YdManPyMXJ7i6XN61EcQI=
  dependencies:
    to-object-path "^0.3.0"

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "https://registry.nlark.com/set-value/download/set-value-2.0.1.tgz?cache=0&sync_timestamp=1631437857327&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fset-value%2Fdownload%2Fset-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "https://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/shallow-clone/download/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.nlark.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/shebang-regex/download/shebang-regex-1.0.0.tgz?cache=0&sync_timestamp=1628896299850&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/shebang-regex/download/shebang-regex-3.0.0.tgz?cache=0&sync_timestamp=1628896299850&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshebang-regex%2Fdownload%2Fshebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shelljs@^0.8.4:
  version "0.8.4"
  resolved "https://registry.nlark.com/shelljs/download/shelljs-0.8.4.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fshelljs%2Fdownload%2Fshelljs-0.8.4.tgz#de7684feeb767f8716b326078a8a00875890e3c2"
  integrity sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shiki@^0.9.12:
  version "0.9.12"
  resolved "https://registry.npmmirror.com/shiki/download/shiki-0.9.12.tgz#70cbc8c1bb78ff7b356f84a7eecdb040efddd247"
  integrity sha1-cMvIwbt4/3s1b4Sn7s2wQO/d0kc=
  dependencies:
    jsonc-parser "^3.0.0"
    onigasm "^2.2.5"
    vscode-textmate "5.2.0"

signal-exit@^3.0.2, signal-exit@^3.0.3:
  version "3.0.6"
  resolved "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.6.tgz?cache=0&sync_timestamp=1637255763586&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.6.tgz#24e630c4b0f03fea446a2bd299e62b4a6ca8d0af"
  integrity sha512-sDl4qMFpijcGw22U5w63KmD3cZJfBuFlVNbVMKje2keoKML7X2UzWbc4XrmEbDwg0NXJc3yv4/ox7b+JWb57kQ==

slash@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"
  integrity sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=

slash@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "https://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "https://registry.nlark.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "https://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sockjs@^0.3.21:
  version "0.3.21"
  resolved "https://registry.nlark.com/sockjs/download/sockjs-0.3.21.tgz#b34ffb98e796930b60a0cfa11904d6a339a7d417"
  integrity sha1-s0/7mOeWkwtgoM+hGQTWozmn1Bc=
  dependencies:
    faye-websocket "^0.11.3"
    uuid "^3.4.0"
    websocket-driver "^0.7.4"

source-map-js@^0.6.2:
  version "0.6.2"
  resolved "https://registry.npmmirror.com/source-map-js/download/source-map-js-0.6.2.tgz?cache=0&sync_timestamp=1636400772640&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsource-map-js%2Fdownload%2Fsource-map-js-0.6.2.tgz#0bb5de631b41cfbda6cfba8bd05a80efdfd2385e"
  integrity sha1-C7XeYxtBz72mz7qL0FqA79/SOF4=

source-map-resolve@^0.5.0:
  version "0.5.3"
  resolved "https://registry.nlark.com/source-map-resolve/download/source-map-resolve-0.5.3.tgz#190866bece7553e1f8f267a2ee82c606b5509a1a"
  integrity sha1-GQhmvs51U+H48mei7oLGBrVQmho=
  dependencies:
    atob "^2.1.2"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@~0.5.20:
  version "0.5.20"
  resolved "https://registry.nlark.com/source-map-support/download/source-map-support-0.5.20.tgz?cache=0&sync_timestamp=1631179636992&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.20.tgz#12166089f8f5e5e8c56926b377633392dd2cb6c9"
  integrity sha1-EhZgifj15ejFaSazd2Mzkt0stsk=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.1"
  resolved "https://registry.nlark.com/source-map-url/download/source-map-url-0.4.1.tgz#0af66605a745a5a2f91cf1bbf8a7afbc283dec56"
  integrity sha1-CvZmBadFpaL5HPG7+KevvCg97FY=

source-map@^0.5.6:
  version "0.5.7"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.0:
  version "0.6.1"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@~0.7.2:
  version "0.7.3"
  resolved "https://registry.nlark.com/source-map/download/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

spdx-correct@^3.0.0:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.1.tgz#dece81ac9c1e6713e5f7d1b6f17d468fa53d89a9"
  integrity sha1-3s6BrJweZxPl99G28X1Gj6U9iak=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.3.0"
  resolved "https://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz#3f28ce1a77a00372683eade4a433183527a2163d"
  integrity sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.11"
  resolved "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.11.tgz?cache=0&sync_timestamp=1636978474315&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fspdx-license-ids%2Fdownload%2Fspdx-license-ids-3.0.11.tgz#50c0d8c40a14ec1bf449bae69a0ea4685a9d9f95"
  integrity sha512-Ctl2BrFiM0X3MANYgj3CkygxhRmr9mi6xhejbdO960nF6EDJApTYpn0BQnDKlnNBULKiCN1n3w9EBkHK8ZWg+g==

spdy-transport@^3.0.0:
  version "3.0.0"
  resolved "https://registry.npm.taobao.org/spdy-transport/download/spdy-transport-3.0.0.tgz#00d4863a6400ad75df93361a1608605e5dcdcf31"
  integrity sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=
  dependencies:
    debug "^4.1.0"
    detect-node "^2.0.4"
    hpack.js "^2.1.6"
    obuf "^1.1.2"
    readable-stream "^3.0.6"
    wbuf "^1.7.3"

spdy@^4.0.2:
  version "4.0.2"
  resolved "https://registry.npm.taobao.org/spdy/download/spdy-4.0.2.tgz#b74f466203a3eda452c02492b91fb9e84a27677b"
  integrity sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=
  dependencies:
    debug "^4.1.0"
    handle-thing "^2.0.0"
    http-deceiver "^1.2.7"
    select-hose "^2.0.0"
    spdy-transport "^3.0.0"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "https://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

static-extend@^0.1.1:
  version "0.1.2"
  resolved "https://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@~1.5.0:
  version "1.5.0"
  resolved "https://registry.npm.taobao.org/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

string-template@~0.2.1:
  version "0.2.1"
  resolved "https://registry.npm.taobao.org/string-template/download/string-template-0.2.1.tgz#42932e598a352d01fc22ec3367d9d84eec6c9add"
  integrity sha1-QpMuWYo1LQH8IuwzZ9nYTuxsmt0=

string-width@^4.1.0:
  version "4.2.3"
  resolved "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632420968947&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://registry.nlark.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.0:
  version "7.0.1"
  resolved "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-7.0.1.tgz#61740a08ce36b61e50e65653f07060d000975fb2"
  integrity sha1-YXQKCM42th5Q5lZT8HBg0ACXX7I=
  dependencies:
    ansi-regex "^6.0.1"

strip-bom-buf@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/strip-bom-buf/download/strip-bom-buf-1.0.0.tgz#1cb45aaf57530f4caf86c7f75179d2c9a51dd572"
  integrity sha1-HLRar1dTD0yvhsf3UXnSyaUd1XI=
  dependencies:
    is-utf8 "^0.2.1"

strip-bom-stream@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-bom-stream/download/strip-bom-stream-2.0.0.tgz#f87db5ef2613f6968aa545abfe1ec728b6a829ca"
  integrity sha1-+H217yYT9paKpUWr/h7HKLaoKco=
  dependencies:
    first-chunk-stream "^2.0.0"
    strip-bom "^2.0.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  integrity sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=
  dependencies:
    is-utf8 "^0.2.0"

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://registry.nlark.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.1.0, strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "https://registry.nlark.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

style-loader@^3.2.1:
  version "3.3.1"
  resolved "https://registry.npmmirror.com/style-loader/download/style-loader-3.3.1.tgz?cache=0&sync_timestamp=1634872439692&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstyle-loader%2Fdownload%2Fstyle-loader-3.3.1.tgz#057dfa6b3d4d7c7064462830f9113ed417d38575"
  integrity sha1-BX36az1NfHBkRigw+RE+1BfThXU=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz?cache=0&sync_timestamp=1637259109971&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz?cache=0&sync_timestamp=1637259109971&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0:
  version "8.1.1"
  resolved "https://registry.npmmirror.com/supports-color/download/supports-color-8.1.1.tgz?cache=0&sync_timestamp=1637259109971&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupports-color%2Fdownload%2Fsupports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

tapable@^2.0.0, tapable@^2.1.1, tapable@^2.2.0:
  version "2.2.1"
  resolved "https://registry.nlark.com/tapable/download/tapable-2.2.1.tgz#1967a73ef4060a82f12ab96af86d52fdb76eeca0"
  integrity sha1-GWenPvQGCoLxKrlq+G1S/bdu7KA=

terser-webpack-plugin@^5.1.3, terser-webpack-plugin@^5.2.4:
  version "5.2.5"
  resolved "https://registry.npmmirror.com/terser-webpack-plugin/download/terser-webpack-plugin-5.2.5.tgz?cache=0&sync_timestamp=1636385924926&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fterser-webpack-plugin%2Fdownload%2Fterser-webpack-plugin-5.2.5.tgz#ce65b9880a0c36872555c4874f45bbdb02ee32c9"
  integrity sha512-3luOVHku5l0QBeYS8r4CdHYWEGMmIj3H1U64jgkdZzECcSOJAyJ9TjuqcQZvw1Y+4AOBN9SeYJPJmFn2cM4/2g==
  dependencies:
    jest-worker "^27.0.6"
    schema-utils "^3.1.1"
    serialize-javascript "^6.0.0"
    source-map "^0.6.1"
    terser "^5.7.2"

terser@^5.7.2:
  version "5.10.0"
  resolved "https://registry.npmmirror.com/terser/download/terser-5.10.0.tgz#b86390809c0389105eb0a0b62397563096ddafcc"
  integrity sha512-AMmF99DMfEDiRJfxfY5jj5wNH/bYO09cniSqhfoyxc8sFoYIgkJy86G04UoZU5VjlpnplVu0K6Tx6E9b5+DlHA==
  dependencies:
    commander "^2.20.0"
    source-map "~0.7.2"
    source-map-support "~0.5.20"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://registry.nlark.com/text-table/download/text-table-0.2.0.tgz?cache=0&sync_timestamp=1618847142316&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftext-table%2Fdownload%2Ftext-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

textextensions@^2.5.0:
  version "2.6.0"
  resolved "https://registry.nlark.com/textextensions/download/textextensions-2.6.0.tgz#d7e4ab13fe54e32e08873be40d51b74229b00fc4"
  integrity sha1-1+SrE/5U4y4IhzvkDVG3QimwD8Q=

through2@^3.0.0, through2@^3.0.1, through2@^3.0.2:
  version "3.0.2"
  resolved "https://registry.npm.taobao.org/through2/download/through2-3.0.2.tgz#99f88931cfc761ec7678b41d5d7336b5b6a07bf4"
  integrity sha1-mfiJMc/HYex2eLQdXXM2tbage/Q=
  dependencies:
    inherits "^2.0.4"
    readable-stream "2 || 3"

"through@>=2.2.7 <3", through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.npm.taobao.org/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

thunky@^1.0.2:
  version "1.1.0"
  resolved "https://registry.nlark.com/thunky/download/thunky-1.1.0.tgz#5abaf714a9405db0504732bbccd2cedd9ef9537d"
  integrity sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=

timed-out@^4.0.0:
  version "4.0.1"
  resolved "https://registry.nlark.com/timed-out/download/timed-out-4.0.1.tgz#f32eacac5a175bea25d7fab565ab3ed8741ef56f"
  integrity sha1-8y6srFoXW+ol1/q1Zas+2HQe9W8=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.nlark.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "https://registry.nlark.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://registry.nlark.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "https://registry.nlark.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz?cache=0&sync_timestamp=1636938489272&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftoidentifier%2Fdownload%2Ftoidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

tr46@~0.0.3:
  version "0.0.3"
  resolved "https://registry.npmmirror.com/tr46/download/tr46-0.0.3.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftr46%2Fdownload%2Ftr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

ts-loader@^9.2.5:
  version "9.2.6"
  resolved "https://registry.nlark.com/ts-loader/download/ts-loader-9.2.6.tgz?cache=0&sync_timestamp=1632169134951&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fts-loader%2Fdownload%2Fts-loader-9.2.6.tgz#9937c4dd0a1e3dbbb5e433f8102a6601c6615d74"
  integrity sha1-mTfE3QoePbu15DP4ECpmAcZhXXQ=
  dependencies:
    chalk "^4.1.0"
    enhanced-resolve "^5.0.0"
    micromatch "^4.0.0"
    semver "^7.3.4"

tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "https://registry.nlark.com/tslib/download/tslib-1.14.1.tgz?cache=0&sync_timestamp=1628722556410&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftslib%2Fdownload%2Ftslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.3:
  version "2.3.1"
  resolved "https://registry.nlark.com/tslib/download/tslib-2.3.1.tgz?cache=0&sync_timestamp=1628722556410&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftslib%2Fdownload%2Ftslib-2.3.1.tgz#e8a335add5ceae51aa261d32a490158ef042ef01"
  integrity sha1-6KM1rdXOrlGqJh0ypJAVjvBC7wE=

tsutils@^3.21.0:
  version "3.21.0"
  resolved "https://registry.nlark.com/tsutils/download/tsutils-3.21.0.tgz?cache=0&sync_timestamp=1622604538827&other_urls=https%3A%2F%2Fregistry.nlark.com%2Ftsutils%2Fdownload%2Ftsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://registry.npm.taobao.org/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.20.2.tgz?cache=0&sync_timestamp=1637149905951&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.21.3.tgz?cache=0&sync_timestamp=1637149905951&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://registry.npmmirror.com/type-fest/download/type-fest-0.6.0.tgz?cache=0&sync_timestamp=1637149905951&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftype-fest%2Fdownload%2Ftype-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "https://registry.nlark.com/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedoc@^0.22.7:
  version "0.22.9"
  resolved "https://registry.npmmirror.com/typedoc/download/typedoc-0.22.9.tgz#7e3f4f72586bbef4c2382f0afcea8e0f9270b712"
  integrity sha512-84PjudoXVcap6bwdZFbYIUWlgdz/iLV09ZHwrCzhtHWXaDQG6mlosJ8te6DSThuRkRvQjp46HO+qY/P7Gpm78g==
  dependencies:
    glob "^7.2.0"
    lunr "^2.3.9"
    marked "^3.0.8"
    minimatch "^3.0.4"
    shiki "^0.9.12"

typescript@^4.4.3:
  version "4.5.2"
  resolved "https://registry.npmmirror.com/typescript/download/typescript-4.5.2.tgz#8ac1fba9f52256fdb06fb89e4122fa6a346c2998"
  integrity sha512-5BlMof9H1yGt0P8/WF+wPNw6GfctgGjXp5hkblpyT+8rkASSmkUKMXrxR0Xg8ThVCi/JnHQiKXeBaEwCeQwMFw==

union-value@^1.0.0:
  version "1.0.1"
  resolved "https://registry.nlark.com/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

untildify@^3.0.3:
  version "3.0.3"
  resolved "https://registry.nlark.com/untildify/download/untildify-3.0.3.tgz#1e7b42b140bcfd922b22e70ca1265bfe3634c7c9"
  integrity sha1-HntCsUC8/ZIrIucMoSZb/jY0x8k=

unzip-response@^2.0.1:
  version "2.0.1"
  resolved "https://registry.npmmirror.com/unzip-response/download/unzip-response-2.0.1.tgz#d2f0f737d16b0615e72a6935ed04214572d56f97"
  integrity sha1-0vD3N9FrBhXnKmk17QQhRXLVb5c=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "https://registry.npm.taobao.org/uri-js/download/uri-js-4.4.1.tgz?cache=0&sync_timestamp=1610237624359&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Furi-js%2Fdownload%2Furi-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "https://registry.npmmirror.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url-parse-lax@^1.0.0:
  version "1.0.0"
  resolved "https://registry.nlark.com/url-parse-lax/download/url-parse-lax-1.0.0.tgz?cache=0&sync_timestamp=1628547550655&other_urls=https%3A%2F%2Fregistry.nlark.com%2Furl-parse-lax%2Fdownload%2Furl-parse-lax-1.0.0.tgz#7af8f303645e9bd79a272e7a14ac68bc0609da73"
  integrity sha1-evjzA2Rem9eaJy56FKxovAYJ2nM=
  dependencies:
    prepend-http "^1.0.1"

url@^0.11.0:
  version "0.11.0"
  resolved "https://registry.nlark.com/url/download/url-0.11.0.tgz?cache=0&sync_timestamp=1618847135337&other_urls=https%3A%2F%2Fregistry.nlark.com%2Furl%2Fdownload%2Furl-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use@^3.1.0:
  version "3.1.1"
  resolved "https://registry.nlark.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.nlark.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

utila@~0.4:
  version "0.4.0"
  resolved "https://registry.nlark.com/utila/download/utila-0.4.0.tgz#8a16a05d445657a3aea5eecc5b12a4fa5379772c"
  integrity sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.nlark.com/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.4.0:
  version "3.4.0"
  resolved "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v8-compile-cache@^2.0.3:
  version "2.3.0"
  resolved "https://registry.nlark.com/v8-compile-cache/download/v8-compile-cache-2.3.0.tgz#2de19618c66dc247dcfb6f99338035d8245a2cee"
  integrity sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4=

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://registry.nlark.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.nlark.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vinyl-file@^3.0.0:
  version "3.0.0"
  resolved "https://registry.nlark.com/vinyl-file/download/vinyl-file-3.0.0.tgz#b104d9e4409ffa325faadd520642d0a3b488b365"
  integrity sha1-sQTZ5ECf+jJfqt1SBkLQo7SIs2U=
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.3.0"
    strip-bom-buf "^1.0.0"
    strip-bom-stream "^2.0.0"
    vinyl "^2.0.1"

vinyl@^2.0.1, vinyl@^2.2.0, vinyl@^2.2.1:
  version "2.2.1"
  resolved "https://registry.nlark.com/vinyl/download/vinyl-2.2.1.tgz#23cfb8bbab5ece3803aa2c0a1eb28af7cbba1974"
  integrity sha1-I8+4u6tezjgDqiwKHrKK98u6GXQ=
  dependencies:
    clone "^2.1.1"
    clone-buffer "^1.0.0"
    clone-stats "^1.0.0"
    cloneable-readable "^1.0.0"
    remove-trailing-separator "^1.0.1"
    replace-ext "^1.0.0"

vscode-textmate@5.2.0:
  version "5.2.0"
  resolved "https://registry.npmmirror.com/vscode-textmate/download/vscode-textmate-5.2.0.tgz#01f01760a391e8222fe4f33fbccbd1ad71aed74e"
  integrity sha1-AfAXYKOR6CIv5PM/vMvRrXGu104=

watchpack@^2.2.0:
  version "2.2.0"
  resolved "https://registry.nlark.com/watchpack/download/watchpack-2.2.0.tgz#47d78f5415fe550ecd740f99fe2882323a58b1ce"
  integrity sha1-R9ePVBX+VQ7NdA+Z/iiCMjpYsc4=
  dependencies:
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.1.2"

wbuf@^1.1.0, wbuf@^1.7.3:
  version "1.7.3"
  resolved "https://registry.npm.taobao.org/wbuf/download/wbuf-1.7.3.tgz#c1d8d149316d3ea852848895cb6a0bfe887b87df"
  integrity sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=
  dependencies:
    minimalistic-assert "^1.0.0"

web-ifc@0.0.26:
  version "0.0.26"
  resolved "https://registry.npmmirror.com/web-ifc/download/web-ifc-0.0.26.tgz#b2ba8479bc9911ae834364d5e3477372a3966699"
  integrity sha1-srqEebyZEa6DQ2TV40dzcqOWZpk=

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "https://registry.nlark.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webpack-cli@^4.8.0, webpack-cli@^4.9.1:
  version "4.9.1"
  resolved "https://registry.npmmirror.com/webpack-cli/download/webpack-cli-4.9.1.tgz#b64be825e2d1b130f285c314caa3b1ba9a4632b3"
  integrity sha1-tkvoJeLRsTDyhcMUyqOxuppGMrM=
  dependencies:
    "@discoveryjs/json-ext" "^0.5.0"
    "@webpack-cli/configtest" "^1.1.0"
    "@webpack-cli/info" "^1.4.0"
    "@webpack-cli/serve" "^1.6.0"
    colorette "^2.0.14"
    commander "^7.0.0"
    execa "^5.0.0"
    fastest-levenshtein "^1.0.12"
    import-local "^3.0.2"
    interpret "^2.2.0"
    rechoir "^0.7.0"
    webpack-merge "^5.7.3"

webpack-dev-middleware@^5.2.1:
  version "5.2.2"
  resolved "https://registry.npmmirror.com/webpack-dev-middleware/download/webpack-dev-middleware-5.2.2.tgz#eb5193faa5479ca1086b9f7bed68b89c731bff62"
  integrity sha512-DjZyYrsHhkikAFNvSNKrpnziXukU1EChFAh9j4LAm6ndPLPW8cN0KhM7T+RAiOqsQ6ABfQ8hoKIs9IWMTjov+w==
  dependencies:
    colorette "^2.0.10"
    memfs "^3.2.2"
    mime-types "^2.1.31"
    range-parser "^1.2.1"
    schema-utils "^4.0.0"

webpack-dev-server@^4.3.1:
  version "4.5.0"
  resolved "https://registry.npmmirror.com/webpack-dev-server/download/webpack-dev-server-4.5.0.tgz#614b5112cfa4730a4801bb4ddebb3be5b0d70497"
  integrity sha512-Ss4WptsUjYa+3hPI4iYZYEc8FrtnfkaPrm5WTjk9ux5kiCS718836srs0ppKMHRaCHP5mQ6g4JZGcfDdGbCjpQ==
  dependencies:
    ansi-html-community "^0.0.8"
    bonjour "^3.5.0"
    chokidar "^3.5.2"
    colorette "^2.0.10"
    compression "^1.7.4"
    connect-history-api-fallback "^1.6.0"
    del "^6.0.0"
    express "^4.17.1"
    graceful-fs "^4.2.6"
    html-entities "^2.3.2"
    http-proxy-middleware "^2.0.0"
    internal-ip "^6.2.0"
    ipaddr.js "^2.0.1"
    open "^8.0.9"
    p-retry "^4.5.0"
    portfinder "^1.0.28"
    schema-utils "^3.1.0"
    selfsigned "^1.10.11"
    serve-index "^1.9.1"
    sockjs "^0.3.21"
    spdy "^4.0.2"
    strip-ansi "^7.0.0"
    url "^0.11.0"
    webpack-dev-middleware "^5.2.1"
    ws "^8.1.0"

webpack-merge@^5.7.3:
  version "5.8.0"
  resolved "https://registry.nlark.com/webpack-merge/download/webpack-merge-5.8.0.tgz#2b39dbf22af87776ad744c390223731d30a68f61"
  integrity sha1-Kznb8ir4d3atdEw5AiNzHTCmj2E=
  dependencies:
    clone-deep "^4.0.1"
    wildcard "^2.0.0"

webpack-sources@^3.2.2:
  version "3.2.2"
  resolved "https://registry.npmmirror.com/webpack-sources/download/webpack-sources-3.2.2.tgz?cache=0&sync_timestamp=1636982760786&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack-sources%2Fdownload%2Fwebpack-sources-3.2.2.tgz#d88e3741833efec57c4c789b6010db9977545260"
  integrity sha512-cp5qdmHnu5T8wRg2G3vZZHoJPN14aqQ89SyQ11NpGH5zEMDCclt49rzo+MaRazk7/UeILhAI+/sEtcM+7Fr0nw==

webpack@^5.52.1:
  version "5.64.1"
  resolved "https://registry.npmmirror.com/webpack/download/webpack-5.64.1.tgz?cache=0&sync_timestamp=1636996758967&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwebpack%2Fdownload%2Fwebpack-5.64.1.tgz#fd59840c16f04fe315f2b2598a85026f12dfa1bb"
  integrity sha512-b4FHmRgaaAjP+aVOVz41a9Qa5SmkUPQ+u8FntTQ1roPHahSComB6rXnLwc976VhUY4CqTaLu5mCswuHiNhOfVw==
  dependencies:
    "@types/eslint-scope" "^3.7.0"
    "@types/estree" "^0.0.50"
    "@webassemblyjs/ast" "1.11.1"
    "@webassemblyjs/wasm-edit" "1.11.1"
    "@webassemblyjs/wasm-parser" "1.11.1"
    acorn "^8.4.1"
    acorn-import-assertions "^1.7.6"
    browserslist "^4.14.5"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^5.8.3"
    es-module-lexer "^0.9.0"
    eslint-scope "5.1.1"
    events "^3.2.0"
    glob-to-regexp "^0.4.1"
    graceful-fs "^4.2.4"
    json-parse-better-errors "^1.0.2"
    loader-runner "^4.2.0"
    mime-types "^2.1.27"
    neo-async "^2.6.2"
    schema-utils "^3.1.0"
    tapable "^2.1.1"
    terser-webpack-plugin "^5.1.3"
    watchpack "^2.2.0"
    webpack-sources "^3.2.2"

websocket-driver@>=0.5.1, websocket-driver@^0.7.4:
  version "0.7.4"
  resolved "https://registry.nlark.com/websocket-driver/download/websocket-driver-0.7.4.tgz#89ad5295bbf64b480abcba31e4953aca706f5760"
  integrity sha1-ia1Slbv2S0gKvLox5JU6ynBvV2A=
  dependencies:
    http-parser-js ">=0.5.1"
    safe-buffer ">=5.1.0"
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.4"
  resolved "https://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.4.tgz#7f8473bc839dfd87608adb95d7eb075211578a42"
  integrity sha1-f4RzvIOd/YdgituV1+sHUhFXikI=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "https://registry.npmmirror.com/whatwg-url/download/whatwg-url-5.0.0.tgz?cache=0&sync_timestamp=1634673646149&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwhatwg-url%2Fdownload%2Fwhatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^1.2.9:
  version "1.3.1"
  resolved "https://registry.nlark.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "https://registry.nlark.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wildcard@^2.0.0:
  version "2.0.0"
  resolved "https://registry.npm.taobao.org/wildcard/download/wildcard-2.0.0.tgz#a77d20e5200c6faaac979e4b3aadc7b3dd7f8fec"
  integrity sha1-p30g5SAMb6qsl55LOq3Hs91/j+w=

with-open-file@^0.1.6:
  version "0.1.7"
  resolved "https://registry.nlark.com/with-open-file/download/with-open-file-0.1.7.tgz#e2de8d974e8a8ae6e58886be4fe8e7465b58a729"
  integrity sha1-4t6Nl06KiubliIa+T+jnRltYpyk=
  dependencies:
    p-finally "^1.0.0"
    p-try "^2.1.0"
    pify "^4.0.1"

word-wrap@^1.2.3:
  version "1.2.3"
  resolved "https://registry.nlark.com/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wrappy@1:
  version "1.0.2"
  resolved "https://registry.nlark.com/wrappy/download/wrappy-1.0.2.tgz?cache=0&sync_timestamp=1619133505879&other_urls=https%3A%2F%2Fregistry.nlark.com%2Fwrappy%2Fdownload%2Fwrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

ws@^8.1.0:
  version "8.2.3"
  resolved "https://registry.npmmirror.com/ws/download/ws-8.2.3.tgz#63a56456db1b04367d0b721a0b80cae6d8becbba"
  integrity sha1-Y6VkVtsbBDZ9C3IaC4DK5ti+y7o=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://registry.npm.taobao.org/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://registry.npm.taobao.org/yallist/download/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yeoman-environment@^2.10.3, yeoman-environment@^2.9.5:
  version "2.10.3"
  resolved "https://registry.npmmirror.com/yeoman-environment/download/yeoman-environment-2.10.3.tgz?cache=0&sync_timestamp=1636471316615&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyeoman-environment%2Fdownload%2Fyeoman-environment-2.10.3.tgz#9d8f42b77317414434cc0e51fb006a4bdd54688e"
  integrity sha1-nY9Ct3MXQUQ0zA5R+wBqS91UaI4=
  dependencies:
    chalk "^2.4.1"
    debug "^3.1.0"
    diff "^3.5.0"
    escape-string-regexp "^1.0.2"
    execa "^4.0.0"
    globby "^8.0.1"
    grouped-queue "^1.1.0"
    inquirer "^7.1.0"
    is-scoped "^1.0.0"
    lodash "^4.17.10"
    log-symbols "^2.2.0"
    mem-fs "^1.1.0"
    mem-fs-editor "^6.0.0"
    npm-api "^1.0.0"
    semver "^7.1.3"
    strip-ansi "^4.0.0"
    text-table "^0.2.0"
    untildify "^3.0.3"
    yeoman-generator "^4.8.2"

yeoman-generator@^4.12.0, yeoman-generator@^4.8.2:
  version "4.13.0"
  resolved "https://registry.npmmirror.com/yeoman-generator/download/yeoman-generator-4.13.0.tgz#a6caeed8491fceea1f84f53e31795f25888b4672"
  integrity sha1-psru2EkfzuofhPU+MXlfJYiLRnI=
  dependencies:
    async "^2.6.2"
    chalk "^2.4.2"
    cli-table "^0.3.1"
    cross-spawn "^6.0.5"
    dargs "^6.1.0"
    dateformat "^3.0.3"
    debug "^4.1.1"
    diff "^4.0.1"
    error "^7.0.2"
    find-up "^3.0.0"
    github-username "^3.0.0"
    istextorbinary "^2.5.1"
    lodash "^4.17.11"
    make-dir "^3.0.0"
    mem-fs-editor "^7.0.1"
    minimist "^1.2.5"
    pretty-bytes "^5.2.0"
    read-chunk "^3.2.0"
    read-pkg-up "^5.0.0"
    rimraf "^2.6.3"
    run-async "^2.0.0"
    semver "^7.2.1"
    shelljs "^0.8.4"
    text-table "^0.2.0"
    through2 "^3.0.1"
  optionalDependencies:
    grouped-queue "^1.1.0"
    yeoman-environment "^2.9.5"
