<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>CurveType | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="CurveType.html">CurveType</a></li></ul><h1>Enumeration CurveType</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Enumeration members</h3><ul class="tsd-index-list"><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#CUBIC_BEZIER_CURVE" class="tsd-kind-icon">CUBIC_<wbr/>BEZIER_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#CURVE" class="tsd-kind-icon">CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#CURVE_PATH" class="tsd-kind-icon">CURVE_<wbr/>PATH</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#ELLIPSE_CURVE" class="tsd-kind-icon">ELLIPSE_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#LINE_CURVE" class="tsd-kind-icon">LINE_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#PATH" class="tsd-kind-icon">PATH</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#QUADRATIC_BEZIER_CURVE" class="tsd-kind-icon">QUADRATIC_<wbr/>BEZIER_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#SHAPE" class="tsd-kind-icon">SHAPE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#SPLINE_CURVE" class="tsd-kind-icon">SPLINE_<wbr/>CURVE</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Enumeration members</h2><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="CUBIC_BEZIER_CURVE" class="tsd-anchor"></a><h3>CUBIC_<wbr/>BEZIER_<wbr/>CURVE</h3><div class="tsd-signature tsd-kind-icon">CUBIC_<wbr/>BEZIER_<wbr/>CURVE<span class="tsd-signature-symbol"> = &quot;CubicBezierCurve&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:16</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="CURVE" class="tsd-anchor"></a><h3>CURVE</h3><div class="tsd-signature tsd-kind-icon">CURVE<span class="tsd-signature-symbol"> = &quot;Curve&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:14</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="CURVE_PATH" class="tsd-anchor"></a><h3>CURVE_<wbr/>PATH</h3><div class="tsd-signature tsd-kind-icon">CURVE_<wbr/>PATH<span class="tsd-signature-symbol"> = &quot;CurvePath&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:15</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="ELLIPSE_CURVE" class="tsd-anchor"></a><h3>ELLIPSE_<wbr/>CURVE</h3><div class="tsd-signature tsd-kind-icon">ELLIPSE_<wbr/>CURVE<span class="tsd-signature-symbol"> = &quot;EllipseCurve&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:17</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="LINE_CURVE" class="tsd-anchor"></a><h3>LINE_<wbr/>CURVE</h3><div class="tsd-signature tsd-kind-icon">LINE_<wbr/>CURVE<span class="tsd-signature-symbol"> = &quot;LineCurve&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:18</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="PATH" class="tsd-anchor"></a><h3>PATH</h3><div class="tsd-signature tsd-kind-icon">PATH<span class="tsd-signature-symbol"> = &quot;Path&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:19</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="QUADRATIC_BEZIER_CURVE" class="tsd-anchor"></a><h3>QUADRATIC_<wbr/>BEZIER_<wbr/>CURVE</h3><div class="tsd-signature tsd-kind-icon">QUADRATIC_<wbr/>BEZIER_<wbr/>CURVE<span class="tsd-signature-symbol"> = &quot;QuadraticBezierCurve&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:20</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="SHAPE" class="tsd-anchor"></a><h3>SHAPE</h3><div class="tsd-signature tsd-kind-icon">SHAPE<span class="tsd-signature-symbol"> = &quot;Shape&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:21</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="SPLINE_CURVE" class="tsd-anchor"></a><h3>SPLINE_<wbr/>CURVE</h3><div class="tsd-signature tsd-kind-icon">SPLINE_<wbr/>CURVE<span class="tsd-signature-symbol"> = &quot;SplineCurve&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:22</li></ul></aside></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-enum"><a href="CurveType.html" class="tsd-kind-icon">Curve<wbr/>Type</a><ul><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#CUBIC_BEZIER_CURVE" class="tsd-kind-icon">CUBIC_<wbr/>BEZIER_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#CURVE" class="tsd-kind-icon">CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#CURVE_PATH" class="tsd-kind-icon">CURVE_<wbr/>PATH</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#ELLIPSE_CURVE" class="tsd-kind-icon">ELLIPSE_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#LINE_CURVE" class="tsd-kind-icon">LINE_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#PATH" class="tsd-kind-icon">PATH</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#QUADRATIC_BEZIER_CURVE" class="tsd-kind-icon">QUADRATIC_<wbr/>BEZIER_<wbr/>CURVE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#SHAPE" class="tsd-kind-icon">SHAPE</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="CurveType.html#SPLINE_CURVE" class="tsd-kind-icon">SPLINE_<wbr/>CURVE</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>