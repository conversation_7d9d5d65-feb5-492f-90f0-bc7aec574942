<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>SectionPlanePopPanelItemConfig | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="SectionPlanePopPanelItemConfig.html">SectionPlanePopPanelItemConfig</a></li></ul><h1>Interface SectionPlanePopPanelItemConfig</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">SectionPlanePopPanelItemConfig</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#canDisable" class="tsd-kind-icon">can<wbr/>Disable</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#content" class="tsd-kind-icon">content</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#hoverActiveTitle" class="tsd-kind-icon">hover<wbr/>Active<wbr/>Title</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#hoverTitle" class="tsd-kind-icon">hover<wbr/>Title</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#iconActiveClass" class="tsd-kind-icon">icon<wbr/>Active<wbr/>Class</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#iconClass" class="tsd-kind-icon">icon<wbr/>Class</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#isActive" class="tsd-kind-icon">is<wbr/>Active</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#isClickable" class="tsd-kind-icon">is<wbr/>Clickable</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#isResetAll" class="tsd-kind-icon">is<wbr/>Reset<wbr/>All</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#itemName" class="tsd-kind-icon">item<wbr/>Name</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#onActive" class="tsd-kind-icon">on<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#onClick" class="tsd-kind-icon">on<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#onDeactive" class="tsd-kind-icon">on<wbr/>Deactive</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="canDisable" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> can<wbr/>Disable</h3><div class="tsd-signature tsd-kind-icon">can<wbr/>Disable<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:11</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="content" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> content</h3><div class="tsd-signature tsd-kind-icon">content<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:10</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="hoverActiveTitle" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> hover<wbr/>Active<wbr/>Title</h3><div class="tsd-signature tsd-kind-icon">hover<wbr/>Active<wbr/>Title<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:7</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="hoverTitle" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> hover<wbr/>Title</h3><div class="tsd-signature tsd-kind-icon">hover<wbr/>Title<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:6</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="iconActiveClass" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> icon<wbr/>Active<wbr/>Class</h3><div class="tsd-signature tsd-kind-icon">icon<wbr/>Active<wbr/>Class<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:9</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="iconClass" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> icon<wbr/>Class</h3><div class="tsd-signature tsd-kind-icon">icon<wbr/>Class<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:8</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="isActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> is<wbr/>Active</h3><div class="tsd-signature tsd-kind-icon">is<wbr/>Active<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:12</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="isClickable" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> is<wbr/>Clickable</h3><div class="tsd-signature tsd-kind-icon">is<wbr/>Clickable<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:13</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="isResetAll" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> is<wbr/>Reset<wbr/>All</h3><div class="tsd-signature tsd-kind-icon">is<wbr/>Reset<wbr/>All<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:14</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="itemName" class="tsd-anchor"></a><h3>item<wbr/>Name</h3><div class="tsd-signature tsd-kind-icon">item<wbr/>Name<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:5</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-interface"><a id="onActive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> on<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-interface"><li class="tsd-signature tsd-kind-icon">on<wbr/>Active<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:16</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-interface"><a id="onClick" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> on<wbr/>Click</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-interface"><li class="tsd-signature tsd-kind-icon">on<wbr/>Click<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:15</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-interface"><a id="onDeactive" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> on<wbr/>Deactive</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-interface"><li class="tsd-signature tsd-kind-icon">on<wbr/>Deactive<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in components/SectionPlanePopPanel.ts:17</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">undefined</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">boolean</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-interface"><a href="SectionPlanePopPanelItemConfig.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel<wbr/>Item<wbr/>Config</a><ul><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#canDisable" class="tsd-kind-icon">can<wbr/>Disable</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#content" class="tsd-kind-icon">content</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#hoverActiveTitle" class="tsd-kind-icon">hover<wbr/>Active<wbr/>Title</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#hoverTitle" class="tsd-kind-icon">hover<wbr/>Title</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#iconActiveClass" class="tsd-kind-icon">icon<wbr/>Active<wbr/>Class</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#iconClass" class="tsd-kind-icon">icon<wbr/>Class</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#isActive" class="tsd-kind-icon">is<wbr/>Active</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#isClickable" class="tsd-kind-icon">is<wbr/>Clickable</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#isResetAll" class="tsd-kind-icon">is<wbr/>Reset<wbr/>All</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#itemName" class="tsd-kind-icon">item<wbr/>Name</a></li><li class="tsd-kind-method tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#onActive" class="tsd-kind-icon">on<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#onClick" class="tsd-kind-icon">on<wbr/>Click</a></li><li class="tsd-kind-method tsd-parent-kind-interface"><a href="SectionPlanePopPanelItemConfig.html#onDeactive" class="tsd-kind-icon">on<wbr/>Deactive</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>