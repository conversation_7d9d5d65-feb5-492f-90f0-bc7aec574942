<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>PlanViewPlugin | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="PlanViewPlugin.html">PlanViewPlugin</a></li></ul><h1>Class PlanViewPlugin</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><div class="lead">
<p>2d plan view plugin</p>
</div></div></section><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="../modules/math.html" class="tsd-signature-type" data-tsd-kind="Namespace">math</a><ul class="tsd-hierarchy"><li><span class="target">PlanViewPlugin</span></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="PlanViewPlugin.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="PlanViewPlugin.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="PlanViewPlugin.html#_originalSettings" class="tsd-kind-icon">_original<wbr/>Settings</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="PlanViewPlugin.html#_zoomToExtent" class="tsd-kind-icon">_zoom<wbr/>To<wbr/>Extent</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#enter2dMode" class="tsd-kind-icon">enter2d<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#exit2dMode" class="tsd-kind-icon">exit2d<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#getActive" class="tsd-kind-icon">get<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#setActive" class="tsd-kind-icon">set<wbr/>Active</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Plan<wbr/>View<wbr/>Plugin<span class="tsd-signature-symbol">(</span>viewer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="PlanViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">PlanViewPlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides Plugin.constructor</p><ul><li>Defined in plugins/PlanViewPlugin.ts:18</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>viewer: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="PlanViewPlugin.html" class="tsd-signature-type" data-tsd-kind="Class">PlanViewPlugin</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_active" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _active</h3><div class="tsd-signature tsd-kind-icon">_active<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol"> = false</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:9</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_originalSettings" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _original<wbr/>Settings</h3><div class="tsd-signature tsd-kind-icon">_original<wbr/>Settings<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>cameraCfg<span class="tsd-signature-symbol">?: </span><a href="../interfaces/CameraConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">CameraConfig</a><span class="tsd-signature-symbol">; </span>navCubeVisible<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">; </span>navMode<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>projection<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = {}</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:11</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5><span class="tsd-flag ts-flagOptional">Optional</span> camera<wbr/>Cfg<span class="tsd-signature-symbol">?: </span><a href="../interfaces/CameraConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">CameraConfig</a></h5></li><li class="tsd-parameter"><h5><span class="tsd-flag ts-flagOptional">Optional</span> nav<wbr/>Cube<wbr/>Visible<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span></h5></li><li class="tsd-parameter"><h5><span class="tsd-flag ts-flagOptional">Optional</span> nav<wbr/>Mode<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5><span class="tsd-flag ts-flagOptional">Optional</span> projection<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span></h5></li></ul></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_zoomToExtent" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _zoom<wbr/>To<wbr/>Extent</h3><div class="tsd-signature tsd-kind-icon">_zoom<wbr/>To<wbr/>Extent<span class="tsd-signature-symbol">:</span> <a href="ZoomToExtent.html" class="tsd-signature-type" data-tsd-kind="Class">ZoomToExtent</a></div><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:10</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:120</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="enter2dMode" class="tsd-anchor"></a><h3>enter2d<wbr/>Mode</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">enter2d<wbr/>Mode<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:38</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="exit2dMode" class="tsd-anchor"></a><h3>exit2d<wbr/>Mode</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">exit2d<wbr/>Mode<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:84</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="getActive" class="tsd-anchor"></a><h3>get<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">get<wbr/>Active<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:33</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="setActive" class="tsd-anchor"></a><h3>set<wbr/>Active</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">set<wbr/>Active<span class="tsd-signature-symbol">(</span>active<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/PlanViewPlugin.ts:24</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>active: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="PlanViewPlugin.html" class="tsd-kind-icon">Plan<wbr/>View<wbr/>Plugin</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="PlanViewPlugin.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="PlanViewPlugin.html#_active" class="tsd-kind-icon">_active</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="PlanViewPlugin.html#_originalSettings" class="tsd-kind-icon">_original<wbr/>Settings</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="PlanViewPlugin.html#_zoomToExtent" class="tsd-kind-icon">_zoom<wbr/>To<wbr/>Extent</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#destroy" class="tsd-kind-icon">destroy</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#enter2dMode" class="tsd-kind-icon">enter2d<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#exit2dMode" class="tsd-kind-icon">exit2d<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#getActive" class="tsd-kind-icon">get<wbr/>Active</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="PlanViewPlugin.html#setActive" class="tsd-kind-icon">set<wbr/>Active</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>