export const en = {
    // English
    NavCube: {
        front: "Front",
        back: "Back",
        top: "Top",
        bottom: "Bottom",
        left: "Left",
        right: "Right",
    },
    ContextMenu: {
        viewFitAll: "View Fit All",
        hideAll: "Hide All",
        showAll: "Show All",
        xRayAll: "X-Ray",
        xRayNone: "X-Ray None",
        selectNone: "Select None",
        resetView: "Reset View",
        viewFitEntity: "View Fit",
        hideEntity: "Hide",
        hideOthers: "Hide Others",
        xRayEntity: "X-Ray",
        xRayOthers: "X-Ray Others",
        select: "Select",
        deselect: "Undo Select",
        showSectionPlane: "Show Section Plane",
        showSectionBox: "Show Section Box",
        showAxisSection: "Show Axis Section",
        hideSectionPlane: "Hide Section Plane",
        hideSectionBox: "Hide Section Box",
        hideAxisSection: "Hide Axis Section",
        undoSection: "Undo Section",
    },
    Toolbar: {
        homeView: "Home",
        orthoView: "Ortho View",
        measurement: "Measure",
        distanceMeasurement: "Distance Measurement",
        areaMeasurement: "Area Measurement",
        clearMeasurement: "Clear Measurement",
        section: "Section",
        axisSection: "Axis Section",
        pickSectionPlane: "Pick Section Plane",
        sectionBox: "Section Box",
        bimTree: "BIM Tree",
        viewpoint: "Viewpoint",
        annotation: "Annotation",
        property: "Property",
        settings: "Settings",
        fullscreen: "Full Screen",
    },
    Tooltip: {
        measure: "Pick a point to continue measuring, pressing ESC could exit measure mode.",
        section: "Click to pick a section plane",
    },
    PopPanel: {
        reset: "Reset",
    },
};

export const cn = {
    // Chinese
    NavCube: {
        front: "前",
        back: "后",
        top: "上",
        bottom: "下",
        left: "左",
        right: "右",
    },
    ContextMenu: {
        viewFitAll: "缩放视口到所有模型",
        hideAll: "全部隐藏",
        showAll: "全部显示",
        xRayAll: "应用 X 光模式",
        xRayNone: "清除 X 光模式",
        selectNone: "清空选择",
        resetView: "重置视图",
        viewFitEntity: "缩放视口到实体",
        hideEntity: "隐藏",
        hideOthers: "隐藏其他 (隔离)",
        xRayEntity: "应用 X 光模式",
        xRayOthers: "对其他实体应用 X 光模式",
        select: "选择",
        deselect: "取消选择",
        showSectionPlane: "显示剖切面",
        showSectionBox: "显示剖切盒",
        showAxisSection: "显示轴向剖切",
        hideSectionPlane: "隐藏剖切面",
        hideSectionBox: "隐藏剖切盒",
        hideAxisSection: "隐藏轴向剖切",
        undoSection: "取消剖切",
    },
    Toolbar: {
        homeView: "主视图",
        orthoView: "正交视图",
        measurement: "测量",
        distanceMeasurement: "距离测量",
        areaMeasurement: "面积测量",
        clearMeasurement: "清除测量",
        section: "剖切",
        axisSection: "轴向剖切",
        pickSectionPlane: "拾取面剖切",
        sectionBox: "剖切盒",
        bimTree: "BIM树",
        viewpoint: "视点",
        annotation: "批注",
        property: "属性",
        settings: "配置",
        fullscreen: "全屏",
    },
    Tooltip: {
        measure: "点击继续测量, ESC 取消测量",
        section: "点击确定剖切面",
    },
    PopPanel: {
        reset: "重置",
    },
};
