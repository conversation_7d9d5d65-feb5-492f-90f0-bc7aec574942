<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>BimViewerConfig | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="BimViewerConfig.html">BimViewerConfig</a></li></ul><h1>Interface BimViewerConfig</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><div class="lead">
<p>This wrappers most config for Viewer (xeokit-sdk\src\viewer\Viewer.js)</p>
</div></div></section><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">BimViewerConfig</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#activeOrthoMode" class="tsd-kind-icon">active<wbr/>Ortho<wbr/>Mode</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#antialias" class="tsd-kind-icon">antialias</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#axisGizmoCanvasId" class="tsd-kind-icon">axis<wbr/>Gizmo<wbr/>Canvas<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#backgroundColor" class="tsd-kind-icon">background<wbr/>Color</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#canvasId" class="tsd-kind-icon">canvas<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableAxisGizmo" class="tsd-kind-icon">enable<wbr/>Axis<wbr/>Gizmo</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableBottomBar" class="tsd-kind-icon">enable<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableContextMenu" class="tsd-kind-icon">enable<wbr/>Context<wbr/>Menu</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableFastNav" class="tsd-kind-icon">enable<wbr/>Fast<wbr/>Nav</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableNavCube" class="tsd-kind-icon">enable<wbr/>Nav<wbr/>Cube</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableSingleSelection" class="tsd-kind-icon">enable<wbr/>Single<wbr/>Selection</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableToolbar" class="tsd-kind-icon">enable<wbr/>Toolbar</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#gammaInput" class="tsd-kind-icon">gamma<wbr/>Input</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#gammaOutput" class="tsd-kind-icon">gamma<wbr/>Output</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#locale" class="tsd-kind-icon">locale</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#navCubeCanvasId" class="tsd-kind-icon">nav<wbr/>Cube<wbr/>Canvas<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#origin" class="tsd-kind-icon">origin</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#pbrEnabled" class="tsd-kind-icon">pbr<wbr/>Enabled</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#saoEnabled" class="tsd-kind-icon">sao<wbr/>Enabled</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#scale" class="tsd-kind-icon">scale</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#skyBoxImgSrc" class="tsd-kind-icon">sky<wbr/>Box<wbr/>Img<wbr/>Src</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#spinnerElementId" class="tsd-kind-icon">spinner<wbr/>Element<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#swapYZ" class="tsd-kind-icon">swapYZ</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#transparent" class="tsd-kind-icon">transparent</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#units" class="tsd-kind-icon">units</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="activeOrthoMode" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> active<wbr/>Ortho<wbr/>Mode</h3><div class="tsd-signature tsd-kind-icon">active<wbr/>Ortho<wbr/>Mode<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:197</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Enter ortho mode by default.</p>
</div></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="antialias" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> antialias</h3><div class="tsd-signature tsd-kind-icon">antialias<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:137</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="axisGizmoCanvasId" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> axis<wbr/>Gizmo<wbr/>Canvas<wbr/>Id</h3><div class="tsd-signature tsd-kind-icon">axis<wbr/>Gizmo<wbr/>Canvas<wbr/>Id<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:133</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>The id of the customized canvas to draw AxisGizmo.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>It will use the default AxisGizmo when this param is empty.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="backgroundColor" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> background<wbr/>Color</h3><div class="tsd-signature tsd-kind-icon">background<wbr/>Color<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:153</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene.canvas</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="canvasId" class="tsd-anchor"></a><h3>canvas<wbr/>Id</h3><div class="tsd-signature tsd-kind-icon">canvas<wbr/>Id<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:113</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For Xeokit Viewer.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="enableAxisGizmo" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr/>Axis<wbr/>Gizmo</h3><div class="tsd-signature tsd-kind-icon">enable<wbr/>Axis<wbr/>Gizmo<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:81</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Shows the AxisGizmo.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>true</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="enableBottomBar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr/>Bottom<wbr/>Bar</h3><div class="tsd-signature tsd-kind-icon">enable<wbr/>Bottom<wbr/>Bar<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:91</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>shows the bottom-bar.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>true</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="enableContextMenu" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr/>Context<wbr/>Menu</h3><div class="tsd-signature tsd-kind-icon">enable<wbr/>Context<wbr/>Menu<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:96</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Shows the context-menu.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>true</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="enableFastNav" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr/>Fast<wbr/>Nav</h3><div class="tsd-signature tsd-kind-icon">enable<wbr/>Fast<wbr/>Nav<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:102</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Enables FastNav</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Viewer plugin that improves interactivity by disabling expensive rendering effects while the Camera is moving.
Default is <code>true</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="enableNavCube" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr/>Nav<wbr/>Cube</h3><div class="tsd-signature tsd-kind-icon">enable<wbr/>Nav<wbr/>Cube<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:76</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Shows the NavCube.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>true</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="enableSingleSelection" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr/>Single<wbr/>Selection</h3><div class="tsd-signature tsd-kind-icon">enable<wbr/>Single<wbr/>Selection<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:107</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Enable single selection.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>true</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="enableToolbar" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> enable<wbr/>Toolbar</h3><div class="tsd-signature tsd-kind-icon">enable<wbr/>Toolbar<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:86</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Shows the toolbar.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>true</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="gammaInput" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> gamma<wbr/>Input</h3><div class="tsd-signature tsd-kind-icon">gamma<wbr/>Input<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:145</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="gammaOutput" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> gamma<wbr/>Output</h3><div class="tsd-signature tsd-kind-icon">gamma<wbr/>Output<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:149</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="locale" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> locale</h3><div class="tsd-signature tsd-kind-icon">locale<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">&quot;cn&quot;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">&quot;en&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:202</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Sets the default locale</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>cn</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="navCubeCanvasId" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> nav<wbr/>Cube<wbr/>Canvas<wbr/>Id</h3><div class="tsd-signature tsd-kind-icon">nav<wbr/>Cube<wbr/>Canvas<wbr/>Id<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:128</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>The id of the customized canvas to draw NavCube.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>It will use the default NavCube when this param is empty.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="origin" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> origin</h3><div class="tsd-signature tsd-kind-icon">origin<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:171</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene.metrics</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="pbrEnabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> pbr<wbr/>Enabled</h3><div class="tsd-signature tsd-kind-icon">pbr<wbr/>Enabled<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:179</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="saoEnabled" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> sao<wbr/>Enabled</h3><div class="tsd-signature tsd-kind-icon">sao<wbr/>Enabled<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:175</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene.sao</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="scale" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> scale</h3><div class="tsd-signature tsd-kind-icon">scale<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">number</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:167</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene.metrics</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="skyBoxImgSrc" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> sky<wbr/>Box<wbr/>Img<wbr/>Src</h3><div class="tsd-signature tsd-kind-icon">sky<wbr/>Box<wbr/>Img<wbr/>Src<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:207</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>The image src of the skybox.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>It will use default background color when this param is empty.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="spinnerElementId" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> spinner<wbr/>Element<wbr/>Id</h3><div class="tsd-signature tsd-kind-icon">spinner<wbr/>Element<wbr/>Id<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:118</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>The id of customized spinner element.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="swapYZ" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> swapYZ</h3><div class="tsd-signature tsd-kind-icon">swapYZ<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:123</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Swaps Y / Z axis.</p>
</div><dl class="tsd-comment-tags"><dt>description</dt><dd><p>Default is <code>false</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="transparent" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> transparent</h3><div class="tsd-signature tsd-kind-icon">transparent<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">boolean</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:141</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene</code>.</p>
</dd></dl></div></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-interface"><a id="units" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagOptional">Optional</span> units</h3><div class="tsd-signature tsd-kind-icon">units<span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:163</li></ul></aside><div class="tsd-comment tsd-typography"><dl class="tsd-comment-tags"><dt>description</dt><dd><p>For <code>Xeokit Viewer.scene.metrics</code>.</p>
<p>Default is <code>meters</code></p>
</dd></dl></div></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-interface"><a href="BimViewerConfig.html" class="tsd-kind-icon">Bim<wbr/>Viewer<wbr/>Config</a><ul><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#activeOrthoMode" class="tsd-kind-icon">active<wbr/>Ortho<wbr/>Mode</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#antialias" class="tsd-kind-icon">antialias</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#axisGizmoCanvasId" class="tsd-kind-icon">axis<wbr/>Gizmo<wbr/>Canvas<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#backgroundColor" class="tsd-kind-icon">background<wbr/>Color</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#canvasId" class="tsd-kind-icon">canvas<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableAxisGizmo" class="tsd-kind-icon">enable<wbr/>Axis<wbr/>Gizmo</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableBottomBar" class="tsd-kind-icon">enable<wbr/>Bottom<wbr/>Bar</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableContextMenu" class="tsd-kind-icon">enable<wbr/>Context<wbr/>Menu</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableFastNav" class="tsd-kind-icon">enable<wbr/>Fast<wbr/>Nav</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableNavCube" class="tsd-kind-icon">enable<wbr/>Nav<wbr/>Cube</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableSingleSelection" class="tsd-kind-icon">enable<wbr/>Single<wbr/>Selection</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#enableToolbar" class="tsd-kind-icon">enable<wbr/>Toolbar</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#gammaInput" class="tsd-kind-icon">gamma<wbr/>Input</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#gammaOutput" class="tsd-kind-icon">gamma<wbr/>Output</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#locale" class="tsd-kind-icon">locale</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#navCubeCanvasId" class="tsd-kind-icon">nav<wbr/>Cube<wbr/>Canvas<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#origin" class="tsd-kind-icon">origin</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#pbrEnabled" class="tsd-kind-icon">pbr<wbr/>Enabled</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#saoEnabled" class="tsd-kind-icon">sao<wbr/>Enabled</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#scale" class="tsd-kind-icon">scale</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#skyBoxImgSrc" class="tsd-kind-icon">sky<wbr/>Box<wbr/>Img<wbr/>Src</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#spinnerElementId" class="tsd-kind-icon">spinner<wbr/>Element<wbr/>Id</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#swapYZ" class="tsd-kind-icon">swapYZ</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#transparent" class="tsd-kind-icon">transparent</a></li><li class="tsd-kind-property tsd-parent-kind-interface"><a href="BimViewerConfig.html#units" class="tsd-kind-icon">units</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>