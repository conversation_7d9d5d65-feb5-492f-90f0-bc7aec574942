<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>ToolbarMenuId | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="ToolbarMenuId.html">ToolbarMenuId</a></li></ul><h1>Enumeration ToolbarMenuId</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><div class="lead">
<p>Buildin toolbar ids</p>
</div></div></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Enumeration members</h3><ul class="tsd-index-list"><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Annotation" class="tsd-kind-icon">Annotation</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#AxisSectionPlane" class="tsd-kind-icon">Axis<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#BimTree" class="tsd-kind-icon">Bim<wbr/>Tree</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Fullscreen" class="tsd-kind-icon">Fullscreen</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#HomeView" class="tsd-kind-icon">Home<wbr/>View</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Measure" class="tsd-kind-icon">Measure</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#MeasureArea" class="tsd-kind-icon">Measure<wbr/>Area</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#MeasureClear" class="tsd-kind-icon">Measure<wbr/>Clear</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#MeasureDistance" class="tsd-kind-icon">Measure<wbr/>Distance</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#OrthoMode" class="tsd-kind-icon">Ortho<wbr/>Mode</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Property" class="tsd-kind-icon">Property</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Section" class="tsd-kind-icon">Section</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#SectionBox" class="tsd-kind-icon">Section<wbr/>Box</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#SectionPlane" class="tsd-kind-icon">Section<wbr/>Plane</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Settings" class="tsd-kind-icon">Settings</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Viewpoint" class="tsd-kind-icon">Viewpoint</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Enumeration members</h2><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="Annotation" class="tsd-anchor"></a><h3>Annotation</h3><div class="tsd-signature tsd-kind-icon">Annotation<span class="tsd-signature-symbol"> = &quot;Annotation&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:32</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="AxisSectionPlane" class="tsd-anchor"></a><h3>Axis<wbr/>Section<wbr/>Plane</h3><div class="tsd-signature tsd-kind-icon">Axis<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol"> = &quot;AxisSectionPlane&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:29</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="BimTree" class="tsd-anchor"></a><h3>Bim<wbr/>Tree</h3><div class="tsd-signature tsd-kind-icon">Bim<wbr/>Tree<span class="tsd-signature-symbol"> = &quot;BimTree&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:30</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="Fullscreen" class="tsd-anchor"></a><h3>Fullscreen</h3><div class="tsd-signature tsd-kind-icon">Fullscreen<span class="tsd-signature-symbol"> = &quot;FullScreen&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:35</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="HomeView" class="tsd-anchor"></a><h3>Home<wbr/>View</h3><div class="tsd-signature tsd-kind-icon">Home<wbr/>View<span class="tsd-signature-symbol"> = &quot;HomeView&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:20</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="Measure" class="tsd-anchor"></a><h3>Measure</h3><div class="tsd-signature tsd-kind-icon">Measure<span class="tsd-signature-symbol"> = &quot;Measure&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:22</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="MeasureArea" class="tsd-anchor"></a><h3>Measure<wbr/>Area</h3><div class="tsd-signature tsd-kind-icon">Measure<wbr/>Area<span class="tsd-signature-symbol"> = &quot;MeasureArea&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:24</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="MeasureClear" class="tsd-anchor"></a><h3>Measure<wbr/>Clear</h3><div class="tsd-signature tsd-kind-icon">Measure<wbr/>Clear<span class="tsd-signature-symbol"> = &quot;MeasureClear&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:25</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="MeasureDistance" class="tsd-anchor"></a><h3>Measure<wbr/>Distance</h3><div class="tsd-signature tsd-kind-icon">Measure<wbr/>Distance<span class="tsd-signature-symbol"> = &quot;MeasureDistance&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:23</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="OrthoMode" class="tsd-anchor"></a><h3>Ortho<wbr/>Mode</h3><div class="tsd-signature tsd-kind-icon">Ortho<wbr/>Mode<span class="tsd-signature-symbol"> = &quot;OrthoMode&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:21</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="Property" class="tsd-anchor"></a><h3>Property</h3><div class="tsd-signature tsd-kind-icon">Property<span class="tsd-signature-symbol"> = &quot;Property&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:33</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="Section" class="tsd-anchor"></a><h3>Section</h3><div class="tsd-signature tsd-kind-icon">Section<span class="tsd-signature-symbol"> = &quot;Section&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:26</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="SectionBox" class="tsd-anchor"></a><h3>Section<wbr/>Box</h3><div class="tsd-signature tsd-kind-icon">Section<wbr/>Box<span class="tsd-signature-symbol"> = &quot;SectionBox&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:27</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="SectionPlane" class="tsd-anchor"></a><h3>Section<wbr/>Plane</h3><div class="tsd-signature tsd-kind-icon">Section<wbr/>Plane<span class="tsd-signature-symbol"> = &quot;SectionPlane&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:28</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="Settings" class="tsd-anchor"></a><h3>Settings</h3><div class="tsd-signature tsd-kind-icon">Settings<span class="tsd-signature-symbol"> = &quot;Settings&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:34</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-enum-member tsd-parent-kind-enum"><a id="Viewpoint" class="tsd-anchor"></a><h3>Viewpoint</h3><div class="tsd-signature tsd-kind-icon">Viewpoint<span class="tsd-signature-symbol"> = &quot;Viewpoint&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:31</li></ul></aside></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-enum"><a href="ToolbarMenuId.html" class="tsd-kind-icon">Toolbar<wbr/>Menu<wbr/>Id</a><ul><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Annotation" class="tsd-kind-icon">Annotation</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#AxisSectionPlane" class="tsd-kind-icon">Axis<wbr/>Section<wbr/>Plane</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#BimTree" class="tsd-kind-icon">Bim<wbr/>Tree</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Fullscreen" class="tsd-kind-icon">Fullscreen</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#HomeView" class="tsd-kind-icon">Home<wbr/>View</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Measure" class="tsd-kind-icon">Measure</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#MeasureArea" class="tsd-kind-icon">Measure<wbr/>Area</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#MeasureClear" class="tsd-kind-icon">Measure<wbr/>Clear</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#MeasureDistance" class="tsd-kind-icon">Measure<wbr/>Distance</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#OrthoMode" class="tsd-kind-icon">Ortho<wbr/>Mode</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Property" class="tsd-kind-icon">Property</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Section" class="tsd-kind-icon">Section</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#SectionBox" class="tsd-kind-icon">Section<wbr/>Box</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#SectionPlane" class="tsd-kind-icon">Section<wbr/>Plane</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Settings" class="tsd-kind-icon">Settings</a></li><li class="tsd-kind-enum-member tsd-parent-kind-enum"><a href="ToolbarMenuId.html#Viewpoint" class="tsd-kind-icon">Viewpoint</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>