<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>KeyBoardRotatePlugin | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="KeyBoardRotatePlugin.html">KeyBoardRotatePlugin</a></li></ul><h1>Class KeyBoardRotatePlugin</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-comment"><div class="tsd-comment tsd-typography"><div class="lead">
<p>Customize the keyboard rotation.
The default mode is orbit. Customize to the first people</p>
</div></div></section><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><a href="../modules/math.html" class="tsd-signature-type" data-tsd-kind="Namespace">math</a><ul class="tsd-hierarchy"><li><span class="target">KeyBoardRotatePlugin</span></li></ul></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="KeyBoardRotatePlugin.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_cameraControl" class="tsd-kind-icon">_camera<wbr/>Control</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_documentKeyDownHandler" class="tsd-kind-icon">_document<wbr/>Key<wbr/>Down<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_documentKeyUpHandler" class="tsd-kind-icon">_document<wbr/>Key<wbr/>Up<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_originNavMode" class="tsd-kind-icon">_origin<wbr/>Nav<wbr/>Mode</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_isKeyForAction" class="tsd-kind-icon">_is<wbr/>Key<wbr/>For<wbr/>Action</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_isKeyForRotate" class="tsd-kind-icon">_is<wbr/>Key<wbr/>For<wbr/>Rotate</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="KeyBoardRotatePlugin.html#destroy" class="tsd-kind-icon">destroy</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><li class="tsd-signature tsd-kind-icon">new <wbr/>Key<wbr/>Board<wbr/>Rotate<wbr/>Plugin<span class="tsd-signature-symbol">(</span>viewer<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, cfg<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="KeyBoardRotatePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">KeyBoardRotatePlugin</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><p>Overrides Plugin.constructor</p><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:16</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>viewer: <span class="tsd-signature-type">any</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> cfg: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="KeyBoardRotatePlugin.html" class="tsd-signature-type" data-tsd-kind="Class">KeyBoardRotatePlugin</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_cameraControl" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _camera<wbr/>Control</h3><div class="tsd-signature tsd-kind-icon">_camera<wbr/>Control<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:13</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_documentKeyDownHandler" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _document<wbr/>Key<wbr/>Down<wbr/>Handler</h3><div class="tsd-signature tsd-kind-icon">_document<wbr/>Key<wbr/>Down<wbr/>Handler<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">KeyboardCallbackType</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:11</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_documentKeyUpHandler" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _document<wbr/>Key<wbr/>Up<wbr/>Handler</h3><div class="tsd-signature tsd-kind-icon">_document<wbr/>Key<wbr/>Up<wbr/>Handler<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">KeyboardCallbackType</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:12</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-private"><a id="_originNavMode" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _origin<wbr/>Nav<wbr/>Mode</h3><div class="tsd-signature tsd-kind-icon">_origin<wbr/>Nav<wbr/>Mode<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">any</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:14</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_isKeyForAction" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _is<wbr/>Key<wbr/>For<wbr/>Action</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_is<wbr/>Key<wbr/>For<wbr/>Action<span class="tsd-signature-symbol">(</span>action<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, keyCode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:97</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>action: <span class="tsd-signature-type">any</span></h5></li><li><h5>keyCode: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class tsd-is-private"><a id="_isKeyForRotate" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagPrivate">Private</span> _is<wbr/>Key<wbr/>For<wbr/>Rotate</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class tsd-is-private"><li class="tsd-signature tsd-kind-icon">_is<wbr/>Key<wbr/>For<wbr/>Rotate<span class="tsd-signature-symbol">(</span>keyCode<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">boolean</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:79</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>keyCode: <span class="tsd-signature-type">any</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">boolean</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="destroy" class="tsd-anchor"></a><h3>destroy</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">destroy<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in plugins/KeyBoardRotatePlugin.ts:72</li></ul></aside><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="KeyBoardRotatePlugin.html" class="tsd-kind-icon">Key<wbr/>Board<wbr/>Rotate<wbr/>Plugin</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-overwrite"><a href="KeyBoardRotatePlugin.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_cameraControl" class="tsd-kind-icon">_camera<wbr/>Control</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_documentKeyDownHandler" class="tsd-kind-icon">_document<wbr/>Key<wbr/>Down<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_documentKeyUpHandler" class="tsd-kind-icon">_document<wbr/>Key<wbr/>Up<wbr/>Handler</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_originNavMode" class="tsd-kind-icon">_origin<wbr/>Nav<wbr/>Mode</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_isKeyForAction" class="tsd-kind-icon">_is<wbr/>Key<wbr/>For<wbr/>Action</a></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><a href="KeyBoardRotatePlugin.html#_isKeyForRotate" class="tsd-kind-icon">_is<wbr/>Key<wbr/>For<wbr/>Rotate</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="KeyBoardRotatePlugin.html#destroy" class="tsd-kind-icon">destroy</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>