<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>@pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="assets/style.css"/><link rel="stylesheet" href="assets/highlight.css"/><script async src="assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base="."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><h1>@pattern-x/gemini-viewer</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Namespaces</h3><ul class="tsd-index-list"><li class="tsd-kind-namespace"><a href="modules/math.html" class="tsd-kind-icon">math</a></li></ul></section><section class="tsd-index-section "><h3>Enumerations</h3><ul class="tsd-index-list"><li class="tsd-kind-enum"><a href="enums/AxisType.html" class="tsd-kind-icon">Axis<wbr/>Type</a></li><li class="tsd-kind-enum"><a href="enums/BoxSectionPlaneType.html" class="tsd-kind-icon">Box<wbr/>Section<wbr/>Plane<wbr/>Type</a></li><li class="tsd-kind-enum"><a href="enums/CurveType.html" class="tsd-kind-icon">Curve<wbr/>Type</a></li><li class="tsd-kind-enum"><a href="enums/ToolbarMenuId.html" class="tsd-kind-icon">Toolbar<wbr/>Menu<wbr/>Id</a></li></ul></section><section class="tsd-index-section "><h3>Classes</h3><ul class="tsd-index-list"><li class="tsd-kind-class"><a href="classes/AxisSectionPlaneController.html" class="tsd-kind-icon">Axis<wbr/>Section<wbr/>Plane<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/AxisSectionPlanePlugin.html" class="tsd-kind-icon">Axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/BackgroundColorPlugin.html" class="tsd-kind-icon">Background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/BimTreeController.html" class="tsd-kind-icon">Bim<wbr/>Tree<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/BimViewer.html" class="tsd-kind-icon">Bim<wbr/>Viewer</a></li><li class="tsd-kind-class"><a href="classes/BottomBar.html" class="tsd-kind-icon">Bottom<wbr/>Bar</a></li><li class="tsd-kind-class"><a href="classes/BoxControl.html" class="tsd-kind-icon">Box<wbr/>Control</a></li><li class="tsd-kind-class"><a href="classes/CommonUtils.html" class="tsd-kind-icon">Common<wbr/>Utils</a></li><li class="tsd-kind-class"><a href="classes/ComponentPropertyPlugin.html" class="tsd-kind-icon">Component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/Controller.html" class="tsd-kind-icon">Controller</a></li><li class="tsd-kind-class"><a href="classes/CubicBezierCurve.html" class="tsd-kind-icon">Cubic<wbr/>Bezier<wbr/>Curve</a></li><li class="tsd-kind-class tsd-has-type-parameter"><a href="classes/Curve.html" class="tsd-kind-icon">Curve</a></li><li class="tsd-kind-class"><a href="classes/CurvePath.html" class="tsd-kind-icon">Curve<wbr/>Path</a></li><li class="tsd-kind-class"><a href="classes/CustomizedGLTFLoaderPlugin.html" class="tsd-kind-icon">CustomizedGLTFLoader<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/DxfLoaderPlugin.html" class="tsd-kind-icon">Dxf<wbr/>Loader<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/DxfPerformanceModelLoader.html" class="tsd-kind-icon">Dxf<wbr/>Performance<wbr/>Model<wbr/>Loader</a></li><li class="tsd-kind-class"><a href="classes/EllipseCurve.html" class="tsd-kind-icon">Ellipse<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/EnhancedDistanceMeasurementPlugin.html" class="tsd-kind-icon">Enhanced<wbr/>Distance<wbr/>Measurement<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/FontManager.html" class="tsd-kind-icon">Font<wbr/>Manager</a></li><li class="tsd-kind-class"><a href="classes/FullScreenController.html" class="tsd-kind-icon">Full<wbr/>Screen<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/FullScreenPlugin.html" class="tsd-kind-icon">Full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/GeometryUtils.html" class="tsd-kind-icon">Geometry<wbr/>Utils</a></li><li class="tsd-kind-class"><a href="classes/GridPlugin.html" class="tsd-kind-icon">Grid<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/HomeViewController.html" class="tsd-kind-icon">Home<wbr/>View<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/KeyBoardRotatePlugin.html" class="tsd-kind-icon">Key<wbr/>Board<wbr/>Rotate<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/LineCurve.html" class="tsd-kind-icon">Line<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/Map.html" class="tsd-kind-icon">Map</a></li><li class="tsd-kind-class"><a href="classes/MathUtil.html" class="tsd-kind-icon">Math<wbr/>Util</a></li><li class="tsd-kind-class"><a href="classes/MeasureAreaController.html" class="tsd-kind-icon">Measure<wbr/>Area<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/MeasureClearController.html" class="tsd-kind-icon">Measure<wbr/>Clear<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/MeasureController.html" class="tsd-kind-icon">Measure<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/MeasureDistanceController.html" class="tsd-kind-icon">Measure<wbr/>Distance<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/NavControlConfig.html" class="tsd-kind-icon">Nav<wbr/>Control<wbr/>Config</a></li><li class="tsd-kind-class"><a href="classes/ObjectUtil.html" class="tsd-kind-icon">Object<wbr/>Util</a></li><li class="tsd-kind-class"><a href="classes/OrthoModeController.html" class="tsd-kind-icon">Ortho<wbr/>Mode<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/OrthoModePlugin.html" class="tsd-kind-icon">Ortho<wbr/>Mode<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/Path.html" class="tsd-kind-icon">Path</a></li><li class="tsd-kind-class"><a href="classes/PlanViewPlugin.html" class="tsd-kind-icon">Plan<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/PlaneControl.html" class="tsd-kind-icon">Plane<wbr/>Control</a></li><li class="tsd-kind-class"><a href="classes/PopPanel.html" class="tsd-kind-icon">Pop<wbr/>Panel</a></li><li class="tsd-kind-class"><a href="classes/PropertyController.html" class="tsd-kind-icon">Property<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/QuadraticBezierCurve.html" class="tsd-kind-icon">Quadratic<wbr/>Bezier<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/SceneGraphTreeView.html" class="tsd-kind-icon">Scene<wbr/>Graph<wbr/>Tree<wbr/>View</a></li><li class="tsd-kind-class"><a href="classes/SceneGraphTreeViewPlugin.html" class="tsd-kind-icon">Scene<wbr/>Graph<wbr/>Tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionBoxController.html" class="tsd-kind-icon">Section<wbr/>Box<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/SectionBoxPlugin.html" class="tsd-kind-icon">Section<wbr/>Box<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionController.html" class="tsd-kind-icon">Section<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/SectionCullPlanePlugin.html" class="tsd-kind-icon">Section<wbr/>Cull<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionPlaneController.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/SectionPlanePlugin.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionPlanePopPanel.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-class"><a href="classes/Shape.html" class="tsd-kind-icon">Shape</a></li><li class="tsd-kind-class"><a href="classes/ShapePath.html" class="tsd-kind-icon">Shape<wbr/>Path</a></li><li class="tsd-kind-class"><a href="classes/ShapeUtils.html" class="tsd-kind-icon">Shape<wbr/>Utils</a></li><li class="tsd-kind-class"><a href="classes/SingleSelectionPlugin.html" class="tsd-kind-icon">Single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SplineCurve.html" class="tsd-kind-icon">Spline<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/TextGeometry.html" class="tsd-kind-icon">Text<wbr/>Geometry</a></li><li class="tsd-kind-class"><a href="classes/Toolbar.html" class="tsd-kind-icon">Toolbar</a></li><li class="tsd-kind-class"><a href="classes/ToolbarMenuBaseController.html" class="tsd-kind-icon">Toolbar<wbr/>Menu<wbr/>Base<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/Tooltip.html" class="tsd-kind-icon">Tooltip</a></li><li class="tsd-kind-class"><a href="classes/ZoomToExtent.html" class="tsd-kind-icon">Zoom<wbr/>To<wbr/>Extent</a></li></ul></section><section class="tsd-index-section "><h3>Interfaces</h3><ul class="tsd-index-list"><li class="tsd-kind-interface"><a href="interfaces/BackgroundColorConfig.html" class="tsd-kind-icon">Background<wbr/>Color<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BimViewerConfig.html" class="tsd-kind-icon">Bim<wbr/>Viewer<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BuildEllipseGeometryConfig.html" class="tsd-kind-icon">Build<wbr/>Ellipse<wbr/>Geometry<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BuildPlaneGeometryConfig.html" class="tsd-kind-icon">Build<wbr/>Plane<wbr/>Geometry<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BuildPlanePositionConfig.html" class="tsd-kind-icon">Build<wbr/>Plane<wbr/>Position<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/CameraConfig.html" class="tsd-kind-icon">Camera<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/ComponentPropertyConfig.html" class="tsd-kind-icon">Component<wbr/>Property<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/Context.html" class="tsd-kind-icon">Context</a></li><li class="tsd-kind-interface"><a href="interfaces/ContextMenuConfig.html" class="tsd-kind-icon">Context<wbr/>Menu<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/CustomizedGLTFLoaderConfig.html" class="tsd-kind-icon">CustomizedGLTFLoader<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/GridConfig.html" class="tsd-kind-icon">Grid<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/GridGeometryConfig.html" class="tsd-kind-icon">Grid<wbr/>Geometry<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/GridMeshConfig.html" class="tsd-kind-icon">Grid<wbr/>Mesh<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/IconClass.html" class="tsd-kind-icon">Icon<wbr/>Class</a></li><li class="tsd-kind-interface"><a href="interfaces/ModelConfig.html" class="tsd-kind-icon">Model<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/OrthoModeConfig.html" class="tsd-kind-icon">Ortho<wbr/>Mode<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionCullPlaneConfig.html" class="tsd-kind-icon">Section<wbr/>Cull<wbr/>Plane<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionOverviewConfig.html" class="tsd-kind-icon">Section<wbr/>Overview<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionPlanePopPanelConfig.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel<wbr/>Item<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SingleSelectionConfig.html" class="tsd-kind-icon">Single<wbr/>Selection<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/TextGeometryParameter.html" class="tsd-kind-icon">Text<wbr/>Geometry<wbr/>Parameter</a></li><li class="tsd-kind-interface"><a href="interfaces/ToolbarMenuConfig.html" class="tsd-kind-icon">Toolbar<wbr/>Menu<wbr/>Config</a></li></ul></section><section class="tsd-index-section "><h3>Type aliases</h3><ul class="tsd-index-list"><li class="tsd-kind-type-alias tsd-has-type-parameter"><a href="modules.html#CallbackRetureVoidType" class="tsd-kind-icon">Callback<wbr/>Reture<wbr/>Void<wbr/>Type</a></li><li class="tsd-kind-type-alias tsd-has-type-parameter"><a href="modules.html#CallbackType" class="tsd-kind-icon">Callback<wbr/>Type</a></li><li class="tsd-kind-type-alias"><a href="modules.html#EmptyCallbackType" class="tsd-kind-icon">Empty<wbr/>Callback<wbr/>Type</a></li><li class="tsd-kind-type-alias"><a href="modules.html#NoParamCallback" class="tsd-kind-icon">No<wbr/>Param<wbr/>Callback</a></li><li class="tsd-kind-type-alias"><a href="modules.html#ToolbarConfig" class="tsd-kind-icon">Toolbar<wbr/>Config</a></li><li class="tsd-kind-type-alias"><a href="modules.html#VecType" class="tsd-kind-icon">Vec<wbr/>Type</a></li></ul></section><section class="tsd-index-section "><h3>Variables</h3><ul class="tsd-index-list"><li class="tsd-kind-variable"><a href="modules.html#AXIS_SECTION_PLANE_CONTROL_ID" class="tsd-kind-icon">AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#AXIS_SECTION_PLANE_ID" class="tsd-kind-icon">AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#DEFAULT_BACKGROUND_COLOR" class="tsd-kind-icon">DEFAULT_<wbr/>BACKGROUND_<wbr/>COLOR</a></li><li class="tsd-kind-variable"><a href="modules.html#DEFAULT_BIM_VIEWER_CONFIG" class="tsd-kind-icon">DEFAULT_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#DEFAULT_TOOLBAR_CONFIG" class="tsd-kind-icon">DEFAULT_<wbr/>TOOLBAR_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#ENTER_KEY" class="tsd-kind-icon">ENTER_<wbr/>KEY</a></li><li class="tsd-kind-variable"><a href="modules.html#ESC_KEY" class="tsd-kind-icon">ESC_<wbr/>KEY</a></li><li class="tsd-kind-variable"><a href="modules.html#GROUP_CONFIG" class="tsd-kind-icon">GROUP_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#ICON_FONT_CLASS" class="tsd-kind-icon">ICON_<wbr/>FONT_<wbr/>CLASS</a></li><li class="tsd-kind-variable"><a href="modules.html#KEYDOWN_EVENT" class="tsd-kind-icon">KEYDOWN_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#KEYUP_EVENT" class="tsd-kind-icon">KEYUP_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#MOUSEDOWN_EVENT" class="tsd-kind-icon">MOUSEDOWN_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#MOUSEMOVE_EVENT" class="tsd-kind-icon">MOUSEMOVE_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#MOUSEUP_EVENT" class="tsd-kind-icon">MOUSEUP_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#SECTION_BOX_ID" class="tsd-kind-icon">SECTION_<wbr/>BOX_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#SECTION_PLANE_CONTROL_ID" class="tsd-kind-icon">SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#SECTION_PLANE_ID" class="tsd-kind-icon">SECTION_<wbr/>PLANE_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#SIMPLE_BIM_VIEWER_CONFIG" class="tsd-kind-icon">SIMPLE_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#cn" class="tsd-kind-icon">cn</a></li><li class="tsd-kind-variable"><a href="modules.html#en" class="tsd-kind-icon">en</a></li></ul></section><section class="tsd-index-section "><h3>Functions</h3><ul class="tsd-index-list"><li class="tsd-kind-function"><a href="modules.html#CatmullRom" class="tsd-kind-icon">Catmull<wbr/>Rom</a></li><li class="tsd-kind-function"><a href="modules.html#CubicBezier" class="tsd-kind-icon">Cubic<wbr/>Bezier</a></li><li class="tsd-kind-function"><a href="modules.html#QuadraticBezier" class="tsd-kind-icon">Quadratic<wbr/>Bezier</a></li><li class="tsd-kind-function"><a href="modules.html#addPrefix" class="tsd-kind-icon">add<wbr/>Prefix</a></li><li class="tsd-kind-function"><a href="modules.html#createCurveByType" class="tsd-kind-icon">create<wbr/>Curve<wbr/>By<wbr/>Type</a></li><li class="tsd-kind-function"><a href="modules.html#showContextMenu" class="tsd-kind-icon">show<wbr/>Context<wbr/>Menu</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Type aliases</h2><section class="tsd-panel tsd-member tsd-kind-type-alias tsd-has-type-parameter"><a id="CallbackRetureVoidType" class="tsd-anchor"></a><h3>Callback<wbr/>Reture<wbr/>Void<wbr/>Type</h3><div class="tsd-signature tsd-kind-icon">Callback<wbr/>Reture<wbr/>Void<wbr/>Type<span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type" data-tsd-kind="Type parameter">T</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">:</span> <a href="modules.html#CallbackType" class="tsd-signature-type" data-tsd-kind="Type alias">CallbackType</a><span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type" data-tsd-kind="Type parameter">T</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">void</span><span class="tsd-signature-symbol">&gt;</span></div><aside class="tsd-sources"><ul><li>Defined in core/CallbackTypes.ts:4</li></ul></aside><h4 class="tsd-type-parameters-title">Type parameters</h4><ul class="tsd-type-parameters"><li><h4>T</h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-type-alias tsd-has-type-parameter"><a id="CallbackType" class="tsd-anchor"></a><h3>Callback<wbr/>Type</h3><div class="tsd-signature tsd-kind-icon">Callback<wbr/>Type<span class="tsd-signature-symbol">&lt;</span><span class="tsd-signature-type" data-tsd-kind="Type parameter">T</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type" data-tsd-kind="Type parameter">U</span><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type" data-tsd-kind="Type parameter">T</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type" data-tsd-kind="Type parameter">U</span></div><aside class="tsd-sources"><ul><li>Defined in core/CallbackTypes.ts:3</li></ul></aside><h4 class="tsd-type-parameters-title">Type parameters</h4><ul class="tsd-type-parameters"><li><h4>T</h4></li><li><h4>U</h4></li></ul><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter-signature"><ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>event<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type" data-tsd-kind="Type parameter">T</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type" data-tsd-kind="Type parameter">U</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>event: <span class="tsd-signature-type" data-tsd-kind="Type parameter">T</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type" data-tsd-kind="Type parameter">U</span></h4></li></ul></li></ul></div></section><section class="tsd-panel tsd-member tsd-kind-type-alias"><a id="EmptyCallbackType" class="tsd-anchor"></a><h3>Empty<wbr/>Callback<wbr/>Type</h3><div class="tsd-signature tsd-kind-icon">Empty<wbr/>Callback<wbr/>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div><aside class="tsd-sources"><ul><li>Defined in core/CallbackTypes.ts:2</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter-signature"><ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></li></ul></div></section><section class="tsd-panel tsd-member tsd-kind-type-alias"><a id="NoParamCallback" class="tsd-anchor"></a><h3>No<wbr/>Param<wbr/>Callback</h3><div class="tsd-signature tsd-kind-icon">No<wbr/>Param<wbr/>Callback<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">void</span></div><aside class="tsd-sources"><ul><li>Defined in plugins/OrthoModePlugin.ts:3</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter-signature"><ul class="tsd-signatures tsd-kind-type-literal tsd-parent-kind-type-alias"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></li></ul></div></section><section class="tsd-panel tsd-member tsd-kind-type-alias"><a id="ToolbarConfig" class="tsd-anchor"></a><h3>Toolbar<wbr/>Config</h3><div class="tsd-signature tsd-kind-icon">Toolbar<wbr/>Config<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{</span> <span class="tsd-signature-symbol">[ </span><span class="tsd-signature-type">key</span><span class="tsd-signature-symbol"> in </span><a href="enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">?: </span><a href="interfaces/ToolbarMenuConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">ToolbarMenuConfig</a> <span class="tsd-signature-symbol">}</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:58</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-type-alias"><a id="VecType" class="tsd-anchor"></a><h3>Vec<wbr/>Type</h3><div class="tsd-signature tsd-kind-icon">Vec<wbr/>Type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">vec3</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/Curve.ts:36</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Reference to <a href="https://github.com/mrdoob/three.js/blob/dev/src/extras/core/Curve.js">https://github.com/mrdoob/three.js/blob/dev/src/extras/core/Curve.js</a>
Extensible curve object.</p>
</div><div><p>Some comments of curve methods:
.getPoint( t, optionalTarget ), .getTangent( t, optionalTarget )
.getPointAt( u, optionalTarget ), .getTangentAt( u, optionalTarget )
.getPoints(), .getSpacedPoints()
.getLength()
.updateArcLengths()</p>
<p>Following curves inherit from Curve:</p>
<p>-- 2D curves --
ArcCurve
CubicBezierCurve
EllipseCurve
LineCurve
QuadraticBezierCurve
SplineCurve</p>
<p>-- 3D curves --
CatmullRomCurve3
CubicBezierCurve3
LineCurve3
QuadraticBezierCurve3</p>
<p>A series of curves can represent as a CurvePath.</p>
</div></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Variables</h2><section class="tsd-panel tsd-member tsd-kind-variable"><a id="AXIS_SECTION_PLANE_CONTROL_ID" class="tsd-anchor"></a><h3>AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID</h3><div class="tsd-signature tsd-kind-icon">AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;axis-section-plane-control&quot;</span><span class="tsd-signature-symbol"> = &quot;axis-section-plane-control&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:13</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="AXIS_SECTION_PLANE_ID" class="tsd-anchor"></a><h3>AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>ID</h3><div class="tsd-signature tsd-kind-icon">AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>ID<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;axis-section-plane&quot;</span><span class="tsd-signature-symbol"> = &quot;axis-section-plane&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:12</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="DEFAULT_BACKGROUND_COLOR" class="tsd-anchor"></a><h3>DEFAULT_<wbr/>BACKGROUND_<wbr/>COLOR</h3><div class="tsd-signature tsd-kind-icon">DEFAULT_<wbr/>BACKGROUND_<wbr/>COLOR<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:213</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Default background color of viewer</p>
</div></div></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="DEFAULT_BIM_VIEWER_CONFIG" class="tsd-anchor"></a><h3>DEFAULT_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG</h3><div class="tsd-signature tsd-kind-icon">DEFAULT_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG<span class="tsd-signature-symbol">:</span> <a href="interfaces/BimViewerConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">BimViewerConfig</a><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:218</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>A default BimViewerConfig as a template, which enables most plugins.</p>
</div></div></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="DEFAULT_TOOLBAR_CONFIG" class="tsd-anchor"></a><h3>DEFAULT_<wbr/>TOOLBAR_<wbr/>CONFIG</h3><div class="tsd-signature tsd-kind-icon">DEFAULT_<wbr/>TOOLBAR_<wbr/>CONFIG<span class="tsd-signature-symbol">:</span> <a href="modules.html#ToolbarConfig" class="tsd-signature-type" data-tsd-kind="Type alias">ToolbarConfig</a><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:62</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="ENTER_KEY" class="tsd-anchor"></a><h3>ENTER_<wbr/>KEY</h3><div class="tsd-signature tsd-kind-icon">ENTER_<wbr/>KEY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;Enter&quot;</span><span class="tsd-signature-symbol"> = &quot;Enter&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:8</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="ESC_KEY" class="tsd-anchor"></a><h3>ESC_<wbr/>KEY</h3><div class="tsd-signature tsd-kind-icon">ESC_<wbr/>KEY<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;Escape&quot;</span><span class="tsd-signature-symbol"> = &quot;Escape&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:7</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="GROUP_CONFIG" class="tsd-anchor"></a><h3>GROUP_<wbr/>CONFIG</h3><div class="tsd-signature tsd-kind-icon">GROUP_<wbr/>CONFIG<span class="tsd-signature-symbol">:</span> <a href="enums/ToolbarMenuId.html" class="tsd-signature-type" data-tsd-kind="Enumeration">ToolbarMenuId</a><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in widgets/toolbar/ToolbarConfig.ts:159</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="ICON_FONT_CLASS" class="tsd-anchor"></a><h3>ICON_<wbr/>FONT_<wbr/>CLASS</h3><div class="tsd-signature tsd-kind-icon">ICON_<wbr/>FONT_<wbr/>CLASS<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;gemini-viewer-icon&quot;</span><span class="tsd-signature-symbol"> = &quot;gemini-viewer-icon&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:10</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="KEYDOWN_EVENT" class="tsd-anchor"></a><h3>KEYDOWN_<wbr/>EVENT</h3><div class="tsd-signature tsd-kind-icon">KEYDOWN_<wbr/>EVENT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;keydown&quot;</span><span class="tsd-signature-symbol"> = &quot;keydown&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:1</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="KEYUP_EVENT" class="tsd-anchor"></a><h3>KEYUP_<wbr/>EVENT</h3><div class="tsd-signature tsd-kind-icon">KEYUP_<wbr/>EVENT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;keyup&quot;</span><span class="tsd-signature-symbol"> = &quot;keyup&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:2</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="MOUSEDOWN_EVENT" class="tsd-anchor"></a><h3>MOUSEDOWN_<wbr/>EVENT</h3><div class="tsd-signature tsd-kind-icon">MOUSEDOWN_<wbr/>EVENT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;mousedown&quot;</span><span class="tsd-signature-symbol"> = &quot;mousedown&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:5</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="MOUSEMOVE_EVENT" class="tsd-anchor"></a><h3>MOUSEMOVE_<wbr/>EVENT</h3><div class="tsd-signature tsd-kind-icon">MOUSEMOVE_<wbr/>EVENT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;mousemove&quot;</span><span class="tsd-signature-symbol"> = &quot;mousemove&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:3</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="MOUSEUP_EVENT" class="tsd-anchor"></a><h3>MOUSEUP_<wbr/>EVENT</h3><div class="tsd-signature tsd-kind-icon">MOUSEUP_<wbr/>EVENT<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;mouseup&quot;</span><span class="tsd-signature-symbol"> = &quot;mouseup&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:4</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="SECTION_BOX_ID" class="tsd-anchor"></a><h3>SECTION_<wbr/>BOX_<wbr/>ID</h3><div class="tsd-signature tsd-kind-icon">SECTION_<wbr/>BOX_<wbr/>ID<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;section-box&quot;</span><span class="tsd-signature-symbol"> = &quot;section-box&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:16</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="SECTION_PLANE_CONTROL_ID" class="tsd-anchor"></a><h3>SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID</h3><div class="tsd-signature tsd-kind-icon">SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;section-plane-control&quot;</span><span class="tsd-signature-symbol"> = &quot;section-plane-control&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:15</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="SECTION_PLANE_ID" class="tsd-anchor"></a><h3>SECTION_<wbr/>PLANE_<wbr/>ID</h3><div class="tsd-signature tsd-kind-icon">SECTION_<wbr/>PLANE_<wbr/>ID<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">&quot;section-plane&quot;</span><span class="tsd-signature-symbol"> = &quot;section-plane&quot;</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Consts.ts:14</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="SIMPLE_BIM_VIEWER_CONFIG" class="tsd-anchor"></a><h3>SIMPLE_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG</h3><div class="tsd-signature tsd-kind-icon">SIMPLE_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG<span class="tsd-signature-symbol">:</span> <a href="interfaces/BimViewerConfig.html" class="tsd-signature-type" data-tsd-kind="Interface">BimViewerConfig</a><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in core/Configs.ts:241</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>A simple BimViewerConfig as a template, which disables most plugins.</p>
</div></div></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="cn" class="tsd-anchor"></a><h3>cn</h3><div class="tsd-signature tsd-kind-icon">cn<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>ContextMenu<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>deselect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>resetView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>select<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>selectNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>undoSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>NavCube<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>back<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bottom<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>front<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>left<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>right<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>top<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>PopPanel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>reset<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>Toolbar<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>annotation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>areaMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>axisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bimTree<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>clearMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>distanceMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>homeView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>orthoView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pickSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>settings<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewpoint<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>Tooltip<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>measure<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Locale.ts:61</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5>Context<wbr/>Menu<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>deselect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>resetView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>select<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>selectNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>undoSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>deselect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Axis<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Entity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Others<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Section<wbr/>Box<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>reset<wbr/>View<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>select<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>select<wbr/>None<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>Axis<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>Section<wbr/>Box<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>undo<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>view<wbr/>Fit<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>view<wbr/>Fit<wbr/>Entity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>Entity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>None<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>Others<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Nav<wbr/>Cube<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>back<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bottom<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>front<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>left<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>right<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>top<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>back<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>bottom<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>front<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>left<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>right<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>top<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Pop<wbr/>Panel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>reset<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>reset<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Toolbar<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>annotation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>areaMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>axisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bimTree<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>clearMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>distanceMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>homeView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>orthoView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pickSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>settings<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewpoint<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>annotation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>area<wbr/>Measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>axis<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>bim<wbr/>Tree<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>clear<wbr/>Measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>distance<wbr/>Measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>home<wbr/>View<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>ortho<wbr/>View<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>pick<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>section<wbr/>Box<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>settings<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>viewpoint<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Tooltip<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>measure<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>measure<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li></ul></div></section><section class="tsd-panel tsd-member tsd-kind-variable"><a id="en" class="tsd-anchor"></a><h3>en</h3><div class="tsd-signature tsd-kind-icon">en<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-symbol">{ </span>ContextMenu<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>deselect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>resetView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>select<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>selectNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>undoSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>NavCube<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>back<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bottom<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>front<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>left<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>right<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>top<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>PopPanel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>reset<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>Toolbar<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>annotation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>areaMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>axisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bimTree<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>clearMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>distanceMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>homeView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>orthoView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pickSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>settings<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewpoint<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol">; </span>Tooltip<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>measure<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> }</span><span class="tsd-signature-symbol"> = ...</span></div><aside class="tsd-sources"><ul><li>Defined in utils/Locale.ts:1</li></ul></aside><div class="tsd-type-declaration"><h4>Type declaration</h4><ul class="tsd-parameters"><li class="tsd-parameter"><h5>Context<wbr/>Menu<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>deselect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>hideSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>resetView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>select<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>selectNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showAxisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>showSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>undoSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewFitEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayAll<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayEntity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayNone<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>xRayOthers<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>deselect<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Axis<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Entity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Others<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Section<wbr/>Box<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>hide<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>reset<wbr/>View<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>select<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>select<wbr/>None<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>Axis<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>Section<wbr/>Box<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>show<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>undo<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>view<wbr/>Fit<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>view<wbr/>Fit<wbr/>Entity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>All<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>Entity<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>None<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>x<wbr/>Ray<wbr/>Others<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Nav<wbr/>Cube<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>back<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bottom<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>front<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>left<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>right<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>top<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>back<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>bottom<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>front<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>left<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>right<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>top<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Pop<wbr/>Panel<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>reset<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>reset<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Toolbar<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>annotation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>areaMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>axisSection<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>bimTree<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>clearMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>distanceMeasurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>homeView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>orthoView<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>pickSectionPlane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>sectionBox<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>settings<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>viewpoint<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>annotation<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>area<wbr/>Measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>axis<wbr/>Section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>bim<wbr/>Tree<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>clear<wbr/>Measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>distance<wbr/>Measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>fullscreen<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>home<wbr/>View<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>measurement<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>ortho<wbr/>View<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>pick<wbr/>Section<wbr/>Plane<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>property<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>section<wbr/>Box<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>settings<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>viewpoint<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li><li class="tsd-parameter"><h5>Tooltip<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">{ </span>measure<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">; </span>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> }</span></h5><ul class="tsd-parameters"><li class="tsd-parameter"><h5>measure<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li><li class="tsd-parameter"><h5>section<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></h5></li></ul></li></ul></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Functions</h2><section class="tsd-panel tsd-member tsd-kind-function"><a id="CatmullRom" class="tsd-anchor"></a><h3>Catmull<wbr/>Rom</h3><ul class="tsd-signatures tsd-kind-function"><li class="tsd-signature tsd-kind-icon">Catmull<wbr/>Rom<span class="tsd-signature-symbol">(</span>t<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p0<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p1<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p2<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p3<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Interpolations.ts:5</li></ul></aside><div class="tsd-comment tsd-typography"><div class="lead">
<p>Bezier Curves formulas obtained from <a href="http://en.wikipedia.org/wiki/B%C3%A9zier_curve">http://en.wikipedia.org/wiki/Bézier_curve</a></p>
</div></div><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>t: <span class="tsd-signature-type">number</span></h5></li><li><h5>p0: <span class="tsd-signature-type">number</span></h5></li><li><h5>p1: <span class="tsd-signature-type">number</span></h5></li><li><h5>p2: <span class="tsd-signature-type">number</span></h5></li><li><h5>p3: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-function"><a id="CubicBezier" class="tsd-anchor"></a><h3>Cubic<wbr/>Bezier</h3><ul class="tsd-signatures tsd-kind-function"><li class="tsd-signature tsd-kind-icon">Cubic<wbr/>Bezier<span class="tsd-signature-symbol">(</span>t<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p0<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p1<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p2<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p3<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Interpolations.ts:48</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>t: <span class="tsd-signature-type">number</span></h5></li><li><h5>p0: <span class="tsd-signature-type">number</span></h5></li><li><h5>p1: <span class="tsd-signature-type">number</span></h5></li><li><h5>p2: <span class="tsd-signature-type">number</span></h5></li><li><h5>p3: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-function"><a id="QuadraticBezier" class="tsd-anchor"></a><h3>Quadratic<wbr/>Bezier</h3><ul class="tsd-signatures tsd-kind-function"><li class="tsd-signature tsd-kind-icon">Quadratic<wbr/>Bezier<span class="tsd-signature-symbol">(</span>t<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p0<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p1<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, p2<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Interpolations.ts:26</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>t: <span class="tsd-signature-type">number</span></h5></li><li><h5>p0: <span class="tsd-signature-type">number</span></h5></li><li><h5>p1: <span class="tsd-signature-type">number</span></h5></li><li><h5>p2: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">number</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-function"><a id="addPrefix" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagConst">Const</span> add<wbr/>Prefix</h3><ul class="tsd-signatures tsd-kind-function"><li class="tsd-signature tsd-kind-icon">add<wbr/>Prefix<span class="tsd-signature-symbol">(</span>prefix<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span>, concat<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">(</span>str<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">string</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in utils/CommonUtils.ts:32</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>prefix: <span class="tsd-signature-type">string</span></h5></li><li><h5>concat: <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;-&quot;</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-symbol">(</span>str<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol"> =&gt; </span><span class="tsd-signature-type">string</span></h4><ul class="tsd-parameters"><li class="tsd-parameter-signature"><ul class="tsd-signatures tsd-kind-type-literal"><li class="tsd-signature tsd-kind-icon"><span class="tsd-signature-symbol">(</span>str<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">string</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>str: <span class="tsd-signature-type">string</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">string</span></h4></li></ul></li></ul></li></ul></section><section class="tsd-panel tsd-member tsd-kind-function"><a id="createCurveByType" class="tsd-anchor"></a><h3>create<wbr/>Curve<wbr/>By<wbr/>Type</h3><ul class="tsd-signatures tsd-kind-function"><li class="tsd-signature tsd-kind-icon">create<wbr/>Curve<wbr/>By<wbr/>Type<span class="tsd-signature-symbol">(</span>type<span class="tsd-signature-symbol">: </span><a href="enums/CurveType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">CurveType</a><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="classes/Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">undefined</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/Curves.ts:25</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>type: <a href="enums/CurveType.html" class="tsd-signature-type" data-tsd-kind="Enumeration">CurveType</a></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="classes/Curve.html" class="tsd-signature-type" data-tsd-kind="Class">Curve</a><span class="tsd-signature-symbol">&lt;</span><a href="modules.html#VecType" class="tsd-signature-type" data-tsd-kind="Type alias">VecType</a><span class="tsd-signature-symbol">&gt;</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">undefined</span></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-function"><a id="showContextMenu" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagConst">Const</span> show<wbr/>Context<wbr/>Menu</h3><ul class="tsd-signatures tsd-kind-function"><li class="tsd-signature tsd-kind-icon">show<wbr/>Context<wbr/>Menu<span class="tsd-signature-symbol">(</span>contextMenu<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, hit<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">any</span>, canvasPos<span class="tsd-signature-symbol">: </span><span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">void</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in utils/ContextMenuUtils.ts:280</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>contextMenu: <span class="tsd-signature-type">any</span></h5></li><li><h5>hit: <span class="tsd-signature-type">any</span></h5></li><li><h5>canvasPos: <span class="tsd-signature-symbol">[</span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">, </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <span class="tsd-signature-type">void</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class="current"><a href="modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="tsd-kind-enum"><a href="enums/AxisType.html" class="tsd-kind-icon">Axis<wbr/>Type</a></li><li class="tsd-kind-enum"><a href="enums/BoxSectionPlaneType.html" class="tsd-kind-icon">Box<wbr/>Section<wbr/>Plane<wbr/>Type</a></li><li class="tsd-kind-enum"><a href="enums/CurveType.html" class="tsd-kind-icon">Curve<wbr/>Type</a></li><li class="tsd-kind-enum"><a href="enums/ToolbarMenuId.html" class="tsd-kind-icon">Toolbar<wbr/>Menu<wbr/>Id</a></li><li class="tsd-kind-class"><a href="classes/AxisSectionPlaneController.html" class="tsd-kind-icon">Axis<wbr/>Section<wbr/>Plane<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/AxisSectionPlanePlugin.html" class="tsd-kind-icon">Axis<wbr/>Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/BackgroundColorPlugin.html" class="tsd-kind-icon">Background<wbr/>Color<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/BimTreeController.html" class="tsd-kind-icon">Bim<wbr/>Tree<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/BimViewer.html" class="tsd-kind-icon">Bim<wbr/>Viewer</a></li><li class="tsd-kind-class"><a href="classes/BottomBar.html" class="tsd-kind-icon">Bottom<wbr/>Bar</a></li><li class="tsd-kind-class"><a href="classes/BoxControl.html" class="tsd-kind-icon">Box<wbr/>Control</a></li><li class="tsd-kind-class"><a href="classes/CommonUtils.html" class="tsd-kind-icon">Common<wbr/>Utils</a></li><li class="tsd-kind-class"><a href="classes/ComponentPropertyPlugin.html" class="tsd-kind-icon">Component<wbr/>Property<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/Controller.html" class="tsd-kind-icon">Controller</a></li><li class="tsd-kind-class"><a href="classes/CubicBezierCurve.html" class="tsd-kind-icon">Cubic<wbr/>Bezier<wbr/>Curve</a></li><li class="tsd-kind-class tsd-has-type-parameter"><a href="classes/Curve.html" class="tsd-kind-icon">Curve</a></li><li class="tsd-kind-class"><a href="classes/CurvePath.html" class="tsd-kind-icon">Curve<wbr/>Path</a></li><li class="tsd-kind-class"><a href="classes/CustomizedGLTFLoaderPlugin.html" class="tsd-kind-icon">CustomizedGLTFLoader<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/DxfLoaderPlugin.html" class="tsd-kind-icon">Dxf<wbr/>Loader<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/DxfPerformanceModelLoader.html" class="tsd-kind-icon">Dxf<wbr/>Performance<wbr/>Model<wbr/>Loader</a></li><li class="tsd-kind-class"><a href="classes/EllipseCurve.html" class="tsd-kind-icon">Ellipse<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/EnhancedDistanceMeasurementPlugin.html" class="tsd-kind-icon">Enhanced<wbr/>Distance<wbr/>Measurement<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/FontManager.html" class="tsd-kind-icon">Font<wbr/>Manager</a></li><li class="tsd-kind-class"><a href="classes/FullScreenController.html" class="tsd-kind-icon">Full<wbr/>Screen<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/FullScreenPlugin.html" class="tsd-kind-icon">Full<wbr/>Screen<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/GeometryUtils.html" class="tsd-kind-icon">Geometry<wbr/>Utils</a></li><li class="tsd-kind-class"><a href="classes/GridPlugin.html" class="tsd-kind-icon">Grid<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/HomeViewController.html" class="tsd-kind-icon">Home<wbr/>View<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/KeyBoardRotatePlugin.html" class="tsd-kind-icon">Key<wbr/>Board<wbr/>Rotate<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/LineCurve.html" class="tsd-kind-icon">Line<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/Map.html" class="tsd-kind-icon">Map</a></li><li class="tsd-kind-class"><a href="classes/MathUtil.html" class="tsd-kind-icon">Math<wbr/>Util</a></li><li class="tsd-kind-class"><a href="classes/MeasureAreaController.html" class="tsd-kind-icon">Measure<wbr/>Area<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/MeasureClearController.html" class="tsd-kind-icon">Measure<wbr/>Clear<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/MeasureController.html" class="tsd-kind-icon">Measure<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/MeasureDistanceController.html" class="tsd-kind-icon">Measure<wbr/>Distance<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/NavControlConfig.html" class="tsd-kind-icon">Nav<wbr/>Control<wbr/>Config</a></li><li class="tsd-kind-class"><a href="classes/ObjectUtil.html" class="tsd-kind-icon">Object<wbr/>Util</a></li><li class="tsd-kind-class"><a href="classes/OrthoModeController.html" class="tsd-kind-icon">Ortho<wbr/>Mode<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/OrthoModePlugin.html" class="tsd-kind-icon">Ortho<wbr/>Mode<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/Path.html" class="tsd-kind-icon">Path</a></li><li class="tsd-kind-class"><a href="classes/PlanViewPlugin.html" class="tsd-kind-icon">Plan<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/PlaneControl.html" class="tsd-kind-icon">Plane<wbr/>Control</a></li><li class="tsd-kind-class"><a href="classes/PopPanel.html" class="tsd-kind-icon">Pop<wbr/>Panel</a></li><li class="tsd-kind-class"><a href="classes/PropertyController.html" class="tsd-kind-icon">Property<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/QuadraticBezierCurve.html" class="tsd-kind-icon">Quadratic<wbr/>Bezier<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/SceneGraphTreeView.html" class="tsd-kind-icon">Scene<wbr/>Graph<wbr/>Tree<wbr/>View</a></li><li class="tsd-kind-class"><a href="classes/SceneGraphTreeViewPlugin.html" class="tsd-kind-icon">Scene<wbr/>Graph<wbr/>Tree<wbr/>View<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionBoxController.html" class="tsd-kind-icon">Section<wbr/>Box<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/SectionBoxPlugin.html" class="tsd-kind-icon">Section<wbr/>Box<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionController.html" class="tsd-kind-icon">Section<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/SectionCullPlanePlugin.html" class="tsd-kind-icon">Section<wbr/>Cull<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionPlaneController.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/SectionPlanePlugin.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SectionPlanePopPanel.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel</a></li><li class="tsd-kind-class"><a href="classes/Shape.html" class="tsd-kind-icon">Shape</a></li><li class="tsd-kind-class"><a href="classes/ShapePath.html" class="tsd-kind-icon">Shape<wbr/>Path</a></li><li class="tsd-kind-class"><a href="classes/ShapeUtils.html" class="tsd-kind-icon">Shape<wbr/>Utils</a></li><li class="tsd-kind-class"><a href="classes/SingleSelectionPlugin.html" class="tsd-kind-icon">Single<wbr/>Selection<wbr/>Plugin</a></li><li class="tsd-kind-class"><a href="classes/SplineCurve.html" class="tsd-kind-icon">Spline<wbr/>Curve</a></li><li class="tsd-kind-class"><a href="classes/TextGeometry.html" class="tsd-kind-icon">Text<wbr/>Geometry</a></li><li class="tsd-kind-class"><a href="classes/Toolbar.html" class="tsd-kind-icon">Toolbar</a></li><li class="tsd-kind-class"><a href="classes/ToolbarMenuBaseController.html" class="tsd-kind-icon">Toolbar<wbr/>Menu<wbr/>Base<wbr/>Controller</a></li><li class="tsd-kind-class"><a href="classes/Tooltip.html" class="tsd-kind-icon">Tooltip</a></li><li class="tsd-kind-class"><a href="classes/ZoomToExtent.html" class="tsd-kind-icon">Zoom<wbr/>To<wbr/>Extent</a></li><li class="tsd-kind-interface"><a href="interfaces/BackgroundColorConfig.html" class="tsd-kind-icon">Background<wbr/>Color<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BimViewerConfig.html" class="tsd-kind-icon">Bim<wbr/>Viewer<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BuildEllipseGeometryConfig.html" class="tsd-kind-icon">Build<wbr/>Ellipse<wbr/>Geometry<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BuildPlaneGeometryConfig.html" class="tsd-kind-icon">Build<wbr/>Plane<wbr/>Geometry<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/BuildPlanePositionConfig.html" class="tsd-kind-icon">Build<wbr/>Plane<wbr/>Position<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/CameraConfig.html" class="tsd-kind-icon">Camera<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/ComponentPropertyConfig.html" class="tsd-kind-icon">Component<wbr/>Property<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/Context.html" class="tsd-kind-icon">Context</a></li><li class="tsd-kind-interface"><a href="interfaces/ContextMenuConfig.html" class="tsd-kind-icon">Context<wbr/>Menu<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/CustomizedGLTFLoaderConfig.html" class="tsd-kind-icon">CustomizedGLTFLoader<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/GridConfig.html" class="tsd-kind-icon">Grid<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/GridGeometryConfig.html" class="tsd-kind-icon">Grid<wbr/>Geometry<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/GridMeshConfig.html" class="tsd-kind-icon">Grid<wbr/>Mesh<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/IconClass.html" class="tsd-kind-icon">Icon<wbr/>Class</a></li><li class="tsd-kind-interface"><a href="interfaces/ModelConfig.html" class="tsd-kind-icon">Model<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/OrthoModeConfig.html" class="tsd-kind-icon">Ortho<wbr/>Mode<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionCullPlaneConfig.html" class="tsd-kind-icon">Section<wbr/>Cull<wbr/>Plane<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionOverviewConfig.html" class="tsd-kind-icon">Section<wbr/>Overview<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionPlanePopPanelConfig.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SectionPlanePopPanelItemConfig.html" class="tsd-kind-icon">Section<wbr/>Plane<wbr/>Pop<wbr/>Panel<wbr/>Item<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/SingleSelectionConfig.html" class="tsd-kind-icon">Single<wbr/>Selection<wbr/>Config</a></li><li class="tsd-kind-interface"><a href="interfaces/TextGeometryParameter.html" class="tsd-kind-icon">Text<wbr/>Geometry<wbr/>Parameter</a></li><li class="tsd-kind-interface"><a href="interfaces/ToolbarMenuConfig.html" class="tsd-kind-icon">Toolbar<wbr/>Menu<wbr/>Config</a></li><li class="tsd-kind-type-alias tsd-has-type-parameter"><a href="modules.html#CallbackRetureVoidType" class="tsd-kind-icon">Callback<wbr/>Reture<wbr/>Void<wbr/>Type</a></li><li class="tsd-kind-type-alias tsd-has-type-parameter"><a href="modules.html#CallbackType" class="tsd-kind-icon">Callback<wbr/>Type</a></li><li class="tsd-kind-type-alias"><a href="modules.html#EmptyCallbackType" class="tsd-kind-icon">Empty<wbr/>Callback<wbr/>Type</a></li><li class="tsd-kind-type-alias"><a href="modules.html#NoParamCallback" class="tsd-kind-icon">No<wbr/>Param<wbr/>Callback</a></li><li class="tsd-kind-type-alias"><a href="modules.html#ToolbarConfig" class="tsd-kind-icon">Toolbar<wbr/>Config</a></li><li class="tsd-kind-type-alias"><a href="modules.html#VecType" class="tsd-kind-icon">Vec<wbr/>Type</a></li><li class="tsd-kind-variable"><a href="modules.html#AXIS_SECTION_PLANE_CONTROL_ID" class="tsd-kind-icon">AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#AXIS_SECTION_PLANE_ID" class="tsd-kind-icon">AXIS_<wbr/>SECTION_<wbr/>PLANE_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#DEFAULT_BACKGROUND_COLOR" class="tsd-kind-icon">DEFAULT_<wbr/>BACKGROUND_<wbr/>COLOR</a></li><li class="tsd-kind-variable"><a href="modules.html#DEFAULT_BIM_VIEWER_CONFIG" class="tsd-kind-icon">DEFAULT_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#DEFAULT_TOOLBAR_CONFIG" class="tsd-kind-icon">DEFAULT_<wbr/>TOOLBAR_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#ENTER_KEY" class="tsd-kind-icon">ENTER_<wbr/>KEY</a></li><li class="tsd-kind-variable"><a href="modules.html#ESC_KEY" class="tsd-kind-icon">ESC_<wbr/>KEY</a></li><li class="tsd-kind-variable"><a href="modules.html#GROUP_CONFIG" class="tsd-kind-icon">GROUP_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#ICON_FONT_CLASS" class="tsd-kind-icon">ICON_<wbr/>FONT_<wbr/>CLASS</a></li><li class="tsd-kind-variable"><a href="modules.html#KEYDOWN_EVENT" class="tsd-kind-icon">KEYDOWN_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#KEYUP_EVENT" class="tsd-kind-icon">KEYUP_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#MOUSEDOWN_EVENT" class="tsd-kind-icon">MOUSEDOWN_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#MOUSEMOVE_EVENT" class="tsd-kind-icon">MOUSEMOVE_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#MOUSEUP_EVENT" class="tsd-kind-icon">MOUSEUP_<wbr/>EVENT</a></li><li class="tsd-kind-variable"><a href="modules.html#SECTION_BOX_ID" class="tsd-kind-icon">SECTION_<wbr/>BOX_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#SECTION_PLANE_CONTROL_ID" class="tsd-kind-icon">SECTION_<wbr/>PLANE_<wbr/>CONTROL_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#SECTION_PLANE_ID" class="tsd-kind-icon">SECTION_<wbr/>PLANE_<wbr/>ID</a></li><li class="tsd-kind-variable"><a href="modules.html#SIMPLE_BIM_VIEWER_CONFIG" class="tsd-kind-icon">SIMPLE_<wbr/>BIM_<wbr/>VIEWER_<wbr/>CONFIG</a></li><li class="tsd-kind-variable"><a href="modules.html#cn" class="tsd-kind-icon">cn</a></li><li class="tsd-kind-variable"><a href="modules.html#en" class="tsd-kind-icon">en</a></li><li class="tsd-kind-function"><a href="modules.html#CatmullRom" class="tsd-kind-icon">Catmull<wbr/>Rom</a></li><li class="tsd-kind-function"><a href="modules.html#CubicBezier" class="tsd-kind-icon">Cubic<wbr/>Bezier</a></li><li class="tsd-kind-function"><a href="modules.html#QuadraticBezier" class="tsd-kind-icon">Quadratic<wbr/>Bezier</a></li><li class="tsd-kind-function"><a href="modules.html#addPrefix" class="tsd-kind-icon">add<wbr/>Prefix</a></li><li class="tsd-kind-function"><a href="modules.html#createCurveByType" class="tsd-kind-icon">create<wbr/>Curve<wbr/>By<wbr/>Type</a></li><li class="tsd-kind-function"><a href="modules.html#showContextMenu" class="tsd-kind-icon">show<wbr/>Context<wbr/>Menu</a></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="assets/main.js"></script></body></html>