<!DOCTYPE html><html class="default no-js"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>ShapePath | @pattern-x/gemini-viewer</title><meta name="description" content="Documentation for @pattern-x/gemini-viewer"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script async src="../assets/search.js" id="search-script"></script></head><body><script>document.body.classList.add(localStorage.getItem("tsd-theme") || "os")</script><header><div class="tsd-page-toolbar"><div class="container"><div class="table-wrap"><div class="table-cell" id="tsd-search" data-base=".."><div class="field"><label for="tsd-search-field" class="tsd-widget search no-caption">Search</label><input type="text" id="tsd-search-field"/></div><ul class="results"><li class="state loading">Preparing search index...</li><li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">@pattern-x/gemini-viewer</a></div><div class="table-cell" id="tsd-widgets"><div id="tsd-filter"><a href="#" class="tsd-widget options no-caption" data-toggle="options">Options</a><div class="tsd-filter-group"><div class="tsd-select" id="tsd-filter-visibility"><span class="tsd-select-label">All</span><ul class="tsd-select-list"><li data-value="public">Public</li><li data-value="protected">Public/Protected</li><li data-value="private" class="selected">All</li></ul></div> <input type="checkbox" id="tsd-filter-inherited" checked/><label class="tsd-widget" for="tsd-filter-inherited">Inherited</label><input type="checkbox" id="tsd-filter-externals" checked/><label class="tsd-widget" for="tsd-filter-externals">Externals</label></div></div><a href="#" class="tsd-widget menu no-caption" data-toggle="menu">Menu</a></div></div></div></div><div class="tsd-page-title"><div class="container"><ul class="tsd-breadcrumb"><li><a href="../modules.html">@pattern-x/gemini-viewer</a></li><li><a href="ShapePath.html">ShapePath</a></li></ul><h1>Class ShapePath</h1></div></div></header><div class="container container-main"><div class="row"><div class="col-8 col-content"><section class="tsd-panel tsd-hierarchy"><h3>Hierarchy</h3><ul class="tsd-hierarchy"><li><span class="target">ShapePath</span></li></ul></section><section class="tsd-panel-group tsd-index-group"><h2>Index</h2><section class="tsd-panel tsd-index-panel"><div class="tsd-index-content"><section class="tsd-index-section "><h3>Constructors</h3><ul class="tsd-index-list"><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="ShapePath.html#constructor" class="tsd-kind-icon">constructor</a></li></ul></section><section class="tsd-index-section tsd-is-private-protected"><h3>Properties</h3><ul class="tsd-index-list"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="ShapePath.html#_currentPath" class="tsd-kind-icon">_current<wbr/>Path</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="ShapePath.html#_subPaths" class="tsd-kind-icon">_sub<wbr/>Paths</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="ShapePath.html#_type" class="tsd-kind-icon">_type</a></li></ul></section><section class="tsd-index-section "><h3>Methods</h3><ul class="tsd-index-list"><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#bezierCurveTo" class="tsd-kind-icon">bezier<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#lineTo" class="tsd-kind-icon">line<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#moveTo" class="tsd-kind-icon">move<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#quadraticCurveTo" class="tsd-kind-icon">quadratic<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#splineThru" class="tsd-kind-icon">spline<wbr/>Thru</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#toShapes" class="tsd-kind-icon">to<wbr/>Shapes</a></li></ul></section></div></section></section><section class="tsd-panel-group tsd-member-group "><h2>Constructors</h2><section class="tsd-panel tsd-member tsd-kind-constructor tsd-parent-kind-class"><a id="constructor" class="tsd-anchor"></a><h3>constructor</h3><ul class="tsd-signatures tsd-kind-constructor tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">new <wbr/>Shape<wbr/>Path<span class="tsd-signature-symbol">(</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:20</li></ul></aside><h4 class="tsd-returns-title">Returns <a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></h4></li></ul></section></section><section class="tsd-panel-group tsd-member-group tsd-is-private-protected"><h2>Properties</h2><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a id="_currentPath" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> <span class="tsd-flag ts-flagOptional">Optional</span> _current<wbr/>Path</h3><div class="tsd-signature tsd-kind-icon">_current<wbr/>Path<span class="tsd-signature-symbol">?:</span> <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a></div><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:18</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a id="_subPaths" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _sub<wbr/>Paths</h3><div class="tsd-signature tsd-kind-icon">_sub<wbr/>Paths<span class="tsd-signature-symbol">:</span> <a href="Path.html" class="tsd-signature-type" data-tsd-kind="Class">Path</a><span class="tsd-signature-symbol">[]</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:17</li></ul></aside></section><section class="tsd-panel tsd-member tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a id="_type" class="tsd-anchor"></a><h3><span class="tsd-flag ts-flagProtected">Protected</span> _type</h3><div class="tsd-signature tsd-kind-icon">_type<span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:16</li></ul></aside></section></section><section class="tsd-panel-group tsd-member-group "><h2>Methods</h2><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="bezierCurveTo" class="tsd-anchor"></a><h3>bezier<wbr/>Curve<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">bezier<wbr/>Curve<wbr/>To<span class="tsd-signature-symbol">(</span>aCP1x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCP1y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCP2x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCP2y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:52</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aCP1x: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCP1y: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCP2x: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCP2y: <span class="tsd-signature-type">number</span></h5></li><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="lineTo" class="tsd-anchor"></a><h3>line<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">line<wbr/>To<span class="tsd-signature-symbol">(</span>x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:36</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>x: <span class="tsd-signature-type">number</span></h5></li><li><h5>y: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="moveTo" class="tsd-anchor"></a><h3>move<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">move<wbr/>To<span class="tsd-signature-symbol">(</span>x<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, y<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:28</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>x: <span class="tsd-signature-type">number</span></h5></li><li><h5>y: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="quadraticCurveTo" class="tsd-anchor"></a><h3>quadratic<wbr/>Curve<wbr/>To</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">quadratic<wbr/>Curve<wbr/>To<span class="tsd-signature-symbol">(</span>aCPx<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aCPy<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aX<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span>, aY<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:44</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>aCPx: <span class="tsd-signature-type">number</span></h5></li><li><h5>aCPy: <span class="tsd-signature-type">number</span></h5></li><li><h5>aX: <span class="tsd-signature-type">number</span></h5></li><li><h5>aY: <span class="tsd-signature-type">number</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="splineThru" class="tsd-anchor"></a><h3>spline<wbr/>Thru</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">spline<wbr/>Thru<span class="tsd-signature-symbol">(</span>pts<span class="tsd-signature-symbol">: </span><span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:60</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5>pts: <span class="tsd-signature-type">vec2</span><span class="tsd-signature-symbol">[]</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="ShapePath.html" class="tsd-signature-type" data-tsd-kind="Class">ShapePath</a></h4></li></ul></section><section class="tsd-panel tsd-member tsd-kind-method tsd-parent-kind-class"><a id="toShapes" class="tsd-anchor"></a><h3>to<wbr/>Shapes</h3><ul class="tsd-signatures tsd-kind-method tsd-parent-kind-class"><li class="tsd-signature tsd-kind-icon">to<wbr/>Shapes<span class="tsd-signature-symbol">(</span>isCCW<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span>, noHoles<span class="tsd-signature-symbol">?: </span><span class="tsd-signature-type">boolean</span><span class="tsd-signature-symbol">)</span><span class="tsd-signature-symbol">: </span><a href="Shape.html" class="tsd-signature-type" data-tsd-kind="Class">Shape</a><span class="tsd-signature-symbol">[]</span></li></ul><ul class="tsd-descriptions"><li class="tsd-description"><aside class="tsd-sources"><ul><li>Defined in core/paths/ShapePath.ts:68</li></ul></aside><h4 class="tsd-parameters-title">Parameters</h4><ul class="tsd-parameters"><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> isCCW: <span class="tsd-signature-type">boolean</span></h5></li><li><h5><span class="tsd-flag ts-flagOptional">Optional</span> noHoles: <span class="tsd-signature-type">boolean</span></h5></li></ul><h4 class="tsd-returns-title">Returns <a href="Shape.html" class="tsd-signature-type" data-tsd-kind="Class">Shape</a><span class="tsd-signature-symbol">[]</span></h4></li></ul></section></section></div><div class="col-4 col-menu menu-sticky-wrap menu-highlight"><nav class="tsd-navigation primary"><ul><li class=""><a href="../modules.html">Exports</a></li><li class=" tsd-kind-namespace"><a href="../modules/math.html">math</a></li></ul></nav><nav class="tsd-navigation secondary menu-sticky"><ul><li class="current tsd-kind-class"><a href="ShapePath.html" class="tsd-kind-icon">Shape<wbr/>Path</a><ul><li class="tsd-kind-constructor tsd-parent-kind-class"><a href="ShapePath.html#constructor" class="tsd-kind-icon">constructor</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="ShapePath.html#_currentPath" class="tsd-kind-icon">_current<wbr/>Path</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="ShapePath.html#_subPaths" class="tsd-kind-icon">_sub<wbr/>Paths</a></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><a href="ShapePath.html#_type" class="tsd-kind-icon">_type</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#bezierCurveTo" class="tsd-kind-icon">bezier<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#lineTo" class="tsd-kind-icon">line<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#moveTo" class="tsd-kind-icon">move<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#quadraticCurveTo" class="tsd-kind-icon">quadratic<wbr/>Curve<wbr/>To</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#splineThru" class="tsd-kind-icon">spline<wbr/>Thru</a></li><li class="tsd-kind-method tsd-parent-kind-class"><a href="ShapePath.html#toShapes" class="tsd-kind-icon">to<wbr/>Shapes</a></li></ul></li></ul></nav></div></div></div><footer class="with-border-bottom"><div class="container"><h2>Legend</h2><div class="tsd-legend-group"><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class"><span class="tsd-kind-icon">Constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-class"><span class="tsd-kind-icon">Method</span></li><li class="tsd-kind-accessor tsd-parent-kind-class"><span class="tsd-kind-icon">Accessor</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-constructor tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited constructor</span></li><li class="tsd-kind-property tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-inherited"><span class="tsd-kind-icon">Inherited method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-interface"><span class="tsd-kind-icon">Property</span></li><li class="tsd-kind-method tsd-parent-kind-interface"><span class="tsd-kind-icon">Method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-protected"><span class="tsd-kind-icon">Protected method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-property tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private property</span></li><li class="tsd-kind-method tsd-parent-kind-class tsd-is-private"><span class="tsd-kind-icon">Private method</span></li></ul><ul class="tsd-legend"><li class="tsd-kind-method tsd-parent-kind-class tsd-is-static"><span class="tsd-kind-icon">Static method</span></li></ul></div><h2>Settings</h2><p>Theme <select id="theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></p></div></footer><div class="container tsd-generator"><p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div><div class="overlay"></div><script src="../assets/main.js"></script></body></html>